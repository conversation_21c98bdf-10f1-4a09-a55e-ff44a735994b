'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  role: string
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  })
  const router = useRouter()

  // Fetch the current user
  const fetchUser = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      
      const response = await fetch('/api/auth/verify')
      
      if (!response.ok) {
        if (response.status === 401) {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: 'Not authenticated',
          })
          return
        }
        
        throw new Error('Failed to fetch user')
      }
      
      const data = await response.json()
      
      setAuthState({
        user: data.user,
        isLoading: false,
        isAuthenticated: data.authenticated,
        error: null,
      })
    } catch (error) {
      console.error('Error fetching user:', error)
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: 'Failed to fetch user',
      })
    }
  }, [])

  // Sign out
  const signOut = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to sign out')
      }
      
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      })
      
      router.push('/login')
      router.refresh()
    } catch (error) {
      console.error('Error signing out:', error)
      setAuthState(prev => ({
        ...prev,
        error: 'Failed to sign out',
      }))
    }
  }, [router])

  // Check if user has a specific role
  const hasRole = useCallback((role: string | string[]) => {
    if (!authState.user) return false
    
    if (Array.isArray(role)) {
      return role.includes(authState.user.role)
    }
    
    return authState.user.role === role
  }, [authState.user])

  // Fetch user on mount
  useEffect(() => {
    fetchUser()
  }, [fetchUser])

  return {
    user: authState.user,
    isLoading: authState.isLoading,
    isAuthenticated: authState.isAuthenticated,
    error: authState.error,
    signOut,
    fetchUser,
    hasRole,
  }
}
