"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"

interface PlaylistItemProps {
  id: number
  songTitle: string
  artistName: string
  type: string
  submissionTime: string
}

export function ExampleCardWithBadge({ item }: { item: PlaylistItemProps }) {
  const [expanded, setExpanded] = useState(false)

  return (
    <div className="admin-card relative p-4 border border-gray-700 rounded-md bg-black text-white">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{item.songTitle}</h3>
          <p className="text-sm text-gray-500">{item.artistName}</p>
        </div>
        <div className="flex flex-col items-end">
          <Badge variant="default">{item.type}</Badge>
          <div className="flex items-center mt-1">
            <span className="text-xs text-gray-500 mr-2">{item.submissionTime}</span>
            <Button
              size="icon"
              variant="ghost"
              onClick={() => setExpanded(!expanded)}
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 h-6 w-6 p-0"
            >
              {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {expanded && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <p className="text-sm text-gray-400">Additional content that appears when expanded...</p>
        </div>
      )}
    </div>
  )
}

// Example usage
export default function ExamplePage() {
  const exampleItem = {
    id: 1,
    songTitle: "Midnight Groove",
    artistName: "DJ Harmony",
    type: "VIP",
    submissionTime: "10:15 AM",
  }

  return (
    <div className="p-4 max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-4">Example Card with Badge</h2>
      <ExampleCardWithBadge item={exampleItem} />
      <div className="mt-8">
        <h3 className="font-medium mb-2">Visual Explanation:</h3>
        <ul className="list-disc pl-5 space-y-2 text-sm">
          <li>
            The badge is now positioned in the <strong>upper right corner</strong>
          </li>
          <li>
            The badge is styled as <strong>regular text</strong> (not a pill)
          </li>
          <li>
            The <strong>submission time</strong> is displayed below the badge
          </li>
          <li>
            An <strong>expand/collapse arrow</strong> is next to the time
          </li>
          <li>Clicking the arrow toggles the expanded content</li>
        </ul>
      </div>
    </div>
  )
}
