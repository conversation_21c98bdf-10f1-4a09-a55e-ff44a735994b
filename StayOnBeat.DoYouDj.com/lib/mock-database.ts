/**
 * Mock Database Implementation for Testing
 * Simulates Supabase operations without requiring a real database connection
 */

interface MockSubmission {
  id: string
  url: string
  media_url: string
  submission_type: string
  platform: string
  platform_id: string
  content_type: string
  status: string
  processing_status: string
  metadata_extracted: boolean
  user_id?: string
  user_email?: string
  notes?: string
  artist_name: string
  song_title: string
  duration?: number
  artwork_url?: string
  album_name?: string
  genre?: string
  is_explicit?: boolean
  stream_url?: string
  created_at: string
  updated_at: string
}

// In-memory storage for testing
const mockSubmissions: Map<string, MockSubmission> = new Map()

export const mockDatabase = {
  submissions: {
    insert: async (data: Partial<MockSubmission>) => {
      const id = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const submission: MockSubmission = {
        id,
        url: data.url || '',
        media_url: data.media_url || data.url || '',
        submission_type: data.submission_type || 'Free',
        platform: data.platform || 'unknown',
        platform_id: data.platform_id || 'unknown',
        content_type: data.content_type || 'track',
        status: data.status || 'pending',
        processing_status: data.processing_status || 'pending',
        metadata_extracted: data.metadata_extracted || false,
        user_id: data.user_id,
        user_email: data.user_email,
        notes: data.notes,
        artist_name: data.artist_name || 'Processing...',
        song_title: data.song_title || 'Extracting metadata...',
        duration: data.duration,
        artwork_url: data.artwork_url,
        album_name: data.album_name,
        genre: data.genre,
        is_explicit: data.is_explicit,
        stream_url: data.stream_url,
        created_at: data.created_at || new Date().toISOString(),
        updated_at: data.updated_at || new Date().toISOString()
      }
      
      mockSubmissions.set(id, submission)
      
      console.log('🗄️ MOCK DB: Inserted submission', { id, platform: submission.platform })
      
      return {
        data: { id },
        error: null
      }
    },
    
    update: async (id: string, updates: Partial<MockSubmission>) => {
      const existing = mockSubmissions.get(id)
      if (!existing) {
        return {
          data: null,
          error: { message: 'Submission not found' }
        }
      }
      
      const updated = {
        ...existing,
        ...updates,
        updated_at: new Date().toISOString()
      }
      
      mockSubmissions.set(id, updated)
      
      console.log('🗄️ MOCK DB: Updated submission', { id, updates: Object.keys(updates) })
      
      return {
        data: updated,
        error: null
      }
    },
    
    select: async (id: string) => {
      const submission = mockSubmissions.get(id)
      if (!submission) {
        return {
          data: null,
          error: { message: 'Submission not found' }
        }
      }
      
      console.log('🗄️ MOCK DB: Selected submission', { id })
      
      return {
        data: submission,
        error: null
      }
    },
    
    list: async () => {
      const submissions = Array.from(mockSubmissions.values())
      
      console.log('🗄️ MOCK DB: Listed submissions', { count: submissions.length })
      
      return {
        data: submissions,
        error: null
      }
    }
  }
}

// Mock Supabase client that uses the mock database
export const createMockSupabaseClient = () => {
  return {
    from: (table: string) => {
      if (table === 'submissions') {
        return {
          insert: (data: any) => ({
            select: (fields: string) => ({
              single: async () => {
                const result = await mockDatabase.submissions.insert(data)
                return result
              }
            })
          }),
          
          update: (data: any) => ({
            eq: (field: string, value: string) => ({
              then: async () => {
                const result = await mockDatabase.submissions.update(value, data)
                return result
              }
            })
          }),
          
          select: (fields: string) => ({
            eq: (field: string, value: string) => ({
              single: async () => {
                const result = await mockDatabase.submissions.select(value)
                return result
              }
            })
          })
        }
      }
      
      // Return empty operations for other tables
      return {
        insert: () => ({ select: () => ({ single: async () => ({ data: null, error: null }) }) }),
        update: () => ({ eq: () => ({ then: async () => ({ data: null, error: null }) }) }),
        select: () => ({ eq: () => ({ single: async () => ({ data: null, error: null }) }) })
      }
    },
    
    auth: {
      getSession: async () => ({ data: { session: null } }),
      signOut: async () => ({ error: null }),
      signInWithOAuth: async () => ({ data: null, error: null }),
      signInWithPassword: async () => ({ data: { user: null, session: null }, error: null }),
      signUp: async () => ({ data: { user: null, session: null }, error: null })
    }
  }
}

// Export mock submissions for inspection
export const getMockSubmissions = () => Array.from(mockSubmissions.values())
export const clearMockSubmissions = () => mockSubmissions.clear()
