import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

// Create a Supabase client with Clerk authentication
export async function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${await auth().getToken({ template: 'supabase' })}`
        }
      }
    }
  )
}

// Client-side Supabase client (to be used in client components)
export function createClientSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
