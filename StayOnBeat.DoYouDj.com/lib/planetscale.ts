/**
 * PlanetScale Database Client for StayOnBeat
 * Replaces Supabase with PlanetScale MySQL database
 */

import { connect } from '@planetscale/database'

// PlanetScale connection configuration
const config = {
  url: process.env.DATABASE_URL || process.env.PLANETSCALE_DATABASE_URL,
}

// Create PlanetScale connection
export const planetscale = connect(config)

// Database client that mimics Supabase API for easy migration
export const planetscaleClient = {
  from: (table: string) => ({
    select: (columns: string = '*') => ({
      order: (column: string, options?: { ascending?: boolean }) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const orderDirection = options?.ascending === false ? 'DESC' : 'ASC'
            const query = `SELECT ${columns} FROM ${table} ORDER BY ${column} ${orderDirection}`
            
            console.log('🔍 PlanetScale Query:', query)
            const result = await planetscale.execute(query)
            
            const response = { data: result.rows, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ PlanetScale select error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      }),
      
      in: (column: string, values: any[]) => ({
        order: (orderColumn: string, options?: { ascending?: boolean }) => ({
          then: async (callback?: (result: any) => void) => {
            try {
              const orderDirection = options?.ascending === false ? 'DESC' : 'ASC'
              const placeholders = values.map(() => '?').join(',')
              const query = `SELECT ${columns} FROM ${table} WHERE ${column} IN (${placeholders}) ORDER BY ${orderColumn} ${orderDirection}`
              
              console.log('🔍 PlanetScale Query:', query, values)
              const result = await planetscale.execute(query, values)
              
              const response = { data: result.rows, error: null }
              if (callback) callback(response)
              return response
            } catch (error) {
              console.error('❌ PlanetScale select with IN error:', error)
              const response = { data: null, error }
              if (callback) callback(response)
              return response
            }
          }
        })
      }),
      
      then: async (callback?: (result: any) => void) => {
        try {
          const query = `SELECT ${columns} FROM ${table}`
          console.log('🔍 PlanetScale Query:', query)
          const result = await planetscale.execute(query)
          
          const response = { data: result.rows, error: null }
          if (callback) callback(response)
          return response
        } catch (error) {
          console.error('❌ PlanetScale select error:', error)
          const response = { data: null, error }
          if (callback) callback(response)
          return response
        }
      }
    }),
    
    insert: (data: any) => ({
      select: (fields: string) => ({
        single: async () => {
          try {
            // Add timestamps
            const insertData = {
              ...data,
              created_at: new Date(),
              updated_at: new Date()
            }
            
            const columns = Object.keys(insertData).join(', ')
            const placeholders = Object.keys(insertData).map(() => '?').join(', ')
            const values = Object.values(insertData)
            
            const query = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`
            console.log('💾 PlanetScale Insert:', query, values)
            
            const result = await planetscale.execute(query, values)
            
            // Get the inserted record
            const selectQuery = `SELECT ${fields} FROM ${table} WHERE id = LAST_INSERT_ID()`
            const selectResult = await planetscale.execute(selectQuery)
            
            return { data: selectResult.rows[0], error: null }
          } catch (error) {
            console.error('❌ PlanetScale insert error:', error)
            return { data: null, error }
          }
        }
      }),
      
      then: async (callback?: (result: any) => void) => {
        try {
          const insertData = {
            ...data,
            created_at: new Date(),
            updated_at: new Date()
          }
          
          const columns = Object.keys(insertData).join(', ')
          const placeholders = Object.keys(insertData).map(() => '?').join(', ')
          const values = Object.values(insertData)
          
          const query = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`
          console.log('💾 PlanetScale Insert:', query, values)
          
          const result = await planetscale.execute(query, values)
          
          const response = { data: { id: result.insertId, ...insertData }, error: null }
          if (callback) callback(response)
          return response
        } catch (error) {
          console.error('❌ PlanetScale insert error:', error)
          const response = { data: null, error }
          if (callback) callback(response)
          return response
        }
      }
    }),
    
    update: (updateData: any) => ({
      eq: (column: string, value: any) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const dataWithTimestamp = {
              ...updateData,
              updated_at: new Date()
            }
            
            const setClause = Object.keys(dataWithTimestamp)
              .map(key => `${key} = ?`)
              .join(', ')
            const values = [...Object.values(dataWithTimestamp), value]
            
            const query = `UPDATE ${table} SET ${setClause} WHERE ${column} = ?`
            console.log('🔄 PlanetScale Update:', query, values)
            
            const result = await planetscale.execute(query, values)
            
            const response = { data: null, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ PlanetScale update error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      })
    }),
    
    delete: () => ({
      eq: (column: string, value: any) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const query = `DELETE FROM ${table} WHERE ${column} = ?`
            console.log('🗑️ PlanetScale Delete:', query, [value])
            
            const result = await planetscale.execute(query, [value])
            
            const response = { data: null, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ PlanetScale delete error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      })
    })
  })
}

// Test connection function
export async function testPlanetScaleConnection() {
  try {
    console.log('🔍 Testing PlanetScale connection...')
    const result = await planetscale.execute('SELECT 1 as test')
    console.log('✅ PlanetScale connection successful:', result)
    return true
  } catch (error) {
    console.error('❌ PlanetScale connection failed:', error)
    return false
  }
}

// Export for compatibility
export const client = planetscaleClient
export default planetscaleClient

console.log('🚀 PlanetScale client initialized')
