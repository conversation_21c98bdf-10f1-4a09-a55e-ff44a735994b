/**
 * Platform Detection Utilities for StayOnBeat
 * Detects and validates URLs from supported platforms
 */

export type SupportedPlatform = 'youtube' | 'spotify' | 'soundcloud' | 'bandcamp' | 'local' | 'unknown'

export interface PlatformDetectionResult {
  platform: SupportedPlatform
  isValid: boolean
  platformId?: string
  contentType?: 'track' | 'playlist' | 'album' | 'artist'
  originalUrl: string
  normalizedUrl?: string
  error?: string
}

/**
 * YouTube URL patterns and detection
 */
const YOUTUBE_PATTERNS = {
  video: /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|music\.youtube\.com\/watch\?v=)([a-zA-Z0-9_-]{11})/,
  playlist: /(?:youtube\.com\/playlist\?list=|music\.youtube\.com\/playlist\?list=)([a-zA-Z0-9_-]+)/,
  channel: /(?:youtube\.com\/channel\/|youtube\.com\/c\/|youtube\.com\/@)([a-zA-Z0-9_-]+)/
}

/**
 * Spotify URL patterns and detection
 */
const SPOTIFY_PATTERNS = {
  track: /spotify\.com\/track\/([a-zA-Z0-9]{22})/,
  album: /spotify\.com\/album\/([a-zA-Z0-9]{22})/,
  playlist: /spotify\.com\/playlist\/([a-zA-Z0-9]{22})/,
  artist: /spotify\.com\/artist\/([a-zA-Z0-9]{22})/
}

/**
 * SoundCloud URL patterns and detection
 */
const SOUNDCLOUD_PATTERNS = {
  track: /soundcloud\.com\/[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+(?:\?.*)?$/,
  playlist: /soundcloud\.com\/[a-zA-Z0-9_-]+\/sets\/[a-zA-Z0-9_-]+/,
  user: /soundcloud\.com\/[a-zA-Z0-9_-]+(?:\?.*)?$/,
  shortUrl: /on\.soundcloud\.com\/[a-zA-Z0-9]+/
}

/**
 * Bandcamp URL patterns and detection
 */
const BANDCAMP_PATTERNS = {
  track: /[a-zA-Z0-9_-]+\.bandcamp\.com\/track\/[a-zA-Z0-9_-]+/,
  album: /[a-zA-Z0-9_-]+\.bandcamp\.com\/album\/[a-zA-Z0-9_-]+/,
  artist: /[a-zA-Z0-9_-]+\.bandcamp\.com\/?$/
}

/**
 * Detect platform from URL
 */
export function detectPlatform(url: string): PlatformDetectionResult {
  if (!url || typeof url !== 'string') {
    return {
      platform: 'unknown',
      isValid: false,
      originalUrl: url,
      error: 'Invalid URL provided'
    }
  }

  // Normalize URL
  const normalizedUrl = url.trim().toLowerCase()

  // YouTube Detection
  if (normalizedUrl.includes('youtube.com') || normalizedUrl.includes('youtu.be') || normalizedUrl.includes('music.youtube.com')) {
    return detectYouTube(url, normalizedUrl)
  }

  // Spotify Detection
  if (normalizedUrl.includes('spotify.com')) {
    return detectSpotify(url, normalizedUrl)
  }

  // SoundCloud Detection
  if (normalizedUrl.includes('soundcloud.com') || normalizedUrl.includes('on.soundcloud.com')) {
    return detectSoundCloud(url, normalizedUrl)
  }

  // Bandcamp Detection
  if (normalizedUrl.includes('bandcamp.com')) {
    return detectBandcamp(url, normalizedUrl)
  }

  // Local file detection (for future file upload support)
  if (normalizedUrl.startsWith('blob:') || normalizedUrl.startsWith('data:') || normalizedUrl.includes('localhost')) {
    return {
      platform: 'local',
      isValid: true,
      contentType: 'track',
      originalUrl: url,
      normalizedUrl: normalizedUrl
    }
  }

  return {
    platform: 'unknown',
    isValid: false,
    originalUrl: url,
    error: 'Unsupported platform or invalid URL format'
  }
}

/**
 * YouTube URL detection and validation
 */
function detectYouTube(originalUrl: string, normalizedUrl: string): PlatformDetectionResult {
  // Check for video
  const videoMatch = originalUrl.match(YOUTUBE_PATTERNS.video)
  if (videoMatch) {
    return {
      platform: 'youtube',
      isValid: true,
      platformId: videoMatch[1],
      contentType: 'track',
      originalUrl,
      normalizedUrl: `https://www.youtube.com/watch?v=${videoMatch[1]}`
    }
  }

  // Check for playlist
  const playlistMatch = originalUrl.match(YOUTUBE_PATTERNS.playlist)
  if (playlistMatch) {
    return {
      platform: 'youtube',
      isValid: true,
      platformId: playlistMatch[1],
      contentType: 'playlist',
      originalUrl,
      normalizedUrl: `https://www.youtube.com/playlist?list=${playlistMatch[1]}`
    }
  }

  return {
    platform: 'youtube',
    isValid: false,
    originalUrl,
    error: 'Invalid YouTube URL format'
  }
}

/**
 * Spotify URL detection and validation
 */
function detectSpotify(originalUrl: string, normalizedUrl: string): PlatformDetectionResult {
  // Check for track
  const trackMatch = originalUrl.match(SPOTIFY_PATTERNS.track)
  if (trackMatch) {
    return {
      platform: 'spotify',
      isValid: true,
      platformId: trackMatch[1],
      contentType: 'track',
      originalUrl,
      normalizedUrl: `https://open.spotify.com/track/${trackMatch[1]}`
    }
  }

  // Check for album
  const albumMatch = originalUrl.match(SPOTIFY_PATTERNS.album)
  if (albumMatch) {
    return {
      platform: 'spotify',
      isValid: true,
      platformId: albumMatch[1],
      contentType: 'album',
      originalUrl,
      normalizedUrl: `https://open.spotify.com/album/${albumMatch[1]}`
    }
  }

  // Check for playlist
  const playlistMatch = originalUrl.match(SPOTIFY_PATTERNS.playlist)
  if (playlistMatch) {
    return {
      platform: 'spotify',
      isValid: true,
      platformId: playlistMatch[1],
      contentType: 'playlist',
      originalUrl,
      normalizedUrl: `https://open.spotify.com/playlist/${playlistMatch[1]}`
    }
  }

  return {
    platform: 'spotify',
    isValid: false,
    originalUrl,
    error: 'Invalid Spotify URL format'
  }
}

/**
 * SoundCloud URL detection and validation
 */
function detectSoundCloud(originalUrl: string, normalizedUrl: string): PlatformDetectionResult {
  // Check for short URL
  const shortMatch = originalUrl.match(SOUNDCLOUD_PATTERNS.shortUrl)
  if (shortMatch) {
    return {
      platform: 'soundcloud',
      isValid: true,
      platformId: shortMatch[0],
      contentType: 'track',
      originalUrl,
      normalizedUrl: originalUrl // Keep original for short URLs
    }
  }

  // Check for playlist
  const playlistMatch = originalUrl.match(SOUNDCLOUD_PATTERNS.playlist)
  if (playlistMatch) {
    return {
      platform: 'soundcloud',
      isValid: true,
      platformId: originalUrl,
      contentType: 'playlist',
      originalUrl,
      normalizedUrl: originalUrl
    }
  }

  // Check for track
  const trackMatch = originalUrl.match(SOUNDCLOUD_PATTERNS.track)
  if (trackMatch) {
    return {
      platform: 'soundcloud',
      isValid: true,
      platformId: originalUrl,
      contentType: 'track',
      originalUrl,
      normalizedUrl: originalUrl
    }
  }

  return {
    platform: 'soundcloud',
    isValid: false,
    originalUrl,
    error: 'Invalid SoundCloud URL format'
  }
}

/**
 * Bandcamp URL detection and validation
 */
function detectBandcamp(originalUrl: string, normalizedUrl: string): PlatformDetectionResult {
  // Check for track
  const trackMatch = originalUrl.match(BANDCAMP_PATTERNS.track)
  if (trackMatch) {
    return {
      platform: 'bandcamp',
      isValid: true,
      platformId: originalUrl,
      contentType: 'track',
      originalUrl,
      normalizedUrl: originalUrl
    }
  }

  // Check for album
  const albumMatch = originalUrl.match(BANDCAMP_PATTERNS.album)
  if (albumMatch) {
    return {
      platform: 'bandcamp',
      isValid: true,
      platformId: originalUrl,
      contentType: 'album',
      originalUrl,
      normalizedUrl: originalUrl
    }
  }

  return {
    platform: 'bandcamp',
    isValid: false,
    originalUrl,
    error: 'Invalid Bandcamp URL format'
  }
}

/**
 * Validate URL accessibility (basic check)
 */
export async function validateUrlAccessibility(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch {
    return false
  }
}
