import { supabase } from "./supabase"

/**
 * Generates an M3U8 playlist from the 20 Spotify tracks
 * and triggers a download
 */
export async function generateSpotifyPlaylist() {
  try {
    // Get the latest 20 submissions (which should be our Spotify tracks)
    const { data, error } = await supabase
      .from("submissions")
      .select("artist_name, song_title, url")
      .order("created_at", { ascending: false })
      .limit(20)

    if (error) {
      console.error("Error fetching submissions:", error)
      return
    }

    if (!data || data.length === 0) {
      console.warn("No submissions found")
      return
    }

    // Generate M3U8 content
    let m3u8Content = "#EXTM3U\n"
    m3u8Content += "#PLAYLIST:Spotify Indie Artists\n\n"

    // Add each track
    data.forEach((track) => {
      m3u8Content += `#EXTINF:-1,${track.artist_name} - ${track.song_title}\n`
      m3u8Content += `${track.url}\n\n`
    })

    // Create a blob and download
    const blob = new Blob([m3u8Content], { type: "application/vnd.apple.mpegurl" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = "spotify-indie-artists.m3u8"
    document.body.appendChild(a)
    a.click()

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }, 100)

    return m3u8Content
  } catch (error) {
    console.error("Error generating playlist:", error)
  }
}
