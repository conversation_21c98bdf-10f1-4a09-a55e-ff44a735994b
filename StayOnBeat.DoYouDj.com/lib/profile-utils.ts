import { supabase } from "./supabase"
import type { User, UserResponse } from "@supabase/supabase-js"

// Function to fetch additional profile data from providers
export async function enrichUserProfile(user: User) {
  try {
    // First check if we already have a profile for this user
    const { data: existingProfile } = await supabase.from("profiles").select("*").eq("id", user.id).single()

    if (existingProfile?.is_complete) {
      return existingProfile
    }

    // Get the user's identities to determine which provider they used
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(user.id);

    if (userError || !userData?.user?.identities?.length) {
      return null
    }

    const identity = userData.user.identities[0]
    const provider = identity.provider

    // Basic profile data from auth metadata
    const profileData = {
      id: userData.user.id,
      email: userData.user.email,
      full_name: userData.user.user_metadata?.full_name || "",
      avatar_url: userData.user.user_metadata?.avatar_url || "",
      provider: provider,
      provider_id: identity.id,
      updated_at: new Date().toISOString(),
    }

    // For each provider, we could fetch additional data using their APIs
    // This would require storing and using the OAuth tokens
    if (provider === "spotify") {
      // Example: Fetch Spotify profile data using the access token
      // This would be implemented with the Spotify API
      console.log("Enriching profile with Spotify data")
      // profileData.spotify_profile = spotifyData
    } else if (provider === "google") {
      // For YouTube (via Google)
      console.log("Enriching profile with YouTube data")
      // profileData.youtube_profile = youtubeData
    } else if (provider === "soundcloud") {
      console.log("Enriching profile with SoundCloud data")
      // profileData.soundcloud_profile = soundcloudData
    }

    // Update the profile in the database
    const { data: updatedProfile, error: updateError } = await supabase
      .from("profiles")
      .upsert({
        ...profileData,
        is_complete: true,
      })
      .select()
      .single()

    if (updateError) {
      console.error("Error updating profile:", updateError)
      return null
    }

    return updatedProfile
  } catch (error) {
    console.error("Error enriching user profile:", error)
    return null
  }
}

// Function to get a user's complete profile
export async function getUserProfile(userId: string) {
  try {
    const { data, error } = await supabase.from("profiles").select("*").eq("id", userId).single()

    if (error) {
      console.error("Error fetching user profile:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in getUserProfile:", error)
    return null
  }
}
