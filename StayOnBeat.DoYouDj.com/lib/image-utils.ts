import { supabase } from "./supabase"

// Image categories
export type ImageCategory = "background" | "profile" | "logo" | "general"

// Image reference structure
export interface ImageReference {
  id: string
  url: string
  category: ImageCategory
  name: string
  createdAt: string
  updatedAt: string
}

// Save image reference to database
export async function saveImageReference(
  url: string,
  category: ImageCategory,
  name: string,
): Promise<ImageReference | null> {
  try {
    const imageData = {
      url,
      category,
      name,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase.from("images").insert(imageData).select().single()

    if (error) throw error

    return data as unknown as ImageReference
  } catch (error) {
    console.error("Error saving image reference:", error)
    return null
  }
}

// Get image by category
export async function getImagesByCategory(category: ImageCategory): Promise<ImageReference[]> {
  try {
    const { data, error } = await supabase
      .from("images")
      .select("*")
      .eq("category", category)
      .order("created_at", { ascending: false })

    if (error) throw error

    return data as unknown as ImageReference[]
  } catch (error) {
    console.error(`Error fetching ${category} images:`, error)
    return []
  }
}

// Get default image for a category - rewritten for compatibility
export async function getDefaultImage(category: ImageCategory): Promise<string | null> {
  if (!category) {
    console.error("Category is required for getDefaultImage")
    return null
  }

  try {
    // Safely construct the ID
    const settingId = `default_${category}_image`

    // Make the query using only basic select and eq methods
    const { data, error } = await supabase.from("settings").select("value").eq("id", settingId)

    // Handle query errors
    if (error) {
      console.error(`Database error fetching ${settingId}:`, error)
      return null
    }

    // If no data was found or empty array
    if (!data || data.length === 0) {
      return null
    }

    // Get the first result from the array
    const setting = data[0]

    // Try to safely extract the URL
    try {
      // Check if value exists and is an object
      if (setting.value && typeof setting.value === "object") {
        // Check if url property exists
        return setting.value.url || null
      }
      return null
    } catch (parseError) {
      console.error(`Error parsing value for ${settingId}:`, parseError)
      return null
    }
  } catch (error) {
    // Catch any unexpected errors
    console.error(`Unexpected error fetching ${category} image:`, error)
    return null
  }
}

// Set default image for a category
export async function setDefaultImage(category: ImageCategory, url: string): Promise<boolean> {
  try {
    const { error } = await supabase.from("settings").upsert({
      id: `default_${category}_image`,
      value: { url },
      updated_at: new Date().toISOString(),
    })

    if (error) throw error

    return true
  } catch (error) {
    console.error(`Error setting default ${category} image:`, error)
    return false
  }
}

// Delete image reference
export async function deleteImageReference(id: string): Promise<boolean> {
  try {
    const { error } = await supabase.from("images").delete().eq("id", id)

    if (error) throw error

    return true
  } catch (error) {
    console.error("Error deleting image reference:", error)
    return false
  }
}
