import { supabase } from "./supabase"
import type { PlaylistItem } from "./types"

/**
 * Fetches playlist items from Supabase
 * @param playlistId The ID of the playlist
 * @returns An array of PlaylistItem objects
 */
export async function getPlaylistItemsFromSupabase(playlistId: string): Promise<PlaylistItem[]> {
  try {
    const { data, error } = await supabase
      .from("playlist")
      .select("*")
      .eq("id", playlistId)
      .order("position", { ascending: true })

    if (error) {
      console.error("Error fetching playlist items:", error)
      return []
    }

    return data.map((item) => ({
      id: item.id,
      position: item.position,
      artistName: item.artist_name,
      songTitle: item.song_title,
      type: item.type,
      mediaUrl: item.url,
      platform: item.platform,
    }))
  } catch (error) {
    console.error("Error in getPlaylistItemsFromSupabase:", error)
    return []
  }
}

/**
 * Generates an M3U8 playlist file content from an array of tracks
 *
 * @param tracks Array of playlist items
 * @param title Optional playlist title
 * @returns String content of the M3U8 file
 */
export function createM3U8FromItems(tracks: PlaylistItem[], title?: string): string {
  // Start with the M3U8 header
  let content = "#EXTM3U\n"

  // Add playlist info if title is provided
  if (title) {
    content += `#PLAYLIST:${title}\n`
  }

  // Add each track
  tracks.forEach((track) => {
    // Add track info with duration if available
    const durationStr = track.duration ? `,${track.duration}` : ""
    content += `#EXTINF:${durationStr},${track.artistName} - ${track.songTitle}\n`

    // Add track URL
    content += `${track.mediaUrl || ""}\n`
  })

  return content
}

/**
 * Generates an M3U8 playlist file content from an array of tracks
 *
 * @returns String content of the M3U8 file
 */
export async function generateM3U8Playlist(): Promise<string> {
  try {
    const { data, error } = await supabase.from("playlist").select("*").order("position", { ascending: true })

    if (error) {
      console.error("Error fetching playlist:", error)
      return ""
    }

    if (!data || data.length === 0) {
      return ""
    }

    let m3u8Content = "#EXTM3U\n"
    data.forEach((item) => {
      m3u8Content += `#EXTINF:${item.duration || -1},${item.artist_name} - ${item.song_title}\n`
      m3u8Content += `${item.url}\n`
    })

    return m3u8Content
  } catch (error) {
    console.error("Error generating M3U8 playlist:", error)
    return ""
  }
}

/**
 * Creates and downloads an M3U8 file
 *
 * @param content M3U8 file content
 * @param filename Name of the file to download
 */
export function downloadM3U8Playlist(content: string, filename = "playlist.m3u8"): void {
  // Create a blob from the content
  const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })

  // Create a download link
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename

  // Trigger the download
  document.body.appendChild(a)
  a.click()

  // Clean up
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

export function downloadM3U8File(content: string, filename = "playlist.m3u8"): void {
  // Create a blob from the content
  const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })

  // Create a download link
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename

  // Trigger the download
  document.body.appendChild(a)
  a.click()

  // Clean up
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
