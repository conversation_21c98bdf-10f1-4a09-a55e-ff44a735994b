/**
 * Neon Database Client for StayOnBeat
 * Free PostgreSQL database with Supabase-compatible API
 */

import { Pool } from 'pg'

// Neon connection configuration
const connectionString = process.env.DATABASE_URL || process.env.NEON_DATABASE_URL

// Create connection pool
let pool: Pool | null = null

function getPool() {
  if (!pool && connectionString) {
    pool = new Pool({
      connectionString,
      ssl: connectionString.includes('neon.tech') ? { rejectUnauthorized: false } : false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }
  return pool
}

// Database client that mimics Supabase API for easy migration
export const neonClient = {
  from: (table: string) => ({
    select: (columns: string = '*') => ({
      order: (column: string, options?: { ascending?: boolean }) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const pool = getPool()
            if (!pool) throw new Error('No database connection')
            
            const orderDirection = options?.ascending === false ? 'DESC' : 'ASC'
            const query = `SELECT ${columns} FROM ${table} ORDER BY ${column} ${orderDirection}`
            
            console.log('🔍 Neon Query:', query)
            const result = await pool.query(query)
            
            const response = { data: result.rows, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ Neon select error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      }),
      
      in: (column: string, values: any[]) => ({
        order: (orderColumn: string, options?: { ascending?: boolean }) => ({
          then: async (callback?: (result: any) => void) => {
            try {
              const pool = getPool()
              if (!pool) throw new Error('No database connection')
              
              const orderDirection = options?.ascending === false ? 'DESC' : 'ASC'
              const placeholders = values.map((_, i) => `$${i + 1}`).join(',')
              const query = `SELECT ${columns} FROM ${table} WHERE ${column} IN (${placeholders}) ORDER BY ${orderColumn} ${orderDirection}`
              
              console.log('🔍 Neon Query:', query, values)
              const result = await pool.query(query, values)
              
              const response = { data: result.rows, error: null }
              if (callback) callback(response)
              return response
            } catch (error) {
              console.error('❌ Neon select with IN error:', error)
              const response = { data: null, error }
              if (callback) callback(response)
              return response
            }
          }
        })
      }),
      
      then: async (callback?: (result: any) => void) => {
        try {
          const pool = getPool()
          if (!pool) throw new Error('No database connection')
          
          const query = `SELECT ${columns} FROM ${table}`
          console.log('🔍 Neon Query:', query)
          const result = await pool.query(query)
          
          const response = { data: result.rows, error: null }
          if (callback) callback(response)
          return response
        } catch (error) {
          console.error('❌ Neon select error:', error)
          const response = { data: null, error }
          if (callback) callback(response)
          return response
        }
      }
    }),
    
    insert: (data: any) => ({
      select: (fields: string) => ({
        single: async () => {
          try {
            const pool = getPool()
            if (!pool) throw new Error('No database connection')
            
            // Add timestamps
            const insertData = {
              ...data,
              created_at: new Date(),
              updated_at: new Date()
            }
            
            const columns = Object.keys(insertData)
            const placeholders = columns.map((_, i) => `$${i + 1}`)
            const values = Object.values(insertData)
            
            const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders.join(', ')}) RETURNING ${fields}`
            console.log('💾 Neon Insert:', query, values)
            
            const result = await pool.query(query, values)
            
            return { data: result.rows[0], error: null }
          } catch (error) {
            console.error('❌ Neon insert error:', error)
            return { data: null, error }
          }
        }
      }),
      
      then: async (callback?: (result: any) => void) => {
        try {
          const pool = getPool()
          if (!pool) throw new Error('No database connection')
          
          const insertData = {
            ...data,
            created_at: new Date(),
            updated_at: new Date()
          }
          
          const columns = Object.keys(insertData)
          const placeholders = columns.map((_, i) => `$${i + 1}`)
          const values = Object.values(insertData)
          
          const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders.join(', ')}) RETURNING *`
          console.log('💾 Neon Insert:', query, values)
          
          const result = await pool.query(query, values)
          
          const response = { data: result.rows[0], error: null }
          if (callback) callback(response)
          return response
        } catch (error) {
          console.error('❌ Neon insert error:', error)
          const response = { data: null, error }
          if (callback) callback(response)
          return response
        }
      }
    }),
    
    update: (updateData: any) => ({
      eq: (column: string, value: any) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const pool = getPool()
            if (!pool) throw new Error('No database connection')
            
            const dataWithTimestamp = {
              ...updateData,
              updated_at: new Date()
            }
            
            const columns = Object.keys(dataWithTimestamp)
            const setClause = columns.map((col, i) => `${col} = $${i + 1}`).join(', ')
            const values = [...Object.values(dataWithTimestamp), value]
            
            const query = `UPDATE ${table} SET ${setClause} WHERE ${column} = $${columns.length + 1}`
            console.log('🔄 Neon Update:', query, values)
            
            const result = await pool.query(query, values)
            
            const response = { data: null, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ Neon update error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      })
    }),
    
    delete: () => ({
      eq: (column: string, value: any) => ({
        then: async (callback?: (result: any) => void) => {
          try {
            const pool = getPool()
            if (!pool) throw new Error('No database connection')
            
            const query = `DELETE FROM ${table} WHERE ${column} = $1`
            console.log('🗑️ Neon Delete:', query, [value])
            
            const result = await pool.query(query, [value])
            
            const response = { data: null, error: null }
            if (callback) callback(response)
            return response
          } catch (error) {
            console.error('❌ Neon delete error:', error)
            const response = { data: null, error }
            if (callback) callback(response)
            return response
          }
        }
      })
    })
  })
}

// Test connection function
export async function testNeonConnection() {
  try {
    console.log('🔍 Testing Neon connection...')
    const pool = getPool()
    if (!pool) {
      console.log('❌ No DATABASE_URL configured')
      return false
    }
    
    const result = await pool.query('SELECT 1 as test')
    console.log('✅ Neon connection successful:', result.rows[0])
    return true
  } catch (error) {
    console.error('❌ Neon connection failed:', error)
    return false
  }
}

// Export for compatibility
export const client = neonClient
export default neonClient

console.log('🚀 Neon client initialized')
