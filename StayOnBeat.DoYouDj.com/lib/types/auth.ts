export type SocialPlatform =
  | "spotify"
  | "apple"
  | "amazon"
  | "deezer"
  | "instagram"
  | "tiktok"
  | "youtube"
  | "soundcloud"
  | "facebook"
  | "twitter"

export interface PlatformConfig {
  name: string
  clientId: string
  authUrl: string
  scope: string
  color: string
  icon: string
  hoverColor: string
}

export interface AuthState {
  platform: SocialPlatform
  timestamp: number
  nonce: string
}
