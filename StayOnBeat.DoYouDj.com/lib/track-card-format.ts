/**
 * StayOnBeat TrackCardSubmission Format
 * Proprietary file format for standardized track submissions
 * 
 * FREE TIER: Basic format with essential data
 * PAID TIER: Enhanced with audio fingerprinting, cross-references, and advanced metadata
 */

import type { SupportedPlatform } from './platform-detector'

// FREE TIER: Basic TrackCardSubmission format
export interface TrackCardSubmission {
  // Unique identifier for this track card
  stayonbeat_id: string // Format: SOB_YYYYMMDD_HHMMSS_XXX
  
  // Submission metadata
  submission_metadata: {
    original_url: string
    platform: SupportedPlatform
    processed_at: string // ISO timestamp
    show_id: string // e.g., "stayonbeat_episode_001"
    submission_type: 'VIP' | 'GA' | 'Free' | 'Skip'
    user_id?: string
    user_email?: string
    processing_version: string // e.g., "1.0.0"
  }
  
  // Core track data
  track_data: {
    title: string
    artist: string
    duration?: number
    artwork_url?: string
    album_name?: string
    genre?: string
    release_date?: string
    is_explicit?: boolean
    platform_id: string
    stream_url?: string
  }
  
  // FREE TIER: Basic search indexing
  search_index: {
    normalized_title: string
    normalized_artist: string
    keywords: string[]
    searchable_text: string
  }
  
  // FREE TIER: Basic file info
  file_metadata: {
    format_version: "1.0.0"
    created_at: string
    file_size_bytes?: number
    checksum?: string
  }
  
  // PAID TIER PLACEHOLDERS (not implemented in free version)
  // audio_fingerprint?: AudioFingerprint
  // cross_reference?: CrossReference
  // advanced_metadata?: AdvancedMetadata
  // master_library_sync?: MasterLibrarySync
}

// FREE TIER: Basic audio fingerprint placeholder
export interface AudioFingerprint {
  // PAID TIER: Implement with services like AcoustID or Chromaprint
  placeholder: "PAID_TIER_FEATURE"
  // algorithm: string
  // fingerprint_data: string
  // confidence_score: number
}

// FREE TIER: Cross-reference placeholder for master database
export interface CrossReference {
  // PAID TIER: Links to DoYouDj.com master database
  placeholder: "PAID_TIER_FEATURE"
  // doyoudj_artist_id?: string
  // master_library_id?: string
  // duplicate_submissions?: string[]
  // similar_tracks?: string[]
}

// FREE TIER: Advanced metadata placeholder
export interface AdvancedMetadata {
  // PAID TIER: Enhanced metadata from multiple sources
  placeholder: "PAID_TIER_FEATURE"
  // spotify_analysis?: SpotifyAudioFeatures
  // youtube_stats?: YouTubeStats
  // soundcloud_stats?: SoundCloudStats
  // mood_analysis?: MoodAnalysis
  // bpm?: number
  // key?: string
  // energy_level?: number
}

/**
 * FREE TIER: Generate a StayOnBeat ID
 */
export function generateStayOnBeatId(showId: string): string {
  const now = new Date()
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '') // YYYYMMDD
  const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '') // HHMMSS
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  
  return `SOB_${dateStr}_${timeStr}_${random}`
}

/**
 * FREE TIER: Create normalized search text
 */
export function createSearchIndex(title: string, artist: string): {
  normalized_title: string
  normalized_artist: string
  keywords: string[]
  searchable_text: string
} {
  const normalizeText = (text: string) => 
    text.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()

  const normalized_title = normalizeText(title)
  const normalized_artist = normalizeText(artist)
  
  const keywords = [
    ...normalized_title.split(' '),
    ...normalized_artist.split(' ')
  ].filter(word => word.length > 2) // Remove short words
  
  const searchable_text = `${normalized_title} ${normalized_artist} ${keywords.join(' ')}`
  
  return {
    normalized_title,
    normalized_artist,
    keywords: [...new Set(keywords)], // Remove duplicates
    searchable_text
  }
}

/**
 * FREE TIER: Convert StayOnBeatTrackCard to TrackCardSubmission
 */
export function createTrackCardSubmission(
  trackCard: any, // StayOnBeatTrackCard
  submissionMetadata: {
    original_url: string
    show_id: string
    submission_type: 'VIP' | 'GA' | 'Free' | 'Skip'
    user_id?: string
    user_email?: string
  }
): TrackCardSubmission {
  const stayonbeat_id = generateStayOnBeatId(submissionMetadata.show_id)
  const search_index = createSearchIndex(trackCard.title, trackCard.artist)
  const now = new Date().toISOString()
  
  return {
    stayonbeat_id,
    submission_metadata: {
      original_url: submissionMetadata.original_url,
      platform: trackCard.platform,
      processed_at: now,
      show_id: submissionMetadata.show_id,
      submission_type: submissionMetadata.submission_type,
      user_id: submissionMetadata.user_id,
      user_email: submissionMetadata.user_email,
      processing_version: "1.0.0"
    },
    track_data: {
      title: trackCard.title,
      artist: trackCard.artist,
      duration: trackCard.duration,
      artwork_url: trackCard.artworkUrl,
      album_name: trackCard.albumName,
      genre: trackCard.genre,
      release_date: trackCard.releaseDate,
      is_explicit: trackCard.isExplicit,
      platform_id: trackCard.platformId,
      stream_url: trackCard.streamUrl
    },
    search_index,
    file_metadata: {
      format_version: "1.0.0",
      created_at: now,
      file_size_bytes: JSON.stringify({}).length, // Will be calculated when saved
      checksum: undefined // Will be calculated when saved
    }
  }
}

/**
 * FREE TIER: Save TrackCardSubmission to local storage or database
 * PAID TIER: Save to Supabase Storage with CDN and versioning
 */
export async function saveTrackCardSubmission(
  submission: TrackCardSubmission,
  storage_method: 'database' | 'local' = 'database'
): Promise<{ success: boolean; file_path?: string; error?: string }> {
  try {
    if (storage_method === 'database') {
      // FREE TIER: Store in database JSONB column
      // PAID TIER: Store in Supabase Storage as files
      
      // For now, just return success (implement database storage later)
      return {
        success: true,
        file_path: `database://track_cards/${submission.stayonbeat_id}`
      }
    } else {
      // FREE TIER: Local storage fallback
      const json_data = JSON.stringify(submission, null, 2)
      
      // In browser environment, use localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`track_card_${submission.stayonbeat_id}`, json_data)
        return {
          success: true,
          file_path: `localStorage://track_card_${submission.stayonbeat_id}`
        }
      }
      
      return {
        success: false,
        error: "Local storage not available in server environment"
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error saving track card'
    }
  }
}

/**
 * PAID TIER UPGRADE NOTES:
 * 
 * 1. SUPABASE STORAGE ($0.021/GB/month):
 *    - Store TrackCardSubmission files as JSON in Supabase Storage
 *    - Enable CDN for fast global access
 *    - Automatic compression and versioning
 * 
 * 2. AUDIO FINGERPRINTING ($0.001/track):
 *    - Integrate AcoustID or Chromaprint
 *    - Detect duplicate submissions automatically
 *    - Enable advanced search by audio similarity
 * 
 * 3. MASTER DATABASE SYNC ($25/month Pro plan):
 *    - Real-time sync with DoYouDj.com master database
 *    - Cross-reference artist profiles across 1000s of shows
 *    - Shared music library with conflict resolution
 * 
 * 4. ADVANCED ANALYTICS ($50/month):
 *    - Spotify Audio Features API integration
 *    - YouTube/SoundCloud statistics tracking
 *    - Mood analysis and BPM detection
 *    - Audience engagement metrics
 * 
 * 5. BACKUP & DISASTER RECOVERY ($10/month):
 *    - Point-in-time recovery
 *    - Cross-region replication
 *    - Automated daily backups to external storage
 */
