/**
 * Local Database Implementation for StayOnBeat
 * This provides a working database when Supabase is not accessible
 * Uses in-memory storage with persistence to local files
 */

import fs from 'fs'
import path from 'path'

// Database storage path
const DB_PATH = path.join(process.cwd(), 'data')
const SUBMISSIONS_FILE = path.join(DB_PATH, 'submissions.json')
const PLAYLIST_FILE = path.join(DB_PATH, 'playlist.json')

// Ensure data directory exists
if (!fs.existsSync(DB_PATH)) {
  fs.mkdirSync(DB_PATH, { recursive: true })
}

// Initialize empty files if they don't exist
if (!fs.existsSync(SUBMISSIONS_FILE)) {
  fs.writeFileSync(SUBMISSIONS_FILE, '[]')
}
if (!fs.existsSync(PLAYLIST_FILE)) {
  fs.writeFileSync(PLAYLIST_FILE, '[]')
}

// In-memory storage
let submissions: any[] = []
let playlist: any[] = []

// Load data from files
function loadData() {
  try {
    submissions = JSON.parse(fs.readFileSync(SUBMISSIONS_FILE, 'utf8'))
    playlist = JSON.parse(fs.readFileSync(PLAYLIST_FILE, 'utf8'))
  } catch (error) {
    console.log('📁 Initializing empty local database')
    submissions = []
    playlist = []
  }
}

// Save data to files
function saveData() {
  try {
    fs.writeFileSync(SUBMISSIONS_FILE, JSON.stringify(submissions, null, 2))
    fs.writeFileSync(PLAYLIST_FILE, JSON.stringify(playlist, null, 2))
  } catch (error) {
    console.error('❌ Error saving local database:', error)
  }
}

// Initialize data
loadData()

// Local database client that mimics Supabase API
export const localDatabase = {
  from: (table: string) => ({
    select: (columns: string = '*') => ({
      order: (column: string, options?: any) => ({
        then: (callback: (result: any) => void) => {
          const data = table === 'submissions' ? submissions : 
                      table === 'playlist' ? playlist : []
          
          // Sort data if needed
          if (options?.ascending === false) {
            data.sort((a, b) => b[column] - a[column])
          } else {
            data.sort((a, b) => a[column] - b[column])
          }
          
          callback({ data, error: null })
          return Promise.resolve({ data, error: null })
        }
      }),
      in: (column: string, values: any[]) => ({
        order: (column: string, options?: any) => ({
          then: (callback: (result: any) => void) => {
            let data = table === 'submissions' ? submissions : 
                      table === 'playlist' ? playlist : []
            
            // Filter data
            data = data.filter((item: any) => values.includes(item[column]))
            
            callback({ data, error: null })
            return Promise.resolve({ data, error: null })
          }
        })
      }),
      then: (callback: (result: any) => void) => {
        const data = table === 'submissions' ? submissions : 
                    table === 'playlist' ? playlist : []
        callback({ data, error: null })
        return Promise.resolve({ data, error: null })
      }
    }),
    
    insert: (data: any) => ({
      select: (fields: string) => ({
        single: () => {
          const newItem = {
            ...data,
            id: Date.now(), // Simple ID generation
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
          
          if (table === 'submissions') {
            submissions.push(newItem)
          } else if (table === 'playlist') {
            playlist.push(newItem)
          }
          
          saveData()
          console.log(`💾 LOCAL DB: Inserted into ${table}:`, newItem.id)
          
          return Promise.resolve({ data: newItem, error: null })
        }
      }),
      then: (callback: (result: any) => void) => {
        const newItem = {
          ...data,
          id: Date.now(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        
        if (table === 'submissions') {
          submissions.push(newItem)
        } else if (table === 'playlist') {
          playlist.push(newItem)
        }
        
        saveData()
        callback({ data: newItem, error: null })
        return Promise.resolve({ data: newItem, error: null })
      }
    }),
    
    update: (updateData: any) => ({
      eq: (column: string, value: any) => ({
        then: (callback: (result: any) => void) => {
          let targetArray = table === 'submissions' ? submissions : 
                           table === 'playlist' ? playlist : []
          
          const index = targetArray.findIndex((item: any) => item[column] === value)
          if (index !== -1) {
            targetArray[index] = {
              ...targetArray[index],
              ...updateData,
              updated_at: new Date().toISOString()
            }
            saveData()
            console.log(`💾 LOCAL DB: Updated ${table} item:`, value)
          }
          
          callback({ data: null, error: null })
          return Promise.resolve({ data: null, error: null })
        }
      })
    })
  })
}

// Check if we should use local database
export const shouldUseLocalDatabase = () => {
  return process.env.NEXT_PUBLIC_USE_LOCAL_DB === 'true'
}

console.log('📁 Local database initialized:', {
  submissions: submissions.length,
  playlist: playlist.length,
  enabled: shouldUseLocalDatabase()
})
