import { createClient } from "@supabase/supabase-js"

// Get environment variables with type checking
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('🔧 Supabase Environment Check:', {
  url: supabaseUrl ? 'Set' : 'Missing',
  key: supabase<PERSON>nonKey ? 'Set' : 'Missing',
  urlValue: supabaseUrl
})

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables:', {
    NEXT_PUBLIC_SUPABASE_URL: supabaseUrl,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: supabaseAnonKey ? 'Present' : 'Missing'
  })
  throw new Error("Missing Supabase environment variables")
}

// Create the Supabase client
console.log('🔧 Creating Supabase client...')
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
})

console.log('✅ Supabase client created:', {
  hasFromMethod: typeof supabase.from === 'function',
  clientType: typeof supabase,
  methods: Object.getOwnPropertyNames(supabase)
})

// Always use the real Supabase client - NO MOCKS
export const client = supabase

// Helper function to get the current user
export async function getCurrentUser() {
  try {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.user
  } catch (error) {
    console.error("Error getting current user:", error)
    return null
  }
}

// Helper function to sign out
export async function signOut() {
  try {
    return await supabase.auth.signOut()
  } catch (error) {
    console.error("Error signing out:", error)
    return { error: null }
  }
}

// Standard Supabase OAuth implementation
export async function signInWithOAuth(provider: string, options: any = {}) {
  try {
    // Use Supabase's standard OAuth implementation
    const redirectTo = typeof window !== 'undefined'
      ? `${window.location.origin}/auth/callback`
      : `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/auth/callback`

    return await supabase.auth.signInWithOAuth({
      provider: provider as any,
      options: {
        redirectTo,
        scopes: getProviderScopes(provider),
        ...options,
      },
    })
  } catch (error) {
    console.error(`Error signing in with ${provider}:`, error)
    return { data: null, error }
  }
}

// Helper function to get appropriate scopes for each provider
function getProviderScopes(provider: string): string {
  switch (provider) {
    case "spotify":
      return "user-read-email user-read-private user-top-read playlist-read-private"
    case "google":
      return "https://www.googleapis.com/auth/youtube.readonly"
    case "soundcloud":
      return "non-expiring"
    default:
      return ""
  }
}

// Helper function for email/password sign in
export async function signInWithEmail(email: string, password: string) {
  try {
    return await supabase.auth.signInWithPassword({
      email,
      password,
    })
  } catch (error) {
    console.error("Error signing in with email:", error)
    return { data: { user: null, session: null }, error }
  }
}

// Helper function for email/password sign up
export async function signUpWithEmail(email: string, password: string) {
  try {
    const redirectTo = typeof window !== 'undefined'
      ? `${window.location.origin}/auth/callback`
      : `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/auth/callback`

    return await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
      },
    })
  } catch (error) {
    console.error("Error signing up with email:", error)
    return { data: { user: null, session: null }, error }
  }
}

// Helper function to save OAuth tokens to Supabase
export async function saveOAuthTokens(provider: string, tokens: any) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new Error("No authenticated user found")
    }

    // Store the tokens in the user's metadata or a separate table
    const { data, error } = await supabase.from("user_oauth_tokens").upsert({
      user_id: user.id,
      provider,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expires_at: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error("Error saving OAuth tokens:", error)
    return null
  }
}
