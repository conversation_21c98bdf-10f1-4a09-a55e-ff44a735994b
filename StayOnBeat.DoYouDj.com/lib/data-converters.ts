import type { QueueCardItem } from "@/components/queue-card"

/**
 * Converts Supabase playlist data to QueueCardItem format
 *
 * @param supabaseData Raw data from Supabase playlist table
 * @returns Formatted QueueCardItem object
 */
export function convertSupabaseToQueueCardItem(supabaseData: any): QueueCardItem {
  return {
    id: supabaseData.id,
    position: supabaseData.position,
    artistName: supabaseData.artist_name,
    songTitle: supabaseData.song_title,
    type: supabaseData.type,
    submissionTime: supabaseData.submission_time,
    platform: supabaseData.platform,
    url: supabaseData.url,
    isPlaying: false,
  }
}

/**
 * Converts QueueCardItem to Supabase playlist format
 *
 * @param item QueueCardItem object
 * @returns Formatted object for Supabase playlist table
 */
export function convertQueueCardItemToSupabase(item: QueueCardItem): any {
  return {
    id: item.id,
    position: item.position,
    artist_name: item.artistName,
    song_title: item.songTitle,
    type: item.type,
    submission_time: item.submissionTime,
    platform: item.platform,
    url: item.url,
  }
}

/**
 * Converts Supabase submission data to QueueCardItem format
 *
 * @param supabaseData Raw data from Supabase submissions table
 * @returns Formatted QueueCardItem object
 */
export function convertSupabaseSubmissionToQueueCardItem(supabaseData: any): QueueCardItem {
  return {
    id: supabaseData.id,
    artistName: supabaseData.artist_name,
    artistId: supabaseData.artist_id,
    songTitle: supabaseData.song_title,
    type: supabaseData.submission_type,
    submissionTime: supabaseData.created_at,
    platform: supabaseData.platform,
    url: supabaseData.url,
  }
}
