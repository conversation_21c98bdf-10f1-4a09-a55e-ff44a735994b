import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

/**
 * Creates a Supabase client with Clerk authentication for server components
 * This uses the 'supabase' JWT template from Clerk
 */
export async function createServerSupabaseClient() {
  // Get the JWT token from Clerk using the 'supabase' template
  const token = await auth().getToken({ template: 'supabase' })
  
  // Create a Supabase client with the token
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    }
  )
}

/**
 * Creates a Supabase client for client components
 * This doesn't include authentication by default
 */
export function createClientSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

/**
 * Example of how to use the Supabase client in a server action
 */
export async function getProtectedData() {
  const supabase = await createServerSupabaseClient()
  
  // This query will include the user's JWT token in the request
  const { data, error } = await supabase
    .from('protected_table')
    .select('*')
  
  if (error) {
    console.error('Error fetching protected data:', error)
    throw new Error('Failed to fetch protected data')
  }
  
  return data
}
