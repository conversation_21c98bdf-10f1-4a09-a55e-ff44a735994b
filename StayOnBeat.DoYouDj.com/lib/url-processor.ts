/**
 * Core URL Processing Logic for StayOnBeat
 * Handles the complete URL-to-metadata pipeline
 */

import { detectPlatform, type PlatformDetectionResult, type SupportedPlatform } from './platform-detector'
import { supabase } from './supabase'
import { localDatabase, shouldUseLocalDatabase } from './local-database'
import { neonClient } from './neon'
import { createTrackCardSubmission, saveTrackCardSubmission, type TrackCardSubmission } from './track-card-format'

export interface ProcessingResult {
  success: boolean
  submissionId?: string
  trackCard?: StayOnBeatTrackCard
  error?: string
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed'
  platformDetection?: PlatformDetectionResult
}

export interface StayOnBeatTrackCard {
  title: string
  artist: string
  duration?: number
  artworkUrl?: string
  albumName?: string
  releaseDate?: string
  genre?: string
  platform: SupportedPlatform
  platformId: string
  originalUrl: string
  streamUrl?: string
  isExplicit?: boolean
  // FREE TIER: Basic metadata only
  // PAID TIER: Add audio_fingerprint, cross_reference_ids, advanced_metadata
}

export interface SubmissionData {
  url: string
  submissionType: 'VIP' | 'GA' | 'Free' | 'Skip'
  userId?: string
  userEmail?: string
  notes?: string
}

// Type alias for metadata - same as StayOnBeatTrackCard
export type TrackMetadata = StayOnBeatTrackCard

/**
 * Choose the appropriate database client based on environment and availability
 */
function getDatabaseClient() {
  // Priority: Neon > Local > Supabase
  if (process.env.DATABASE_URL) {
    console.log('🚀 Using Neon database')
    return neonClient
  } else if (shouldUseLocalDatabase()) {
    console.log('📁 Using local database')
    return localDatabase
  } else {
    console.log('☁️ Using Supabase database')
    return supabase
  }
}

/**
 * Main URL processing function
 * This is the entry point for all URL submissions
 */
export async function processUrl(submissionData: SubmissionData): Promise<ProcessingResult> {
  console.log('🔄 Starting URL processing for:', submissionData.url)

  try {
    // Step 1: Platform Detection
    const platformDetection = detectPlatform(submissionData.url)

    if (!platformDetection.isValid) {
      return {
        success: false,
        error: platformDetection.error || 'Invalid URL format',
        processingStatus: 'failed',
        platformDetection
      }
    }

    console.log('✅ Platform detected:', platformDetection.platform)

    // Step 2: Create initial submission record
    const submissionId = await createInitialSubmission(submissionData, platformDetection)

    if (!submissionId) {
      return {
        success: false,
        error: 'Failed to create submission record',
        processingStatus: 'failed',
        platformDetection
      }
    }

    console.log('✅ Initial submission created:', submissionId)

    // Step 3: Update status to processing
    await updateSubmissionStatus(submissionId, 'processing')

    // Step 4: Extract metadata based on platform
    let metadata: TrackMetadata | null = null

    try {
      metadata = await extractMetadata(platformDetection)
      console.log('✅ Metadata extracted:', metadata?.title)
    } catch (metadataError) {
      console.error('❌ Metadata extraction failed:', metadataError)

      // Create fallback metadata
      metadata = createFallbackMetadata(platformDetection)
      console.log('⚠️ Using fallback metadata')
    }

    // Step 5: Create TrackCardSubmission (FREE TIER)
    if (metadata) {
      // Create the standardized TrackCardSubmission format
      const trackCardSubmission = createTrackCardSubmission(metadata, {
        original_url: submissionData.url,
        show_id: "stayonbeat_episode_001", // FREE TIER: Static show ID
        submission_type: submissionData.submissionType,
        user_id: submissionData.userId,
        user_email: submissionData.userEmail
      })

      // Save TrackCardSubmission (FREE TIER: database storage)
      const saveResult = await saveTrackCardSubmission(trackCardSubmission, 'database')
      console.log('💾 TrackCardSubmission saved:', saveResult.file_path)

      // Update submission with metadata
      await updateSubmissionWithMetadata(submissionId, metadata)
      await updateSubmissionStatus(submissionId, 'completed')

      console.log('✅ URL processing completed successfully')

      return {
        success: true,
        submissionId,
        trackCard: metadata,
        processingStatus: 'completed',
        platformDetection
      }
    } else {
      await updateSubmissionStatus(submissionId, 'failed')

      return {
        success: false,
        error: 'Failed to extract metadata',
        processingStatus: 'failed',
        platformDetection
      }
    }

  } catch (error) {
    console.error('❌ URL processing failed:', error)

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown processing error',
      processingStatus: 'failed',
      platformDetection: detectPlatform(submissionData.url)
    }
  }
}

/**
 * Create initial submission record in database
 */
async function createInitialSubmission(
  submissionData: SubmissionData,
  platformDetection: PlatformDetectionResult
): Promise<string | null> {
  try {
    // Choose database based on availability
    const database = getDatabaseClient()

    console.log('💾 Creating submission in database...')

    // Use enhanced schema with all new columns
    const { data, error } = await database
      .from('submissions')
      .insert({
        url: submissionData.url,
        media_url: platformDetection.normalizedUrl || submissionData.url,
        submission_type: submissionData.submissionType,
        platform: platformDetection.platform,
        platform_id: platformDetection.platformId,
        content_type: platformDetection.contentType,
        status: 'pending',
        processing_status: 'pending',
        metadata_extracted: false,
        user_id: submissionData.userId,
        user_email: submissionData.userEmail,
        notes: submissionData.notes,
        artist_name: 'Processing...',
        song_title: 'Extracting metadata...',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single()

    if (error) {
      console.error('Database error creating submission:', error)
      return null
    }

    return data.id
  } catch (error) {
    console.error('Error creating initial submission:', error)
    return null
  }
}

/**
 * Update submission processing status
 */
async function updateSubmissionStatus(
  submissionId: string,
  status: 'pending' | 'processing' | 'completed' | 'failed'
): Promise<void> {
  try {
    // Choose database based on availability
    const database = getDatabaseClient()

    console.log(`💾 Updating submission ${submissionId} status to ${status}`)

    // Update both processing_status and status fields
    const statusMapping = {
      'pending': 'pending',
      'processing': 'pending',
      'completed': 'confirmed',
      'failed': 'rejected'
    }

    const { error } = await database
      .from('submissions')
      .update({
        processing_status: status,
        status: statusMapping[status],
        updated_at: new Date().toISOString()
      })
      .eq('id', submissionId)

    if (error) {
      console.error('Error updating submission status:', error)
    }
  } catch (error) {
    console.error('Error updating submission status:', error)
  }
}

/**
 * Extract metadata from platform APIs
 * This will be enhanced in Phase 3 with actual API integrations
 */
async function extractMetadata(platformDetection: PlatformDetectionResult): Promise<TrackMetadata | null> {
  // For now, create enhanced placeholder metadata based on platform
  // This will be replaced with actual API calls in Phase 3

  const platformMetadata = {
    youtube: {
      title: 'YouTube Track',
      artist: 'YouTube Artist',
      duration: 180,
      genre: 'Unknown'
    },
    spotify: {
      title: 'Spotify Track',
      artist: 'Spotify Artist',
      duration: 200,
      genre: 'Pop'
    },
    soundcloud: {
      title: 'SoundCloud Track',
      artist: 'SoundCloud Artist',
      duration: 240,
      genre: 'Electronic'
    },
    bandcamp: {
      title: 'Bandcamp Track',
      artist: 'Bandcamp Artist',
      duration: 220,
      genre: 'Indie'
    },
    local: {
      title: 'Local Track',
      artist: 'Local Artist',
      duration: 180,
      genre: 'Unknown'
    },
    unknown: {
      title: 'Unknown Track',
      artist: 'Unknown Artist',
      duration: 180,
      genre: 'Unknown'
    }
  }

  const baseMetadata = platformMetadata[platformDetection.platform]

  return {
    title: baseMetadata.title,
    artist: baseMetadata.artist,
    duration: baseMetadata.duration,
    genre: baseMetadata.genre,
    platform: platformDetection.platform,
    platformId: platformDetection.platformId || 'unknown',
    originalUrl: platformDetection.originalUrl,
    artworkUrl: 'https://placehold.co/400x400/purple/white?text=StayOnBeat',
    isExplicit: false
  }
}

/**
 * Create fallback metadata when extraction fails
 */
function createFallbackMetadata(platformDetection: PlatformDetectionResult): TrackMetadata {
  return {
    title: `${platformDetection.platform.charAt(0).toUpperCase() + platformDetection.platform.slice(1)} Track`,
    artist: `${platformDetection.platform.charAt(0).toUpperCase() + platformDetection.platform.slice(1)} Artist`,
    duration: 180,
    platform: platformDetection.platform,
    platformId: platformDetection.platformId || 'unknown',
    originalUrl: platformDetection.originalUrl,
    artworkUrl: 'https://placehold.co/400x400/gray/white?text=No+Metadata',
    genre: 'Unknown',
    isExplicit: false
  }
}

/**
 * Update submission record with extracted metadata
 */
async function updateSubmissionWithMetadata(
  submissionId: string,
  metadata: TrackMetadata
): Promise<void> {
  try {
    // Choose database based on availability
    const database = getDatabaseClient()

    console.log(`💾 Updating submission ${submissionId} with metadata:`, {
      title: metadata.title,
      artist: metadata.artist,
      platform: metadata.platform
    })

    // Use enhanced schema with all metadata columns
    const { error } = await database
      .from('submissions')
      .update({
        artist_name: metadata.artist,
        song_title: metadata.title,
        duration: metadata.duration,
        artwork_url: metadata.artworkUrl,
        album_name: metadata.albumName,
        genre: metadata.genre,
        is_explicit: metadata.isExplicit,
        stream_url: metadata.streamUrl,
        metadata_extracted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', submissionId)

    if (error) {
      console.error('Error updating submission with metadata:', error)
    }
  } catch (error) {
    console.error('Error updating submission with metadata:', error)
  }
}

/**
 * Get processing status for a submission
 */
export async function getProcessingStatus(submissionId: string): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from('submissions')
      .select('processing_status')
      .eq('id', submissionId)
      .single()

    if (error) {
      console.error('Error getting processing status:', error)
      return null
    }

    return data.processing_status
  } catch (error) {
    console.error('Error getting processing status:', error)
    return null
  }
}
