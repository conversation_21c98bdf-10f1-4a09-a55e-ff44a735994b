/**
 * Database Authentication for StayOnBeat
 * Integrates Clerk authentication with Neon database RLS
 */

import { neon } from '@neondatabase/serverless'
import { auth } from '@clerk/nextjs/server'

// Neon connection strings for different use cases
const ownerConnectionString = process.env.DATABASE_URL
const authenticatedConnectionString = process.env.DATABASE_AUTHENTICATED_URL
const publicConnectionString = process.env.NEXT_PUBLIC_DATABASE_AUTHENTICATED_URL

/**
 * Creates a Neon client with Clerk authentication for server components
 * This uses the authenticated connection string with JWT token
 */
export async function createServerDatabaseClient() {
  if (!authenticatedConnectionString) {
    throw new Error('DATABASE_AUTHENTICATED_URL is not configured')
  }

  // Get the JWT token from Clerk
  const token = await auth().getToken()
  
  if (!token) {
    // Return unauthenticated client for public data
    return neon(authenticatedConnectionString)
  }

  // Create authenticated Neon client with JWT
  // Note: This would require Neon to support JWT authentication
  // For now, we'll use the standard connection
  return neon(authenticatedConnectionString)
}

/**
 * Creates a Neon client for client components
 * Uses public connection string (if available)
 */
export function createClientDatabaseClient() {
  const connectionString = publicConnectionString || authenticatedConnectionString
  
  if (!connectionString) {
    throw new Error('No public database connection string configured')
  }

  return neon(connectionString)
}

/**
 * Creates an admin Neon client for migrations and admin operations
 * Uses the owner connection string
 */
export function createAdminDatabaseClient() {
  if (!ownerConnectionString) {
    throw new Error('DATABASE_URL (owner) is not configured')
  }

  return neon(ownerConnectionString)
}

/**
 * Get the current user from Clerk
 */
export async function getCurrentUser() {
  try {
    const { userId } = await auth()
    return userId
  } catch (error) {
    console.error("Error getting current user:", error)
    return null
  }
}

/**
 * Create a database client that automatically selects the right connection
 * based on the context (server vs client, authenticated vs public)
 */
export async function getDatabaseClient(context: 'server' | 'client' | 'admin' = 'server') {
  switch (context) {
    case 'admin':
      return createAdminDatabaseClient()
    case 'client':
      return createClientDatabaseClient()
    case 'server':
    default:
      return await createServerDatabaseClient()
  }
}

/**
 * Execute a query with proper authentication context
 */
export async function executeQuery(
  query: string, 
  params: any[] = [], 
  context: 'server' | 'client' | 'admin' = 'server'
) {
  const sql = await getDatabaseClient(context)
  
  if (params.length > 0) {
    // For parameterized queries, we'd need to use a different approach
    // Neon serverless uses template literals
    console.warn('Parameterized queries not yet implemented for Neon serverless')
  }
  
  return await sql`${query}`
}

/**
 * Test database connection
 */
export async function testDatabaseConnection(context: 'server' | 'client' | 'admin' = 'server') {
  try {
    console.log(`🔍 Testing ${context} database connection...`)
    const sql = await getDatabaseClient(context)
    const result = await sql`SELECT 1 as test, current_user, current_database()`
    console.log(`✅ ${context} database connection successful:`, result[0])
    return true
  } catch (error) {
    console.error(`❌ ${context} database connection failed:`, error)
    return false
  }
}
