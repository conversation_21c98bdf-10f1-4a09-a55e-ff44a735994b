export interface Submission {
  id: number
  artistName: string
  artistId?: string
  songTitle: string
  submissionType: "VIP" | "Skip" | "GA" | "Free"
  submissionTime: string
  status: "pending" | "confirmed" | "rejected"
  mediaUrl?: string
  notes?: string
  platform?: string
  url?: string
  images?: string[]
  previousSubmissions?: { title: string; date: string }[]
  spotifyId?: string
  isrc?: string
}

export interface PlaylistItem {
  id: number
  position: number
  artistName: string
  songTitle: string
  type: "VIP" | "Skip" | "GA" | "Free"
  mediaUrl?: string
  platform?: string
  url?: string
  spotifyId?: string
  isrc?: string
  albumName?: string
  artworkUrl?: string | null
  duration?: number
  submissionTime?: string
}

export interface PlayedTrack {
  id: number
  artistName: string
  songTitle: string
  playedAt: string
  feedback: string
  feedbackSent: boolean
  platform?: string
  url?: string
  spotifyId?: string
  isrc?: string
}

export interface Artist {
  id: string
  name: string
  bio: string
  profileImage: string
  coverImages?: string[]
  socialLinks: {
    instagram?: string
    twitter?: string
    website?: string
  }
  submissions: {
    id: number
    songTitle: string
    submissionType: string
    submissionTime: string
    status: string
    platform: string
    url: string
  }[]
}
