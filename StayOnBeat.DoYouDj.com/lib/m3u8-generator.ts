import { supabase } from "@/lib/supabase"
import type { PlaylistItem } from "@/lib/types"

/**
 * Fetches playlist data from Supabase and converts it to PlaylistItem array
 *
 * @param playlistId ID of the playlist to fetch
 * @returns Promise with array of PlaylistItem objects
 */
export async function fetchPlaylistFromSupabase(playlistId: string): Promise<PlaylistItem[]> {
  try {
    // Fetch playlist items with song data
    const { data, error } = await supabase
      .from("playlist_items")
      .select(`
        id,
        position,
        songs (
          id,
          title,
          artist_name,
          media_url,
          duration,
          album_name,
          artwork_url
        )
      `)
      .eq("playlist_id", playlistId)
      .order("position", { ascending: true })

    if (error) {
      console.error("Error fetching playlist:", error)
      return []
    }

    // Map the data to PlaylistItem format
    return data.map((item) => ({
      id: item.songs.id,
      position: item.position,
      artistName: item.songs.artist_name,
      songTitle: item.songs.title,
      albumName: item.songs.album_name || "",
      mediaUrl: item.songs.media_url,
      duration: item.songs.duration || 0,
      artworkUrl: item.songs.artwork_url || null,
      platform: "supabase",
      type: "track",
      submissionTime: new Date().toISOString(),
      url: item.songs.media_url,
    }))
  } catch (error) {
    console.error("Error in fetchPlaylistFromSupabase:", error)
    return []
  }
}

/**
 * Fetches all playlists from Supabase
 *
 * @returns Promise with array of playlist objects
 */
export async function fetchAllPlaylists() {
  try {
    const { data, error } = await supabase.from("playlists").select("*").order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching playlists:", error)
      return []
    }

    return data
  } catch (error) {
    console.error("Error in fetchAllPlaylists:", error)
    return []
  }
}

// Add a new function to fetch playlists by visibility
/**
 * Fetches playlists from Supabase filtered by visibility
 *
 * @param visibility The visibility filter ('public', 'private', or 'admin')
 * @returns Promise with array of playlist objects
 */
export async function fetchPlaylistsByVisibility(visibility = "public") {
  try {
    const { data, error } = await supabase
      .from("playlists")
      .select("*")
      .eq("visibility", visibility)
      .order("created_at", { ascending: false })

    if (error) {
      console.error(`Error fetching ${visibility} playlists:`, error)
      return []
    }

    return data
  } catch (error) {
    console.error(`Error in fetchPlaylistsByVisibility:`, error)
    return []
  }
}

/**
 * Generates an M3U8 playlist string from Supabase data
 *
 * @param playlistId ID of the playlist to generate
 * @returns Promise with the M3U8 content string
 */
export async function generateM3U8FromSupabase(playlistId: string): Promise<string> {
  try {
    // Get playlist details
    const { data: playlistData, error: playlistError } = await supabase
      .from("playlists")
      .select("name")
      .eq("id", playlistId)
      .single()

    if (playlistError) {
      console.error("Error fetching playlist details:", playlistError)
      return ""
    }

    // Get playlist items
    const playlistItems = await fetchPlaylistFromSupabase(playlistId)

    if (playlistItems.length === 0) {
      return ""
    }

    // Generate M3U8 content
    let content = "#EXTM3U\n"
    content += `#PLAYLIST:${playlistData.name}\n`

    playlistItems.forEach((item) => {
      // Add track info with duration if available
      const durationStr = item.duration ? `,${item.duration}` : ""
      content += `#EXTINF:${durationStr},${item.artistName} - ${item.songTitle}\n`

      // Add track URL
      content += `${item.mediaUrl}\n`
    })

    return content
  } catch (error) {
    console.error("Error generating M3U8:", error)
    return ""
  }
}

/**
 * Creates a Blob URL for an M3U8 playlist
 *
 * @param content M3U8 content string
 * @returns Blob URL that can be used as a source for HLS players
 */
export function createM3U8BlobUrl(content: string): string {
  const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })
  return URL.createObjectURL(blob)
}

/**
 * Fetches playlist data and creates a Blob URL for the M3U8 playlist
 *
 * @param playlistId ID of the playlist to fetch
 * @returns Promise with the Blob URL
 */
export async function getM3U8BlobUrlFromSupabase(playlistId: string): Promise<string> {
  const m3u8Content = await generateM3U8FromSupabase(playlistId)
  if (!m3u8Content) {
    return ""
  }
  return createM3U8BlobUrl(m3u8Content)
}

/**
 * Downloads an M3U8 playlist file
 *
 * @param playlistId ID of the playlist to download
 * @param filename Optional filename for the download
 */
export async function downloadM3U8FromSupabase(playlistId: string, filename?: string): Promise<void> {
  try {
    // Get playlist details for the filename if not provided
    if (!filename) {
      const { data, error } = await supabase.from("playlists").select("name").eq("id", playlistId).single()

      if (error) throw error

      filename = `${data.name.replace(/\s+/g, "-").toLowerCase()}.m3u8`
    }

    const m3u8Content = await generateM3U8FromSupabase(playlistId)
    if (!m3u8Content) {
      throw new Error("Failed to generate M3U8 content")
    }

    const blob = new Blob([m3u8Content], { type: "application/vnd.apple.mpegurl" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }, 100)
  } catch (error) {
    console.error("Error downloading M3U8:", error)
  }
}
