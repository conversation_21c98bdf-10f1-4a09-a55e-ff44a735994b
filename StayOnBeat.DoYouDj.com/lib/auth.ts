import type { SocialPlatform, AuthState } from "./types/auth"
import { supabase } from "./supabase"

// Base redirect URI for OAuth callbacks
const REDIRECT_URI = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000/auth/callback"

// Platform configurations
export const platformConfigs = {
  spotify: {
    name: "Spotify",
    provider: "spotify",
    scope: "user-read-private user-read-email",
    color: "from-green-500 to-green-700",
    hoverColor: "from-green-600 to-green-800",
    icon: "/icons/spotify.svg",
  },
  apple: {
    name: "Apple Music",
    provider: "apple",
    scope: "name email",
    color: "from-gray-200 to-gray-500",
    hoverColor: "from-gray-300 to-gray-600",
    icon: "/icons/apple.svg",
  },
  amazon: {
    name: "Amazon",
    provider: "amazon",
    scope: "profile",
    color: "from-orange-400 to-yellow-500",
    hoverColor: "from-orange-500 to-yellow-600",
    icon: "/icons/amazon.svg",
  },
  deezer: {
    name: "<PERSON><PERSON>",
    provider: "deezer",
    scope: "basic_access,email",
    color: "from-purple-500 to-purple-700",
    hoverColor: "from-purple-600 to-purple-800",
    icon: "/icons/deezer.svg",
  },
  instagram: {
    name: "Instagram",
    provider: "instagram",
    scope: "user_profile,user_media",
    color: "from-pink-500 to-purple-500",
    hoverColor: "from-pink-600 to-purple-600",
    icon: "/icons/instagram.svg",
  },
  tiktok: {
    name: "TikTok",
    provider: "tiktok",
    scope: "user.info.basic",
    color: "from-black to-gray-800",
    hoverColor: "from-gray-900 to-gray-700",
    icon: "/icons/tiktok.svg",
  },
  youtube: {
    name: "YouTube",
    provider: "youtube",
    scope: "https://www.googleapis.com/auth/youtube.readonly",
    color: "from-red-500 to-red-700",
    hoverColor: "from-red-600 to-red-800",
    icon: "/icons/youtube.svg",
  },
  soundcloud: {
    name: "SoundCloud",
    provider: "soundcloud",
    scope: "non-expiring",
    color: "from-orange-500 to-red-500",
    hoverColor: "from-orange-600 to-red-600",
    icon: "/icons/soundcloud.svg",
  },
  facebook: {
    name: "Facebook",
    provider: "facebook",
    scope: "email,public_profile",
    color: "from-blue-500 to-blue-700",
    hoverColor: "from-blue-600 to-blue-800",
    icon: "/icons/facebook.svg",
  },
  twitter: {
    name: "X / Twitter",
    provider: "twitter",
    scope: "users.read tweet.read",
    color: "from-gray-700 to-gray-900",
    hoverColor: "from-gray-800 to-black",
    icon: "/icons/twitter.svg",
  },
}

// Function to initiate OAuth flow
export async function initiateOAuth(platform: SocialPlatform) {
  try {
    // Use the appropriate client based on environment
    const supabaseClient = process.env.NODE_ENV === "development" ? client : supabase

    // In development mode with mock client, just log the attempt
    if (process.env.NODE_ENV === "development") {
      console.log(`Development mode: OAuth redirect would happen here for ${platform}`)
      return { provider: platform, url: "#" }
    }

    const config = platformConfigs[platform]
    const { data, error } = await supabaseClient.auth.signInWithOAuth({
      provider: config.provider as any,
      options: {
        redirectTo: REDIRECT_URI,
        scopes: config.scope,
      },
    })

    if (error) throw error
    return data
  } catch (error) {
    console.error(`Error initiating OAuth with ${platform}:`, error)
    throw error
  }
}

// Function to handle OAuth callback
export async function handleAuthCallback(code: string, state: string) {
  try {
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    if (error) throw error
    return data
  } catch (error) {
    console.error("Error handling auth callback:", error)
    throw error
  }
}
