import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET() {
  try {
    // Check submissions table
    const { data: submissions, error: submissionsError } = await supabase
      .from('submissions')
      .select('*')
      .order('created_at', { ascending: false })

    if (submissionsError) throw submissionsError

    // Check playlist table
    const { data: playlist, error: playlistError } = await supabase
      .from('playlist')
      .select('*')
      .order('position', { ascending: true })

    if (playlistError) throw playlistError

    // Check storage buckets
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets()

    if (bucketsError) throw bucketsError

    // Check files in tracks bucket if it exists
    let trackFiles = []
    const tracksBucket = buckets.find(bucket => bucket.name === 'tracks')
    if (tracksBucket) {
      const { data: files, error: filesError } = await supabase
        .storage
        .from('tracks')
        .list()
      
      if (!filesError) {
        trackFiles = files || []
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        submissions: submissions || [],
        playlist: playlist || [],
        storage: {
          buckets: buckets || [],
          trackFiles: trackFiles
        }
      }
    })
    
  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
