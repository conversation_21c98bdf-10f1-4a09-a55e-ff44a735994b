import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST() {
  try {
    // Get current max position
    const { data: maxPositionData, error: maxPositionError } = await supabase
      .from("playlist")
      .select("position")
      .order("position", { ascending: false })
      .limit(1)

    if (maxPositionError) throw maxPositionError

    const nextPosition = maxPositionData && maxPositionData.length > 0 ? maxPositionData[0].position + 1 : 1

    // Add a test track to the playlist
    const { data, error } = await supabase
      .from("playlist")
      .insert({
        position: nextPosition,
        artist_name: "Test Artist",
        song_title: "Test Song",
        type: "Free",
        url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
        media_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()

    if (error) throw error

    return NextResponse.json({ 
      success: true, 
      message: "Test track added to playlist",
      track: data[0] 
    })
  } catch (error) {
    console.error("Error adding test track:", error)
    return NextResponse.json(
      { error: "Failed to add test track" },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Get all tracks from playlist
    const { data, error } = await supabase
      .from("playlist")
      .select("*")
      .order("position", { ascending: true })

    if (error) throw error

    return NextResponse.json({ 
      success: true, 
      tracks: data || [],
      count: data?.length || 0
    })
  } catch (error) {
    console.error("Error fetching playlist:", error)
    return NextResponse.json(
      { error: "Failed to fetch playlist" },
      { status: 500 }
    )
  }
}
