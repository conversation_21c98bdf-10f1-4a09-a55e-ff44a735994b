import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function POST() {
  try {
    // Clear existing data
    await supabase.from('playlist').delete().neq('id', 0)
    await supabase.from('submissions').delete().neq('id', 0)

    // Your real URLs
    const urls = [
      'https://on.soundcloud.com/dvpjnwPokiHuvvtG9',
      'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW',
      'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185',
      'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW'
    ]

    // Insert submissions
    const submissionsData = urls.map((url, index) => ({
      artist_name: `Real Artist ${index + 1}`,
      song_title: `Real Track ${index + 1}`,
      submission_type: 'Free',
      url: url,
      media_url: url,
      status: 'confirmed',
      created_at: new Date().toISOString()
    }))

    const { data: submissions, error: submissionError } = await supabase
      .from('submissions')
      .insert(submissionsData)
      .select()

    if (submissionError) throw submissionError

    // Insert into playlist
    const playlistData = urls.map((url, index) => ({
      position: index + 1,
      artist_name: `Real Artist ${index + 1}`,
      song_title: `Real Track ${index + 1}`,
      type: 'Free',
      url: url,
      media_url: url,
      created_at: new Date().toISOString()
    }))

    const { data: playlist, error: playlistError } = await supabase
      .from('playlist')
      .insert(playlistData)
      .select()

    if (playlistError) throw playlistError

    return NextResponse.json({
      success: true,
      message: 'Test data inserted successfully',
      submissionsCount: submissions.length,
      playlistCount: playlist.length
    })

  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
