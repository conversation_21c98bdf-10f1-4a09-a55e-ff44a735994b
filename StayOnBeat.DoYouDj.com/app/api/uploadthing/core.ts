import { createUploadthing, type FileRouter } from "uploadthing/next"
import { createClient } from "@supabase/supabase-js"

// Initialize Supabase client for server-side operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper function to get the current user from a request
async function auth(req: Request) {
  const authHeader = req.headers.get("authorization")
  if (!authHeader) {
    // For development, return a mock user if no auth header is present
    if (process.env.NODE_ENV === "development") {
      return { id: "dev-user-id" }
    }
    throw new Error("Unauthorized: No authorization header")
  }

  try {
    // Get the token from the Authorization header
    const token = authHeader.replace("Bearer ", "")

    // Verify the token with Supabase
    const { data, error } = await supabase.auth.getUser(token)

    if (error || !data.user) {
      throw new Error("Unauthorized: Invalid token")
    }

    return data.user
  } catch (error) {
    console.error("Authentication error:", error)
    throw new Error("Unauthorized: Authentication failed")
  }
}

const f = createUploadthing()

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Define as many FileRoutes as you need, each with a unique route key
  imageUploader: f({ image: { maxFileSize: "4KB", maxFileCount: 10 } })
    .middleware(({ req }) => {
      // This code runs on your server before upload
      const user = { id: "user-id" } // Replace with actual user data

      // If you throw, the user will not be able to upload
      if (!user) throw new Error("Unauthorized")

      // Whatever is returned here is accessible in onUploadComplete as `metadata`
      return { userId: user.id }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId)

      // !!! Whatever is returned here is sent to the clientside `onClientUploadComplete` callback
      return { uploadedBy: metadata.userId, url: file.url }
    }),

  // Specific route for background images
  backgroundImage: f({ image: { maxFileSize: "4KB", maxFileCount: 1 } })
    .middleware(() => {
      return { type: "background" }
    })
    .onUploadComplete(async ({ file }) => {
      return { url: file.url }
    }),

  // Specific route for profile images
  profileImage: f({ image: { maxFileSize: "4KB", maxFileCount: 1 } })
    .middleware(() => {
      return { type: "profile" }
    })
    .onUploadComplete(async ({ file }) => {
      return { url: file.url }
    }),

  // Specific route for logo images
  logoImage: f({ image: { maxFileSize: "4KB", maxFileCount: 1 } })
    .middleware(() => {
      return { type: "logo" }
    })
    .onUploadComplete(async ({ file }) => {
      return { url: file.url }
    }),

  // Audio file uploader for music tracks
  audioUploader: f({ audio: { maxFileSize: "50MB", maxFileCount: 1 } })
    .middleware(() => {
      return { type: "audio" }
    })
    .onUploadComplete(async ({ file }) => {
      console.log("Audio upload complete:", file.name)
      return { url: file.url }
    }),

  // Artwork uploader for track covers
  artworkUploader: f({ image: { maxFileSize: "2MB", maxFileCount: 1 } })
    .middleware(() => {
      return { type: "artwork" }
    })
    .onUploadComplete(async ({ file }) => {
      console.log("Artwork upload complete:", file.name)
      return { url: file.url }
    }),

  // M3U8 playlist file uploader
  m3u8Uploader: f({
    "text/plain": { maxFileSize: "1MB", maxFileCount: 1 },
    "application/vnd.apple.mpegurl": { maxFileSize: "1MB", maxFileCount: 1 },
    "audio/mpegurl": { maxFileSize: "1MB", maxFileCount: 1 }
  })
    .middleware(async ({ req }) => {
      try {
        // For development, allow uploads without authentication
        if (process.env.NODE_ENV === "development") {
          return { userId: "dev-user-id", type: "m3u8" }
        }

        // Get the user from the request
        const user = await auth(req)

        // If you throw, the user will not be able to upload
        if (!user) throw new Error("Unauthorized")

        // Return metadata for use in onUploadComplete
        return { userId: user.id, type: "m3u8" }
      } catch (error) {
        console.error("M3U8 upload middleware error:", error)
        throw new Error("Unauthorized: Please log in to upload M3U8 files")
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      try {
        console.log("M3U8 upload complete:", file.name)
        console.log("Uploaded by user:", metadata.userId)

        // Store reference in Supabase if needed
        // This could be used to associate the M3U8 file with a playlist
        const { data, error } = await supabase
          .from("playlist_files")
          .insert({
            user_id: metadata.userId,
            file_url: file.url,
            file_name: file.name,
            file_type: "m3u8",
            created_at: new Date().toISOString()
          })

        if (error) {
          console.error("Error storing M3U8 file reference:", error)
        }

        return {
          url: file.url,
          fileName: file.name,
          uploadedBy: metadata.userId
        }
      } catch (error) {
        console.error("Error in M3U8 onUploadComplete:", error)
        return { url: file.url }
      }
    }),
} satisfies FileRouter

export type OurFileRouter = typeof ourFileRouter
