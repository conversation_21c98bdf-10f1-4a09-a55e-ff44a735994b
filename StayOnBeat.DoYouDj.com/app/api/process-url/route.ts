/**
 * URL Processing API Endpoint for StayOnBeat
 * Main entry point for URL submission processing
 */

import { NextRequest, NextResponse } from 'next/server'
import { processUrl, type SubmissionData } from '@/lib/url-processor'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 URL Processing API called')
    
    // Parse request body
    const body = await request.json()
    
    // Validate required fields
    if (!body.url) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'URL is required' 
        },
        { status: 400 }
      )
    }

    if (!body.submissionType) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Submission type is required' 
        },
        { status: 400 }
      )
    }

    // Validate submission type
    const validTypes = ['VIP', 'GA', 'Free', 'Skip']
    if (!validTypes.includes(body.submissionType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid submission type. Must be VIP, GA, Free, or Skip' 
        },
        { status: 400 }
      )
    }

    // Prepare submission data
    const submissionData: SubmissionData = {
      url: body.url.trim(),
      submissionType: body.submissionType,
      userId: body.userId || null,
      userEmail: body.userEmail || null,
      notes: body.notes || null
    }

    console.log('📝 Processing submission:', {
      url: submissionData.url,
      type: submissionData.submissionType,
      userId: submissionData.userId
    })

    // Process the URL
    const result = await processUrl(submissionData)

    if (result.success) {
      console.log('✅ URL processing completed successfully')
      
      return NextResponse.json({
        success: true,
        message: 'URL processed successfully',
        data: {
          submissionId: result.submissionId,
          metadata: result.metadata,
          platform: result.platformDetection?.platform,
          processingStatus: result.processingStatus
        }
      })
    } else {
      console.log('❌ URL processing failed:', result.error)
      
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          data: {
            platform: result.platformDetection?.platform,
            processingStatus: result.processingStatus
          }
        },
        { status: 422 }
      )
    }

  } catch (error) {
    console.error('❌ URL Processing API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during URL processing' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const submissionId = searchParams.get('submissionId')

    if (!submissionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Submission ID is required' 
        },
        { status: 400 }
      )
    }

    // Get processing status
    const { getProcessingStatus } = await import('@/lib/url-processor')
    const status = await getProcessingStatus(submissionId)

    if (!status) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Submission not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        submissionId,
        processingStatus: status
      }
    })

  } catch (error) {
    console.error('❌ Get processing status error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
