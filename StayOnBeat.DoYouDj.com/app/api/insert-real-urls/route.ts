import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Platform detection function
function detectPlatform(url: string): string {
  if (url.includes('soundcloud.com')) return 'soundcloud'
  if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube'
  if (url.includes('spotify.com')) return 'spotify'
  if (url.includes('bandcamp.com')) return 'bandcamp'
  return 'unknown'
}

// Extract metadata from URL (simplified version)
function extractMetadata(url: string, index: number) {
  const platform = detectPlatform(url)

  // In a real implementation, you'd call the respective APIs
  // For now, we'll use the platform info to create better names
  const platformNames = {
    soundcloud: 'SoundCloud Artist',
    youtube: 'YouTube Artist',
    spotify: 'Spotify Artist',
    bandcamp: 'Bandcamp Artist',
    unknown: 'Unknown Artist'
  }

  const trackNames = {
    soundcloud: 'SoundCloud Track',
    youtube: 'YouTube Track',
    spotify: 'Spotify Track',
    bandcamp: 'Bandcamp Track',
    unknown: 'Unknown Track'
  }

  return {
    artist_name: `${platformNames[platform]} ${index + 1}`,
    song_title: `${trackNames[platform]} ${index + 1}`,
    platform: platform
  }
}

export async function POST() {
  try {
    console.log('🔄 Processing real URLs with metadata extraction...')

    // Clear existing data
    await supabase.from('playlist').delete().neq('id', 0)
    await supabase.from('submissions').delete().neq('id', 0)

    console.log('✅ Cleared existing data')

    // Your real URLs
    const urls = [
      'https://on.soundcloud.com/dvpjnwPokiHuvvtG9',
      'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW',
      'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185',
      'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW'
    ]

    // Process each URL with metadata extraction
    const submissionsData = urls.map((url, index) => {
      const metadata = extractMetadata(url, index)
      return {
        artist_name: metadata.artist_name,
        song_title: metadata.song_title,
        submission_type: 'Free',
        platform: metadata.platform,
        url: url,
        media_url: url,
        status: 'confirmed',
        created_at: new Date().toISOString()
      }
    })

    const { data: submissions, error: submissionError } = await supabase
      .from('submissions')
      .insert(submissionsData)
      .select()

    if (submissionError) throw submissionError
    console.log(`Inserted ${submissions.length} submissions`)

    // Insert into playlist
    const playlistData = urls.map((url, index) => ({
      position: index + 1,
      artist_name: `Real Artist ${index + 1}`,
      song_title: `Real Track ${index + 1}`,
      type: 'Free',
      url: url,
      media_url: url,
      created_at: new Date().toISOString()
    }))

    const { data: playlist, error: playlistError } = await supabase
      .from('playlist')
      .insert(playlistData)
      .select()

    if (playlistError) throw playlistError
    console.log(`Inserted ${playlist.length} playlist items`)

    return NextResponse.json({
      success: true,
      message: 'Successfully inserted real URLs into database',
      playlistName: '2025-05-26 Episode#Test001',
      submissionsCount: submissions.length,
      playlistCount: playlist.length
    })

  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
