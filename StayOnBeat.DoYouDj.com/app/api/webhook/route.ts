import { NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Define webhook payload schema
const webhookSchema = z.object({
  event: z.string(),
  data: z.record(z.unknown()),
  timestamp: z.string().datetime(),
});

export async function POST(request: Request) {
  try {
    // Get the webhook signature from headers
    const signature = request.headers.get('x-webhook-signature');
    
    // Verify webhook signature (implement your verification logic here)
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = webhookSchema.safeParse(body);

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Invalid webhook payload', details: validatedData.error },
        { status: 400 }
      );
    }

    // Store webhook event in Supabase
    const { error } = await supabase
      .from('webhook_events')
      .insert({
        event_type: validatedData.data.event,
        payload: validatedData.data.data,
        received_at: validatedData.data.timestamp,
        signature: signature,
      });

    if (error) {
      console.error('Error storing webhook event:', error);
      return NextResponse.json(
        { error: 'Failed to process webhook' },
        { status: 500 }
      );
    }

    // Process the webhook based on event type
    switch (validatedData.data.event) {
      case 'user.created':
        // Handle user creation
        break;
      case 'user.updated':
        // Handle user update
        break;
      // Add more event handlers as needed
      default:
        console.log(`Unhandled event type: ${validatedData.data.event}`);
    }

    return NextResponse.json({ status: 'success' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 