'use client'

import { 
  <PERSON><PERSON><PERSON>, 
  Tilt_Neon, 
  <PERSON><PERSON><PERSON>_Glitch, 
  <PERSON><PERSON><PERSON>_Burned,
  <PERSON><PERSON>,
  <PERSON><PERSON>opate,
  Chakra_Petch,
  Turret_Road,
  Michroma,
  Rajdhani,
  Exo_2,
  Bai_Jamjuree,
  Syne_Mono,
  Monoton,
  <PERSON><PERSON>t_One,
  <PERSON>_<PERSON>o_Display
} from 'next/font/google'

// Current fonts
const orbitron = Orbitron({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-orbitron',
})

const tiltNeon = Tilt_Neon({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-tilt-neon',
})

const rubikGlitch = Rubik_Glitch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-glitch',
  weight: ['400'],
})

const rubikBurned = Rubik_Burned({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-burned',
  weight: ['400'],
})

// Suggested fonts
const audiowide = Audiowide({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-audiowide',
  weight: ['400'],
})

const syncopate = Syncopate({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syncopate',
  weight: ['400', '700'],
})

const chakraPetch = Chakra_Petch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-chakra-petch',
  weight: ['400', '700'],
})

const turretRoad = Turret_Road({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-turret-road',
  weight: ['400', '700'],
})

const michroma = Michroma({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-michroma',
  weight: ['400'],
})

const rajdhani = Rajdhani({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rajdhani',
  weight: ['400', '700'],
})

const exo2 = Exo_2({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-exo2',
  weight: ['400', '700'],
})

const baiJamjuree = Bai_Jamjuree({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-bai-jamjuree',
  weight: ['400', '700'],
})

const syneMono = Syne_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syne-mono',
  weight: ['400'],
})

const monoton = Monoton({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-monoton',
  weight: ['400'],
})

const poiretOne = Poiret_One({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poiret-one',
  weight: ['400'],
})

const majorMonoDisplay = Major_Mono_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-major-mono-display',
  weight: ['400'],
})

export default function FontsPreviewPage() {
  return (
    <div className={`
      ${orbitron.variable} 
      ${tiltNeon.variable} 
      ${rubikGlitch.variable} 
      ${rubikBurned.variable}
      ${audiowide.variable}
      ${syncopate.variable}
      ${chakraPetch.variable}
      ${turretRoad.variable}
      ${michroma.variable}
      ${rajdhani.variable}
      ${exo2.variable}
      ${baiJamjuree.variable}
      ${syneMono.variable}
      ${monoton.variable}
      ${poiretOne.variable}
      ${majorMonoDisplay.variable}
      min-h-screen bg-black p-8
    `}>
      <h1 className="text-3xl text-white mb-8">Font Preview</h1>
      
      <div className="mb-12">
        <h2 className="text-2xl text-[#00ffff] mb-4">Current Fonts</h2>
        <div className="space-y-8">
          <div>
            <h3 className="text-xl text-white mb-2">Orbitron</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-orbitron)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Tilt Neon</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-tilt-neon)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Rubik Glitch (Neonderthaw)</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-rubik-glitch)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Rubik Burned (Neonify)</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-rubik-burned)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl text-[#00ffff] mb-4">Suggested Fonts</h2>
        <div className="space-y-8">
          <div>
            <h3 className="text-xl text-white mb-2">Audiowide</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-audiowide)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Syncopate</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-syncopate)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Chakra Petch</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-chakra-petch)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Turret Road</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-turret-road)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Michroma</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-michroma)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Rajdhani</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-rajdhani)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Exo 2</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-exo2)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Bai Jamjuree</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-bai-jamjuree)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Syne Mono</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-syne-mono)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Monoton</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-monoton)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Poiret One</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-poiret-one)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
          
          <div>
            <h3 className="text-xl text-white mb-2">Major Mono Display</h3>
            <p className="text-[#00ffff] text-2xl" style={{ fontFamily: 'var(--font-major-mono-display)' }}>
              The quick brown fox jumps over the lazy dog
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
