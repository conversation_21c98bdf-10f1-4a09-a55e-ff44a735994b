"use client"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-black via-purple-900 to-black relative overflow-hidden flex flex-col">
      {/* Add a horizon line effect */}
      <div
        className="absolute inset-0 bg-gradient-to-t from-transparent via-[#00ffff]/5 to-transparent h-full w-full"
        style={{
          transform: "perspective(1000px) rotateX(80deg)",
          transformOrigin: "bottom",
          bottom: "-5%",
          opacity: 0.7,
        }}
      ></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20"></div>

      {/* Main content */}
      <div className="w-full text-white py-16 flex-grow relative z-10 flex items-center justify-center">
        <div className="container mx-auto px-4 pt-14 pb-12 bg-black/40 backdrop-blur-sm border border-[#00ffff]/30 neon-box-cyan rounded-lg max-w-[81%] md:max-w-[725px] relative z-20">
          {/* Text moved to top */}
          <div className="text-center mb-12">
            <p
              className="text-3xl neon-text-cyan font-neonderthaw inline-block"
              style={{
                color: "#00ffff",
                textShadow: "0 0 5px #00ffff, 0 0 10px rgba(0, 255, 255, 0.5)",
              }}
            >
              Music Submission & Playlist System
            </p>
          </div>

          <div className="w-full max-w-[580px] mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full">
              {/* Login Card */}
              <Link href="/login">
                <div
                  className="bg-black/80 backdrop-blur-sm p-7 rounded-lg border-2 border-red-600 text-center hover:animate-flicker transition-all duration-300 shadow-lg aspect-square flex flex-col items-center justify-center mx-auto w-full max-w-[180px]"
                  style={{ boxShadow: "0 0 10px rgba(255, 0, 0, 0.7)" }}
                >
                  <div
                    className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 hover:animate-flicker transition-all duration-300 shadow-lg"
                    style={{
                      background:
                        "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                      boxShadow:
                        "inset 0 0 15px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.3)",
                      backdropFilter: "blur(4px)",
                      transform: "translateZ(0)",
                    }}
                  >
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background:
                          "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                        transform: "translateZ(5px)",
                      }}
                    ></div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white neon-text-red"
                    >
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <span className="text-white neon-text-red mt-4 font-medium">Login</span>
                </div>
              </Link>

              {/* Submit Card */}
              <Link href="/submit">
                <div
                  className="bg-black/80 backdrop-blur-sm p-7 rounded-lg border-2 border-red-600 text-center hover:animate-flicker transition-all duration-300 shadow-lg aspect-square flex flex-col items-center justify-center mx-auto w-full max-w-[180px]"
                  style={{ boxShadow: "0 0 10px rgba(255, 0, 0, 0.7)" }}
                >
                  <div
                    className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 hover:animate-flicker transition-all duration-300 shadow-lg"
                    style={{
                      background:
                        "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                      boxShadow:
                        "inset 0 0 15px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.3)",
                      backdropFilter: "blur(4px)",
                      transform: "translateZ(0)",
                    }}
                  >
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background:
                          "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                        transform: "translateZ(5px)",
                      }}
                    ></div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white neon-text-red"
                    >
                      <path d="M9 18V5l12-2v13"></path>
                      <circle cx="6" cy="18" r="3"></circle>
                      <circle cx="18" cy="16" r="3"></circle>
                    </svg>
                  </div>
                  <span className="text-white neon-text-red mt-4 font-medium">Submit</span>
                </div>
              </Link>

              {/* Queue Card */}
              <Link href="/queue">
                <div
                  className="bg-black/80 backdrop-blur-sm p-7 rounded-lg border-2 border-red-600 text-center hover:animate-flicker transition-all duration-300 shadow-lg aspect-square flex flex-col items-center justify-center mx-auto w-full max-w-[180px]"
                  style={{ boxShadow: "0 0 10px rgba(255, 0, 0, 0.7)" }}
                >
                  <div
                    className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 hover:animate-flicker transition-all duration-300 shadow-lg"
                    style={{
                      background:
                        "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                      boxShadow:
                        "inset 0 0 15px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.3)",
                      backdropFilter: "blur(4px)",
                      transform: "translateZ(0)",
                    }}
                  >
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background:
                          "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                        transform: "translateZ(5px)",
                      }}
                    ></div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white neon-text-red"
                    >
                      <line x1="8" y1="6" x2="21" y2="6"></line>
                      <line x1="8" y1="12" x2="21" y2="12"></line>
                      <line x1="8" y1="18" x2="21" y2="18"></line>
                      <line x1="3" y1="6" x2="3.01" y2="6"></line>
                      <line x1="3" y1="12" x2="3.01" y2="12"></line>
                      <line x1="3" y1="18" x2="3.01" y2="18"></line>
                    </svg>
                  </div>
                  <span className="text-white neon-text-red mt-4 font-medium">Queue</span>
                </div>
              </Link>

              {/* Player Card */}
              <Link href="/player">
                <div
                  className="bg-black/80 backdrop-blur-sm p-7 rounded-lg border-2 border-red-600 text-center hover:animate-flicker transition-all duration-300 shadow-lg aspect-square flex flex-col items-center justify-center mx-auto w-full max-w-[180px]"
                  style={{ boxShadow: "0 0 10px rgba(255, 0, 0, 0.7)" }}
                >
                  <div
                    className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 hover:animate-flicker transition-all duration-300 shadow-lg"
                    style={{
                      background:
                        "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                      boxShadow:
                        "inset 0 0 15px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.3)",
                      backdropFilter: "blur(4px)",
                      transform: "translateZ(0)",
                    }}
                  >
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background:
                          "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                        transform: "translateZ(5px)",
                      }}
                    ></div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white neon-text-red"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polygon points="10 8 16 12 10 16 10 8"></polygon>
                    </svg>
                  </div>
                  <span className="text-white neon-text-red mt-4 font-medium">Player</span>
                </div>
              </Link>
            </div>
          </div>

          {/* Admin Button */}
          <div className="mt-8 text-center">
            <Link href="/admin">
              <button
                className="px-6 py-2 rounded-md bg-black/80 border border-[#00ffff]/50 text-[#00ffff] hover:bg-black/60 transition-colors"
                style={{
                  boxShadow: "0 0 10px rgba(0, 255, 255, 0.3)",
                  textShadow: "0 0 5px rgba(0, 255, 255, 0.5)"
                }}
              >
                Admin Login
              </button>
            </Link>
          </div>

          {/* Add horizon grid lines */}
          <div
            className="absolute bottom-0 left-0 right-0 h-[30vh] z-0"
            style={{
              background:
                "linear-gradient(to top, rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(to right, rgba(0,255,255,0.1) 1px, transparent 1px)",
              backgroundSize: "40px 20px",
              transform: "perspective(500px) rotateX(60deg)",
              transformOrigin: "bottom",
              opacity: 0.2,
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}
