"use client"

import { useState, useEffect } from "react"
import { SocialLoginCard } from "@/components/auth/social-login-card"
import { AuthForm } from "@/components/auth/auth-form"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { PageTransition } from "@/components/page-transition"
// Remove direct NavigationDock import as it's handled by the layout
import { LoginOptionsLayover } from "@/components/login-options-layover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function LoginPage() {
  const [loading, setLoading] = useState(true)
  const [showLayover, setShowLayover] = useState(true)
  const [activeTab, setActiveTab] = useState<"connect" | "login">("connect")
  const router = useRouter()

  useEffect(() => {
    async function checkUser() {
      try {
        setLoading(true)

        // In development mode with DEV_AUTO_LOGIN enabled, bypass authentication
        if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true') {
          console.log('Development mode: Auto-login enabled, bypassing authentication')
          // Simulate a delay to avoid instant redirects
          setTimeout(() => {
            router.push("/profile")
          }, 1000)
          return
        }

        const { data: { session } } = await supabase.auth.getSession()
        if (session) {
          // Already logged in, redirect to profile
          router.push("/profile")
        }
      } catch (error) {
        console.error("Error checking user:", error)
      } finally {
        setLoading(false)
      }
    }

    checkUser()
  }, [router])

  const handleAuthSuccess = (userData: any) => {
    toast({
      title: "Authentication successful",
      description: `Connected as ${userData.email || "user"}`,
    })
    router.push("/profile")
  }

  const handleAuthError = (error: Error) => {
    console.error("Authentication error:", error)
    toast({
      title: "Authentication failed",
      description: "Could not connect to authentication service. Please try again later.",
      variant: "destructive",
    })
  }

  const handleOptionSelect = (option: "guest" | "login" | "connect" | "register") => {
    setShowLayover(false)
    if (option === "login" || option === "connect" || option === "register") {
      setActiveTab(option === "register" ? "login" : option)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-black to-purple-950">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-purple-500 border-t-transparent" />
          <p className="mt-4 text-purple-400">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-b from-black to-purple-950">
        {/* NavigationDock is now handled by the layout */}
        <main className="container mx-auto px-4 py-8">
          {showLayover ? (
            <LoginOptionsLayover onOptionSelect={handleOptionSelect} />
          ) : (
            <div className="mx-auto max-w-md">
              <h1 className="mb-8 text-center text-3xl font-bold text-white">
                {activeTab === "connect" ? "Connect Your Accounts" : "Login"}
              </h1>
              <Tabs defaultValue="email" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6 bg-black/40">
                  <TabsTrigger value="email" className="text-white data-[state=active]:bg-purple-900 data-[state=active]:text-white">Email Login</TabsTrigger>
                  <TabsTrigger value="social" className="text-white data-[state=active]:bg-purple-900 data-[state=active]:text-white">Social Login</TabsTrigger>
                </TabsList>

                <TabsContent value="email">
                  <AuthForm />
                </TabsContent>

                <TabsContent value="social">
                  <SocialLoginCard
                    onSuccess={handleAuthSuccess}
                    onError={handleAuthError}
                  />
                </TabsContent>
              </Tabs>
            </div>
          )}
        </main>
      </div>
    </PageTransition>
  )
}
