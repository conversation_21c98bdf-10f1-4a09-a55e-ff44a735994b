'use client'

import { useState, useEffect } from 'react'
import { 
  Orbitron, 
  Tilt_Neon, 
  Rubik_Glitch, 
  Rubik_Burned,
  Audiowide,
  Syncopate,
  Michroma,
  Exo_2,
  Syne_Mono,
  Monoton,
  <PERSON>iret_One,
  Major_Mono_Display
} from 'next/font/google'

// Load fonts
const orbitron = Orbitron({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-orbitron',
})

const tiltNeon = Tilt_Neon({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-tilt-neon',
})

const rubikGlitch = Rubik_Glitch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-glitch',
  weight: ['400'],
})

const rubikBurned = Rubik_Burned({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-burned',
  weight: ['400'],
})

const audiowide = Audiowide({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-audiowide',
  weight: ['400'],
})

const syncopate = Syncopate({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syncopate',
  weight: ['400', '700'],
})

const michroma = Michroma({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-michroma',
  weight: ['400'],
})

const exo2 = Exo_2({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-exo2',
  weight: ['400', '700'],
})

const syneMono = Syne_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syne-mono',
  weight: ['400'],
})

const monoton = Monoton({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-monoton',
  weight: ['400'],
})

const poiretOne = Poiret_One({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poiret-one',
  weight: ['400'],
})

const majorMonoDisplay = Major_Mono_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-major-mono-display',
  weight: ['400'],
})

// Font definitions
const fonts = [
  { name: 'Orbitron', variable: '--font-orbitron', weights: ['400', '500', '600', '700', '800', '900'] },
  { name: 'Tilt Neon', variable: '--font-tilt-neon', weights: ['400'] },
  { name: 'Rubik Glitch', variable: '--font-rubik-glitch', weights: ['400'] },
  { name: 'Rubik Burned', variable: '--font-rubik-burned', weights: ['400'] },
  { name: 'Audiowide', variable: '--font-audiowide', weights: ['400'] },
  { name: 'Syncopate', variable: '--font-syncopate', weights: ['400', '700'] },
  { name: 'Michroma', variable: '--font-michroma', weights: ['400'] },
  { name: 'Exo 2', variable: '--font-exo2', weights: ['400', '700'] },
  { name: 'Syne Mono', variable: '--font-syne-mono', weights: ['400'] },
  { name: 'Monoton', variable: '--font-monoton', weights: ['400'] },
  { name: 'Poiret One', variable: '--font-poiret-one', weights: ['400'] },
  { name: 'Major Mono Display', variable: '--font-major-mono-display', weights: ['400'] },
]

// HTML elements where fonts can be applied
const htmlElements = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'button', 'a']

export default function FontSelectorPage() {
  const [sampleText, setSampleText] = useState('The quick brown fox jumps over the lazy dog')
  const [fontSize, setFontSize] = useState(24)
  const [fontWeight, setFontWeight] = useState('400')
  const [glowIntensity, setGlowIntensity] = useState(5)
  const [textColor, setTextColor] = useState('#00ffff')
  const [selectedFont, setSelectedFont] = useState(fonts[0])
  const [selectedElement, setSelectedElement] = useState('p')
  const [savedSelections, setSavedSelections] = useState<Array<{
    font: string;
    element: string;
    fontSize: number;
    fontWeight: string;
    glowIntensity: number;
    textColor: string;
  }>>([])

  // Save current selection
  const saveSelection = () => {
    const newSelection = {
      font: selectedFont.name,
      element: selectedElement,
      fontSize,
      fontWeight,
      glowIntensity,
      textColor
    }
    
    setSavedSelections([...savedSelections, newSelection])
    
    // Show confirmation
    alert(`Saved: ${selectedFont.name} for ${selectedElement} elements`)
  }

  // Generate CSS for saved selections
  const generateCSS = () => {
    let css = '/* Font styles */\n'
    
    savedSelections.forEach(selection => {
      const fontVariable = fonts.find(f => f.name === selection.font)?.variable || ''
      
      css += `${selection.element} {\n`
      css += `  font-family: var(${fontVariable});\n`
      css += `  font-size: ${selection.fontSize}px;\n`
      css += `  font-weight: ${selection.fontWeight};\n`
      css += `  color: ${selection.textColor};\n`
      css += `  text-shadow: 0 0 ${selection.glowIntensity}px ${selection.textColor};\n`
      css += `}\n\n`
    })
    
    return css
  }

  // Copy CSS to clipboard
  const copyCSSToClipboard = () => {
    const css = generateCSS()
    navigator.clipboard.writeText(css)
    alert('CSS copied to clipboard!')
  }

  return (
    <div className={`
      ${orbitron.variable} 
      ${tiltNeon.variable} 
      ${rubikGlitch.variable} 
      ${rubikBurned.variable}
      ${audiowide.variable}
      ${syncopate.variable}
      ${michroma.variable}
      ${exo2.variable}
      ${syneMono.variable}
      ${monoton.variable}
      ${poiretOne.variable}
      ${majorMonoDisplay.variable}
      min-h-screen bg-black p-8
    `}>
      <h1 className="text-3xl text-white mb-8">Font Selector</h1>
      
      {/* Controls */}
      <div className="bg-gray-900 p-6 rounded-lg mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-white mb-2">Font</label>
            <select 
              className="w-full bg-black text-white border border-gray-700 rounded p-2"
              value={selectedFont.name}
              onChange={(e) => setSelectedFont(fonts.find(f => f.name === e.target.value) || fonts[0])}
            >
              {fonts.map(font => (
                <option key={font.name} value={font.name}>{font.name}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-white mb-2">Font Size (px)</label>
            <input 
              type="range" 
              min="12" 
              max="72" 
              value={fontSize}
              onChange={(e) => setFontSize(parseInt(e.target.value))}
              className="w-full"
            />
            <div className="text-white text-center">{fontSize}px</div>
          </div>
          
          <div>
            <label className="block text-white mb-2">Font Weight</label>
            <select 
              className="w-full bg-black text-white border border-gray-700 rounded p-2"
              value={fontWeight}
              onChange={(e) => setFontWeight(e.target.value)}
            >
              {selectedFont.weights.map(weight => (
                <option key={weight} value={weight}>{weight}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-white mb-2">Glow Intensity (px)</label>
            <input 
              type="range" 
              min="0" 
              max="20" 
              value={glowIntensity}
              onChange={(e) => setGlowIntensity(parseInt(e.target.value))}
              className="w-full"
            />
            <div className="text-white text-center">{glowIntensity}px</div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-white mb-2">Text Color</label>
            <input 
              type="color" 
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full bg-black border border-gray-700 rounded p-1 h-10"
            />
          </div>
          
          <div>
            <label className="block text-white mb-2">Sample Text</label>
            <input 
              type="text" 
              value={sampleText}
              onChange={(e) => setSampleText(e.target.value)}
              className="w-full bg-black text-white border border-gray-700 rounded p-2"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-white mb-2">Apply To HTML Element</label>
            <select 
              className="w-full bg-black text-white border border-gray-700 rounded p-2"
              value={selectedElement}
              onChange={(e) => setSelectedElement(e.target.value)}
            >
              {htmlElements.map(element => (
                <option key={element} value={element}>{element}</option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button 
              onClick={saveSelection}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Save and Use
            </button>
          </div>
        </div>
      </div>
      
      {/* Preview */}
      <div className="bg-black border border-gray-800 rounded-lg p-6 mb-8">
        <h2 className="text-white mb-4">Preview</h2>
        <div 
          style={{ 
            fontFamily: `var(${selectedFont.variable})`,
            fontSize: `${fontSize}px`,
            fontWeight: fontWeight,
            color: textColor,
            textShadow: `0 0 ${glowIntensity}px ${textColor}`
          }}
        >
          {sampleText}
        </div>
      </div>
      
      {/* Saved Selections */}
      {savedSelections.length > 0 && (
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-white">Saved Selections</h2>
            <button 
              onClick={copyCSSToClipboard}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            >
              Copy CSS
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-white">
              <thead>
                <tr className="border-b border-gray-800">
                  <th className="text-left py-2">Element</th>
                  <th className="text-left py-2">Font</th>
                  <th className="text-left py-2">Size</th>
                  <th className="text-left py-2">Weight</th>
                  <th className="text-left py-2">Glow</th>
                  <th className="text-left py-2">Color</th>
                </tr>
              </thead>
              <tbody>
                {savedSelections.map((selection, index) => (
                  <tr key={index} className="border-b border-gray-800">
                    <td className="py-2">{selection.element}</td>
                    <td className="py-2">{selection.font}</td>
                    <td className="py-2">{selection.fontSize}px</td>
                    <td className="py-2">{selection.fontWeight}</td>
                    <td className="py-2">{selection.glowIntensity}px</td>
                    <td className="py-2">
                      <div className="flex items-center">
                        <div 
                          className="w-4 h-4 mr-2 rounded-full" 
                          style={{ backgroundColor: selection.textColor }}
                        ></div>
                        {selection.textColor}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <h3 className="text-white mb-2">Generated CSS</h3>
            <pre className="text-green-400 overflow-x-auto whitespace-pre-wrap">
              {generateCSS()}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
