"use client"

import { useParams } from "next/navigation"
import { SupabasePlaylistPlayer } from "@/components/supabase-playlist-player"
import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

export default function SupabasePlayerPage() {
  const params = useParams()
  const playlistId = params.id as string
  const [playlistName, setPlaylistName] = useState<string>("")
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    // Check if user is admin
    const checkAdmin = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (user) {
        const { data } = await supabase.from("profiles").select("role").eq("id", user.id).single()

        setIsAdmin(data?.role === "admin")
      }
    }

    // Get playlist name
    const getPlaylistName = async () => {
      const { data } = await supabase.from("playlists").select("name").eq("id", playlistId).single()

      if (data) {
        setPlaylistName(data.name)
      }
    }

    checkAdmin()
    getPlaylistName()
  }, [playlistId])

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/playlists">
          <Button variant="outline" size="sm" className="flex items-center space-x-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Playlists</span>
          </Button>
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-6">{playlistName || "Playlist"}</h1>

      <div className="bg-black rounded-lg overflow-hidden p-4">
        <SupabasePlaylistPlayer playlistId={playlistId} isAdmin={isAdmin} showLogo={true} />
      </div>
    </div>
  )
}
