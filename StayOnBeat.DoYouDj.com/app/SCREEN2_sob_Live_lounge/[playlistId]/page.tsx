"use client"

import { useParams } from "next/navigation"
import { SupabaseM3U8Player } from "@/components/supabase-m3u8-player"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"

export default function PlaylistPlayerPage() {
  const params = useParams()
  const playlistId = params.playlistId as string
  const [playlistName, setPlaylistName] = useState<string>("")

  useEffect(() => {
    async function fetchPlaylistDetails() {
      if (!playlistId) return

      try {
        const { data, error } = await supabase.from("playlists").select("name").eq("id", playlistId).single()

        if (error) throw error

        if (data) {
          setPlaylistName(data.name)
        }
      } catch (err) {
        console.error("Error fetching playlist details:", err)
      }
    }

    fetchPlaylistDetails()
  }, [playlistId])

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/playlists/m3u8">
          <Button variant="outline" size="sm" className="flex items-center space-x-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Playlists</span>
          </Button>
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-6">{playlistName ? `Playing: ${playlistName}` : "Playlist Player"}</h1>

      <div className="bg-black rounded-lg overflow-hidden">
        <SupabaseM3U8Player
          playlistId={playlistId}
          showControls={true}
          fluid={true}
          aspectRatio="16:9"
          showLogo={true}
        />
      </div>
    </div>
  )
}
