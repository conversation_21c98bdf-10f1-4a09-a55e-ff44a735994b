"use client"
import { MusicPlayerProvider } from "@/contexts/music-player-context"
import { MusicPlayer } from "@/components/music-player/music-player"
import { TrackList } from "@/components/music-player/track-list"
import { Queue } from "@/components/music-player/queue"
import { MiniPlayer } from "@/components/music-player/mini-player"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"

// Sample tracks for demonstration
const sampleTracks = [
  {
    id: "1",
    title: "Stay On Beat",
    artist: "DJ Rhythm",
    audio_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
    artwork_url: "/abstract-soundscape.png",
    duration: 372,
  },
  {
    id: "2",
    title: "Midnight Groove",
    artist: "Smooth Operators",
    audio_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3",
    artwork_url: "/jazz-album-cover.png",
    duration: 289,
  },
  {
    id: "3",
    title: "Electric Dreams",
    artist: "Synth Wave",
    audio_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3",
    artwork_url: "/electronic-album-cover.png",
    duration: 318,
  },
  {
    id: "4",
    title: "Urban Pulse",
    artist: "City Beats",
    audio_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3",
    artwork_url: "/hip-hop-album-cover.png",
    duration: 245,
  },
  {
    id: "5",
    title: "Sunset Vibes",
    artist: "Chill Masters",
    audio_url: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3",
    artwork_url: "/lofi-album-cover.png",
    duration: 302,
  },
]

export default function PlayerPage() {
  return (
    <MusicPlayerProvider>
      <div className="min-h-screen bg-white dark:bg-gray-950 pb-24">
        <div className="max-w-7xl mx-auto p-6">
          <h1 className="text-3xl font-bold mb-8">Music Player</h1>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="col-span-1 md:col-span-2">
              <h2 className="text-xl font-semibold mb-4">Featured Tracks</h2>
              <TrackList tracks={sampleTracks} />
            </div>

            <div className="col-span-1">
              <h2 className="text-xl font-semibold mb-4">Now Playing</h2>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                <MiniPlayer />
              </div>

              <h2 className="text-xl font-semibold mt-8 mb-4">Current Queue</h2>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                <Queue />
              </div>
            </div>
          </div>

          <div className="mt-12">
            <h2 className="text-xl font-semibold mb-4">Explore Music</h2>
            <Tabs defaultValue="trending">
              <TabsList>
                <TabsTrigger value="trending">Trending</TabsTrigger>
                <TabsTrigger value="new">New Releases</TabsTrigger>
                <TabsTrigger value="recommended">Recommended</TabsTrigger>
              </TabsList>
              <TabsContent value="trending" className="mt-4">
                <TrackList tracks={sampleTracks.slice(0, 3)} />
              </TabsContent>
              <TabsContent value="new" className="mt-4">
                <TrackList tracks={sampleTracks.slice(2, 5)} />
              </TabsContent>
              <TabsContent value="recommended" className="mt-4">
                <TrackList tracks={sampleTracks.slice(1, 4)} />
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <MusicPlayer />
      </div>
    </MusicPlayerProvider>
  )
}
