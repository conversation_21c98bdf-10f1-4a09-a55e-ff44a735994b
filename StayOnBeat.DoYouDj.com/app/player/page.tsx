"use client"

import { Player<PERSON>rovider } from "@/contexts/music-player-context"
import { MusicPlayer } from "@/components/music-player/music-player"
import { Playlist } from "@/components/playlist/playlist"
import { TrackUpload } from "@/components/track-upload/track-upload"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "sonner"
import { useEffect } from "react"
import { useSupabasePlayer } from "@/hooks/use-supabase-player"
import { usePlayer } from "@/contexts/music-player-context"

function PlayerContent() {
  const { tracks, fetchTracks } = useSupabasePlayer()
  const { setPlaylist } = usePlayer()

  // Load tracks from Supabase when component mounts
  useEffect(() => {
    async function loadTracks() {
      await fetchTracks()
      if (tracks.length > 0) {
        setPlaylist(tracks)
      }
    }

    loadTracks()
  }, [fetchTracks, setPlaylist, tracks])

  return (
    <div className="container mx-auto flex h-screen flex-col space-y-4 p-4">
      <h1 className="text-2xl font-bold">Music Player</h1>
      <MusicPlayer />
      <Tabs defaultValue="playlist" className="flex-1">
        <TabsList>
          <TabsTrigger value="playlist">Playlist</TabsTrigger>
          <TabsTrigger value="upload">Upload</TabsTrigger>
        </TabsList>
        <TabsContent value="playlist" className="h-[calc(100vh-16rem)]">
          <Playlist />
        </TabsContent>
        <TabsContent value="upload" className="h-[calc(100vh-16rem)]">
          <div className="mx-auto max-w-md p-4">
            <TrackUpload />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function PlayerPage() {
  return (
    <PlayerProvider>
      <PlayerContent />
      <Toaster />
    </PlayerProvider>
  )
}