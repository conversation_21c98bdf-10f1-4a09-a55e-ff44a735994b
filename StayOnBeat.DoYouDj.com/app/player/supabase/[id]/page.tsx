"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { PlayerProvider } from "@/contexts/music-player-context"
import { MusicPlayer } from "@/components/music-player/music-player"
import { Playlist } from "@/components/playlist/playlist"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { Toaster } from "sonner"
import { useSupabasePlayer } from "@/hooks/use-supabase-player"
import { usePlayer } from "@/contexts/music-player-context"
import type { Track } from "@/contexts/music-player-context"

export default function SupabasePlaylistPage() {
  const params = useParams()
  const router = useRouter()
  const playlistId = params.id as string
  const [playlistName, setPlaylistName] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // We need to use this component to load the playlist and then pass it to the PlayerProvider
  return (
    <PlayerProvider>
      <SupabasePlaylistContent playlistId={playlistId} />
      <Toaster />
    </PlayerProvider>
  )
}

function SupabasePlaylistContent({ playlistId }: { playlistId: string }) {
  const [playlistName, setPlaylistName] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { setPlaylist } = usePlayer()

  // Fetch playlist data and load it into the player
  useEffect(() => {
    async function loadPlaylist() {
      try {
        setIsLoading(true)
        
        // Get playlist details
        const { data: playlistData, error: playlistError } = await supabase
          .from("playlists")
          .select("name")
          .eq("id", playlistId)
          .single()

        if (playlistError) throw playlistError
        setPlaylistName(playlistData.name)

        // Get playlist items
        const { data: playlistItems, error: itemsError } = await supabase
          .from("playlist_items")
          .select(`
            id,
            position,
            songs (
              id,
              title,
              artist_name,
              media_url,
              duration,
              album_name,
              artwork_url
            )
          `)
          .eq("playlist_id", playlistId)
          .order("position", { ascending: true })

        if (itemsError) throw itemsError

        // Convert to Track format
        const tracks: Track[] = playlistItems.map((item) => ({
          id: item.songs.id,
          title: item.songs.title,
          artist: item.songs.artist_name,
          artwork_url: item.songs.artwork_url || "/placeholder.svg",
          audio_url: item.songs.media_url,
          duration: item.songs.duration || 0,
          created_at: new Date().toISOString(),
        }))

        // Set the playlist in the player context
        setPlaylist(tracks)
      } catch (err) {
        console.error("Error loading playlist:", err)
        setError("Failed to load playlist. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    loadPlaylist()
  }, [playlistId, setPlaylist])

  return (
    <div className="container mx-auto flex h-screen flex-col space-y-4 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/playlists">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {isLoading ? "Loading..." : playlistName || "Playlist"}
          </h1>
        </div>
      </div>

      <MusicPlayer />
      
      <div className="flex-1 overflow-hidden rounded-lg border">
        <Playlist />
      </div>
    </div>
  )
}
