"use server"

import { supabase } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

// Submit a new track
export async function submitTrack(formData: FormData) {
  const artistName = formData.get("artistName") as string
  const songTitle = formData.get("songTitle") as string
  const submissionType = formData.get("submissionType") as string
  const url = formData.get("url") as string
  const userId = formData.get("userId") as string

  try {
    const { data, error } = await supabase
      .from("submissions")
      .insert([
        {
          artist_name: artistName,
          song_title: songTitle,
          submission_type: submissionType,
          url: url,
          user_id: userId,
          status: "pending",
          created_at: new Date().toISOString(),
        },
      ])
      .select()

    if (error) throw error

    revalidatePath("/queue")
    return { success: true, data }
  } catch (error) {
    console.error("Error submitting track:", error)
    return { success: false, error }
  }
}

// Approve a submission and add it to the playlist
export async function approveSubmission(submissionId: number) {
  try {
    // First get the submission
    const { data: submission, error: fetchError } = await supabase
      .from("submissions")
      .select("*")
      .eq("id", submissionId)
      .single()

    if (fetchError) throw fetchError

    // Update the submission status
    const { error: updateError } = await supabase
      .from("submissions")
      .update({ status: "confirmed" })
      .eq("id", submissionId)

    if (updateError) throw updateError

    // Get the current max position in the playlist
    const { data: maxPositionData, error: maxPositionError } = await supabase
      .from("playlist")
      .select("position")
      .order("position", { ascending: false })
      .limit(1)

    if (maxPositionError) throw maxPositionError

    const nextPosition = maxPositionData && maxPositionData.length > 0 ? maxPositionData[0].position + 1 : 1

    // Add to playlist
    const { error: insertError } = await supabase.from("playlist").insert([
      {
        position: nextPosition,
        artist_name: submission.artist_name,
        song_title: submission.song_title,
        type: submission.submission_type,
        user_id: submission.user_id,
        url: submission.url,
      },
    ])

    if (insertError) throw insertError

    revalidatePath("/admin")
    revalidatePath("/queue")
    return { success: true }
  } catch (error) {
    console.error("Error approving submission:", error)
    return { success: false, error }
  }
}

// Update playlist order
export async function updatePlaylistOrder(items: { id: number; position: number }[]) {
  try {
    // Update each item's position
    for (const item of items) {
      const { error } = await supabase.from("playlist").update({ position: item.position }).eq("id", item.id)

      if (error) throw error
    }

    revalidatePath("/admin")
    revalidatePath("/queue")
    return { success: true }
  } catch (error) {
    console.error("Error updating playlist order:", error)
    return { success: false, error }
  }
}

// Mark track as played
export async function markTrackAsPlayed(trackId: number, feedback = "") {
  try {
    // First get the track
    const { data: track, error: fetchError } = await supabase.from("playlist").select("*").eq("id", trackId).single()

    if (fetchError) throw fetchError

    // Add to played tracks
    const { error: insertError } = await supabase.from("played_tracks").insert([
      {
        artist_name: track.artist_name,
        song_title: track.song_title,
        played_at: new Date().toISOString(),
        feedback: feedback,
        feedback_sent: feedback.length > 0,
        user_id: track.user_id,
        url: track.url,
      },
    ])

    if (insertError) throw insertError

    // Remove from playlist
    const { error: deleteError } = await supabase.from("playlist").delete().eq("id", trackId)

    if (deleteError) throw deleteError

    // Reorder remaining tracks
    const { data: remainingTracks, error: remainingError } = await supabase
      .from("playlist")
      .select("*")
      .order("position", { ascending: true })

    if (remainingError) throw remainingError

    // Update positions
    for (let i = 0; i < remainingTracks.length; i++) {
      const { error } = await supabase
        .from("playlist")
        .update({ position: i + 1 })
        .eq("id", remainingTracks[i].id)

      if (error) throw error
    }

    revalidatePath("/admin")
    revalidatePath("/queue")
    return { success: true }
  } catch (error) {
    console.error("Error marking track as played:", error)
    return { success: false, error }
  }
}
