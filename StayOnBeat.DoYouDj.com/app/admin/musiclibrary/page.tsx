"use client"
import { useState } from "react"
import { AdminsBigDock } from "@/components/admin/admins-big-dock"
import { ChevronLeft, ChevronRight, Library, Search, User } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

// Mock data for demonstration
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
]
const currentYear = new Date().getFullYear()

// Temporary playlist data - to be replaced with database data
const temporaryPlaylists = [
  {
    id: "temp-1",
    name: "Temporary Playlist 1",
    date: "2024-03-20",
    linkStatus: "valid",
    tracks: [
      { id: "temp-track-1", title: "Temporary Track 1", artist: "Temp Artist 1" },
      { id: "temp-track-2", title: "Temporary Track 2", artist: "Temp Artist 2" }
    ]
  },
  {
    id: "temp-2",
    name: "Temporary Playlist 2",
    date: "2024-03-21",
    linkStatus: "valid",
    tracks: [
      { id: "temp-track-3", title: "Temporary Track 3", artist: "Temp Artist 3" },
      { id: "temp-track-4", title: "Temporary Track 4", artist: "Temp Artist 4" }
    ]
  }
]

// Function to get status color
const getLinkStatusColor = (status: string) => {
  switch (status) {
    case "valid":
      return "bg-green-500"
    case "invalid":
      return "bg-red-500"
    case "replaced":
      return "bg-yellow-500"
    default:
      return "bg-gray-500"
  }
}

export default function MusicLibraryPage() {
  const [currentPlaylistIndex, setCurrentPlaylistIndex] = useState(0)
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // Filter playlists based on search query
  const filteredPlaylists = temporaryPlaylists.filter((playlist) =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Get playlists for a specific date
  const getPlaylistsForDate = (date: string) => {
    return temporaryPlaylists.filter((playlist) => playlist.date === date)
  }

  // Navigate through playlists
  const nextPlaylist = () => {
    if (currentPlaylistIndex < filteredPlaylists.length - 1) {
      setCurrentPlaylistIndex(currentPlaylistIndex + 1)
    }
  }

  const prevPlaylist = () => {
    if (currentPlaylistIndex > 0) {
      setCurrentPlaylistIndex(currentPlaylistIndex - 1)
    }
  }

  // Generate calendar days for a month
  const generateCalendarDays = (month: number) => {
    const daysInMonth = new Date(currentYear, month + 1, 0).getDate()
    const firstDayOfMonth = new Date(currentYear, month, 1).getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = `${currentYear}-${String(month + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`
      const hasPlaylist = temporaryPlaylists.some((playlist) => playlist.date === date)

      days.push(
        <div
          key={`day-${day}`}
          onClick={() => hasPlaylist && setSelectedDate(date)}
          className={`h-8 w-8 flex items-center justify-center rounded-full cursor-pointer
            ${hasPlaylist ? "bg-purple-600 text-white hover:bg-purple-700" : "text-gray-500"}
            ${selectedDate === date ? "ring-2 ring-purple-300" : ""}
          `}
        >
          {day}
        </div>,
      )
    }

    return days
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AdminsBigDock />
      <div className="admin-container-header">
        <Library className="h-6 w-6 mr-2 text-purple-600" />
        <h1 className="admin-container-title">Music Library</h1>
      </div>

      <div className="admin-container">
        <Tabs defaultValue="artist" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="artist">By Artist Name</TabsTrigger>
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            <TabsTrigger value="search">Search</TabsTrigger>
          </TabsList>

          <TabsContent value="artist" className="space-y-6">
            <div className="bg-black rounded-lg shadow p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  "A",
                  "B",
                  "C",
                  "D",
                  "E",
                  "F",
                  "G",
                  "H",
                  "I",
                  "J",
                  "K",
                  "L",
                  "M",
                  "N",
                  "O",
                  "P",
                  "Q",
                  "R",
                  "S",
                  "T",
                  "U",
                  "V",
                  "W",
                  "X",
                  "Y",
                  "Z",
                ].map((letter) => (
                  <div key={letter} className="admin-card">
                    <h3 className="text-xl font-semibold mb-3 text-white flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      {letter}
                    </h3>
                    <div className="space-y-2">
                      {temporaryPlaylists
                        .filter((playlist) => playlist.name.startsWith(letter))
                        .map((playlist) => (
                          <div key={playlist.id} className="flex items-center justify-between p-2 bg-black rounded-lg">
                            <div>
                              <h4 className="text-sm font-medium text-white">{playlist.name}</h4>
                            </div>
                            <Badge className={getLinkStatusColor(playlist.linkStatus)}>
                              {playlist.linkStatus.charAt(0).toUpperCase() + playlist.linkStatus.slice(1)}
                            </Badge>
                          </div>
                        ))}
                      {temporaryPlaylists.filter((playlist) => playlist.name.startsWith(letter)).length === 0 && (
                        <p className="text-gray-500 text-sm">No artists</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {MONTHS.map((month, index) => (
                <div key={month} className="admin-card">
                  <h3 className="text-xl font-semibold mb-2 text-white">{month}</h3>
                  <div className="grid grid-cols-7 gap-1">
                    {["S", "M", "T", "W", "T", "F", "S"].map((day) => (
                      <div key={day} className="h-8 w-8 flex items-center justify-center text-gray-400 font-medium">
                        {day}
                      </div>
                    ))}
                    {generateCalendarDays(index)}
                  </div>
                </div>
              ))}
            </div>

            {selectedDate && (
              <div className="mt-8 admin-container">
                <h2 className="text-2xl font-bold mb-4 text-white">Playlists for {selectedDate}</h2>
                <div className="space-y-4">
                  {getPlaylistsForDate(selectedDate).map((playlist) => (
                    <div key={playlist.id} className="admin-card">
                      <div>
                        <h3 className="text-lg font-medium text-white">{playlist.name}</h3>
                        <p className="text-gray-400">{playlist.date}</p>
                      </div>
                      <div className="flex items-center">
                        <Badge className={`${getLinkStatusColor(playlist.linkStatus)} mr-2`}>
                          {playlist.linkStatus.charAt(0).toUpperCase() + playlist.linkStatus.slice(1)}
                        </Badge>
                        <button className="text-purple-500 hover:text-purple-400">Play</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="search">
            <div className="admin-card">
              <div className="flex items-center mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search playlists..."
                    className="w-full pl-10 pr-4 py-2 bg-black border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              {filteredPlaylists.length > 0 ? (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-white">
                      Playlist {currentPlaylistIndex + 1} of {filteredPlaylists.length}
                    </h2>
                    <div className="flex space-x-2">
                      <button
                        onClick={prevPlaylist}
                        disabled={currentPlaylistIndex === 0}
                        className="p-2 rounded-full bg-black border border-purple-500/50 text-white disabled:opacity-50 shadow-[0_0_10px_rgba(147,51,234,0.3)]"
                      >
                        <ChevronLeft size={20} />
                      </button>
                      <button
                        onClick={nextPlaylist}
                        disabled={currentPlaylistIndex === filteredPlaylists.length - 1}
                        className="p-2 rounded-full bg-black border border-purple-500/50 text-white disabled:opacity-50 shadow-[0_0_10px_rgba(147,51,234,0.3)]"
                      >
                        <ChevronRight size={20} />
                      </button>
                    </div>
                  </div>

                  <div className="admin-card">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-white">
                          {filteredPlaylists[currentPlaylistIndex].name}
                        </h3>
                        <p className="text-gray-400">{filteredPlaylists[currentPlaylistIndex].date}</p>
                      </div>
                      <Badge className={getLinkStatusColor(filteredPlaylists[currentPlaylistIndex].linkStatus)}>
                        {filteredPlaylists[currentPlaylistIndex].linkStatus.charAt(0).toUpperCase() +
                          filteredPlaylists[currentPlaylistIndex].linkStatus.slice(1)}
                      </Badge>
                    </div>

                    <div className="mt-4 p-3 border border-purple-500/30 rounded-lg">
                      <p className="text-gray-300 text-sm">
                        <span className="font-medium">Format:</span> .m3u8
                      </p>
                      <p className="text-gray-300 text-sm mt-1">
                        <span className="font-medium">Status:</span>{" "}
                        {filteredPlaylists[currentPlaylistIndex].linkStatus === "valid"
                          ? "Link is working"
                          : filteredPlaylists[currentPlaylistIndex].linkStatus === "invalid"
                            ? "Link is broken"
                            : "Link was replaced"}
                      </p>
                      <div className="mt-3 flex justify-between">
                        <button className="text-purple-500 hover:text-purple-400 text-sm">Validate Link</button>
                        <button className="text-purple-500 hover:text-purple-400 text-sm">Update Link</button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-medium text-white mb-2">Access Information</h3>
                    <p className="text-gray-300 text-sm">
                      <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                      Public - Available to all users (previous two weeks only)
                    </p>
                    <p className="text-gray-300 text-sm mt-1">
                      <span className="inline-block w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                      VIP Access - All admin-approved playlists
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400">No playlists found matching your search.</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
