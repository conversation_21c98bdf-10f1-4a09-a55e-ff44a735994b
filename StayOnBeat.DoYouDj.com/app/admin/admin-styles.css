.admin-container {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 0.75rem;
  border: 2px solid rgba(147, 51, 234, 0.5);
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.5), inset 0 0 10px rgba(147, 51, 234, 0.3);
  backdrop-filter: blur(4px);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.admin-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.1), rgba(0, 0, 0, 0.8) 60%, transparent 70%);
  pointer-events: none;
}

.admin-container-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.admin-container-title {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(to right, #9333ea, #4f46e5);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 0.5rem;
}

.admin-card {
  background-color: rgba(17, 17, 17, 0.7);
  border-radius: 0.5rem;
  border: 1px solid rgba(147, 51, 234, 0.3);
  box-shadow: 0 0 10px rgba(147, 51, 234, 0.2);
  padding: 1rem;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.admin-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #9333ea, #4f46e5);
  opacity: 0.7;
}
