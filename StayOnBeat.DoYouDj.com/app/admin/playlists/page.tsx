"use client"

import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { PlaylistM3U8Tools } from "@/components/admin/playlist-m3u8-tools"
import { M3U8Generator } from "@/components/m3u8-generator"
import { SpotifyPlaylistButton } from "@/components/spotify-playlist-button"

export default function AdminPlaylistsPage() {
  const [playlists, setPlaylists] = useState([])
  const [newPlaylistName, setNewPlaylistName] = useState("")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchPlaylists()
  }, [])

  async function fetchPlaylists() {
    setIsLoading(true)
    const { data, error } = await supabase.from("playlists").select("*").order("created_at", { ascending: false })
    if (error) {
      console.error("Error fetching playlists:", error)
    } else {
      setPlaylists(data || [])
    }
    setIsLoading(false)
  }

  async function createPlaylist() {
    if (!newPlaylistName.trim()) return

    const { error } = await supabase.from("playlists").insert([{ name: newPlaylistName }])

    if (error) {
      console.error("Error creating playlist:", error)
    } else {
      setNewPlaylistName("")
      fetchPlaylists()
    }
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Playlist Management</h1>

      <Tabs defaultValue="playlists">
        <TabsList className="mb-6">
          <TabsTrigger value="playlists">Playlists</TabsTrigger>
          <TabsTrigger value="m3u8">M3U8 Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="playlists">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              {/* Existing playlist management UI */}
              <div className="bg-black/20 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Your Playlists</h2>
                {isLoading ? (
                  <p>Loading playlists...</p>
                ) : playlists.length === 0 ? (
                  <p>No playlists found. Create your first playlist!</p>
                ) : (
                  <div className="space-y-4">
                    {/* Playlist list */}
                    {playlists.map((playlist: any) => (
                      <div key={playlist.id} className="flex items-center justify-between p-4 bg-black/30 rounded-lg">
                        <div>
                          <h3 className="font-medium">{playlist.name}</h3>
                          <p className="text-sm text-gray-400">
                            Created: {new Date(playlist.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div>
              <div className="bg-black/20 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Create New Playlist</h2>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="playlist-name">Playlist Name</Label>
                    <Input
                      id="playlist-name"
                      value={newPlaylistName}
                      onChange={(e) => setNewPlaylistName(e.target.value)}
                      placeholder="Enter playlist name"
                    />
                  </div>
                  <Button onClick={createPlaylist} disabled={!newPlaylistName.trim()}>
                    Create Playlist
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="m3u8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <M3U8Generator />
            <div className="bg-black/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Spotify Indie Artists</h2>
              <p className="mb-4">Generate an M3U8 playlist from the 20 Spotify indie artist tracks.</p>
              <SpotifyPlaylistButton />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <PlaylistM3U8Tools />

            <div className="bg-black/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">About M3U8 Playlists</h2>
              <p className="mb-4">
                M3U8 is a playlist file format used for multimedia playlists. It's commonly used for streaming audio and
                video.
              </p>
              <p className="mb-4">
                You can generate M3U8 playlists from your Supabase data and use them with any compatible player.
              </p>
              <h3 className="text-lg font-medium mb-2">Features:</h3>
              <ul className="list-disc pl-5 space-y-1">
                <li>Generate M3U8 playlists from your Supabase data</li>
                <li>Download playlists for offline use</li>
                <li>Play playlists directly in the browser</li>
                <li>Share playlists with others</li>
              </ul>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
