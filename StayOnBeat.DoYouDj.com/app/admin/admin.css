/* Admin page custom styles */

/* Custom scrollbar for the admin page */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(147, 51, 234, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.5);
}

/* Neon text effects */
.neon-text-purple {
  color: #9333ea;
  text-shadow: 0 0 5px #9333ea, 0 0 10px rgba(147, 51, 234, 0.5);
}

/* Gradient borders */
.border-gradient-primary {
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(to right, #9333ea, #4f46e5);
}

/* Animations */
@keyframes pulse-purple {
  0%, 100% {
    box-shadow: 0 0 10px rgba(147, 51, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.8);
  }
}

.pulse-animation {
  animation: pulse-purple 2s infinite;
}

/* Admin container */
.admin-container {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 51, 234, 0.3);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.2);
}

/* ON AIR light */
.on-air-light {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 80px;
  height: 40px;
  background: linear-gradient(to right, #9333ea, #4f46e5);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.7);
  z-index: 100;
  animation: pulse-purple 2s infinite;
}

/* Drag and drop styles */
.draggable-item {
  transition: background-color 0.2s, border-color 0.2s, transform 0.1s;
}

.draggable-item:hover {
  border-color: rgba(147, 51, 234, 0.5);
  transform: translateY(-2px);
}

.draggable-item.dragging {
  background: rgba(147, 51, 234, 0.2);
  border-color: rgba(147, 51, 234, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}
