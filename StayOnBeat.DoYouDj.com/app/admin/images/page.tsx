"use client"

import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"
import { ImageUploader } from "@/components/image-uploader"
import { AdminsBigDock } from "@/components/admin/admins-big-dock"

export default function ImagesPage() {
  const [images, setImages] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchImages()
  }, [])

  async function fetchImages() {
    try {
      setLoading(true)
      const { data, error } = await supabase.from("images").select("*").order("created_at", { ascending: false })

      if (error) throw error
      if (data) setImages(data)
    } catch (error) {
      console.error("Error fetching images:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AdminsBigDock />
      <div className="admin-container-header">
        <h1 className="admin-container-title">Image Management</h1>
      </div>

      <div className="admin-container">
        <div className="admin-container-header">
          <h2 className="admin-container-title">Upload New Image</h2>
          <p className="text-gray-400">Upload images for your app. Supported formats: JPG, PNG, GIF.</p>
        </div>
        <div className="mt-4">
          <ImageUploader onUploadComplete={fetchImages} />
        </div>
      </div>

      <div className="admin-container mt-8">
        <div className="admin-container-header">
          <h2 className="admin-container-title">Image Gallery</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
          {loading ? (
            <p>Loading images...</p>
          ) : images.length === 0 ? (
            <p>No images uploaded yet.</p>
          ) : (
            images.map((image) => (
              <div key={image.id} className="admin-card">
                <img
                  src={image.url || "/placeholder.svg"}
                  alt={image.name}
                  className="w-full h-48 object-cover rounded-md"
                />
                <div className="p-4">
                  <h3 className="font-medium text-white">{image.name}</h3>
                  <p className="text-sm text-gray-400">Uploaded on {new Date(image.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
