"use client"

import { useState } from "react"
import { BackgroundImageUploader } from "@/components/background-image-uploader"
import { But<PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { AdminsBigDock } from "@/components/admin/admins-big-dock"

export default function SettingsPage() {
  const [backgroundUrl, setBackgroundUrl] = useState<string | null>(null)

  const saveSettings = async () => {
    try {
      // Save the background URL to your settings table
      const { error } = await supabase.from("settings").upsert({
        id: "default_background_image",
        value: { url: backgroundUrl },
        updated_at: new Date().toISOString(),
      })

      if (error) throw error

      // Update local storage for immediate use
      if (backgroundUrl) {
        localStorage.setItem("backgroundImage", backgroundUrl)
      }
    } catch (error) {
      console.error("Error saving settings:", error)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AdminsBigDock />
      <div className="admin-container-header">
        <h1 className="admin-container-title">App Settings</h1>
      </div>

      <div className="admin-container">
        <div className="admin-container-header">
          <h2 className="admin-container-title">Background Image</h2>
          <p className="text-gray-400">Upload a custom background image for your app. Recommended size: 1920x1080px.</p>
        </div>
        <div className="mt-4">
          <BackgroundImageUploader onUploadComplete={(url) => setBackgroundUrl(url)} />

          {backgroundUrl && (
            <Button onClick={saveSettings} className="mt-4 bg-gradient-to-r from-purple-600 to-indigo-600">
              Save Background
            </Button>
          )}
        </div>
      </div>

      <div className="admin-container mt-8">
        <div className="admin-container-header">
          <h2 className="admin-container-title">Admin Controls</h2>
          <p className="text-gray-400">Configure admin-specific settings and controls.</p>
        </div>

        <div className="mt-4 p-4 bg-black/80 backdrop-blur-md border border-[#00ffff]/30 rounded-lg">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="text-[#00ffff]">Admin Control Panel</div>
            <div className="flex flex-col md:flex-row items-start md:items-center gap-3">
              <div className="flex items-center">
                <span className="text-white mr-2">Transition Speed:</span>
                <select
                  className="bg-black/50 border border-[#00ffff]/30 text-white rounded px-2 py-1"
                  onChange={(e) => {
                    // This would normally update a transition speed state
                    localStorage.setItem("transitionSpeed", e.target.value)
                  }}
                  defaultValue="0.5"
                >
                  <option value="0.3">Fast</option>
                  <option value="0.5">Normal</option>
                  <option value="0.8">Slow</option>
                </select>
              </div>
              <button
                className="bg-black/50 border border-[#00ffff]/30 text-white rounded px-2 py-1 hover:bg-[#00ffff]/20"
                onClick={() => {
                  // Toggle admin mode
                  if (typeof window !== "undefined") {
                    localStorage.setItem("isAdminMode", "false")
                    window.location.reload()
                  }
                }}
              >
                Exit Admin Mode
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
