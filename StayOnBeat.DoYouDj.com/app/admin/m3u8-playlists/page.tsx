"use client"

import { useState, useEffect } from "react"
import { AdminsBigDock } from "@/components/admin/admins-big-dock"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { M3U8FileUploader } from "@/components/m3u8-file-uploader"
import { supabase } from "@/lib/supabase"
import { toast } from "sonner"
import Link from "next/link"
import { Download, Play, RefreshCw, Upload, FileText, Trash2 } from "lucide-react"
import { downloadM3U8FromSupabase } from "@/lib/m3u8-generator"

export default function AdminM3U8PlaylistsPage() {
  const [playlists, setPlaylists] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string | null>(null)
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null)
  const [playlistName, setPlaylistName] = useState("")
  const [isCreating, setIsCreating] = useState(false)

  // Fetch playlists on component mount
  useEffect(() => {
    fetchPlaylists()
  }, [])

  // Function to fetch playlists from Supabase
  const fetchPlaylists = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("playlists")
        .select("*")
        .order("created_at", { ascending: false })

      if (error) throw error
      setPlaylists(data || [])
    } catch (error) {
      console.error("Error fetching playlists:", error)
      toast.error("Failed to load playlists")
    } finally {
      setLoading(false)
    }
  }

  // Handle file upload completion
  const handleUploadComplete = (url: string, fileName: string) => {
    setUploadedFileUrl(url)
    setUploadedFileName(fileName)
    
    // Extract playlist name from file name
    const nameWithoutExtension = fileName.replace(/\.m3u8$/, "")
    setPlaylistName(nameWithoutExtension)
  }

  // Handle playlist creation
  const handleCreatePlaylist = async () => {
    if (!uploadedFileUrl || !playlistName) {
      toast.error("Please upload an M3U8 file and provide a playlist name")
      return
    }

    setIsCreating(true)
    try {
      // Create a new playlist entry
      const { data: playlist, error: playlistError } = await supabase
        .from("playlists")
        .insert({
          name: playlistName,
          visibility: "admin", // Default to admin-only
          created_at: new Date().toISOString(),
          is_finalized: true
        })
        .select()
        .single()
      
      if (playlistError) throw playlistError
      
      // Store the M3U8 file reference
      const { error: fileError } = await supabase
        .from("playlist_files")
        .insert({
          playlist_id: playlist.id,
          content: "", // We'll store the URL instead of the content
          format: "m3u8",
          file_url: uploadedFileUrl,
          file_name: uploadedFileName,
          created_at: new Date().toISOString()
        })
      
      if (fileError) throw fileError
      
      toast.success("Playlist created successfully")
      
      // Reset form and refresh playlists
      setUploadedFileUrl(null)
      setUploadedFileName(null)
      setPlaylistName("")
      fetchPlaylists()
    } catch (error) {
      console.error("Error creating playlist:", error)
      toast.error("Failed to create playlist")
    } finally {
      setIsCreating(false)
    }
  }

  // Handle playlist deletion
  const handleDeletePlaylist = async (playlistId: string) => {
    if (!confirm("Are you sure you want to delete this playlist?")) return

    try {
      // Delete playlist files first
      const { error: filesError } = await supabase
        .from("playlist_files")
        .delete()
        .eq("playlist_id", playlistId)

      if (filesError) throw filesError

      // Then delete the playlist
      const { error: playlistError } = await supabase
        .from("playlists")
        .delete()
        .eq("id", playlistId)

      if (playlistError) throw playlistError

      toast.success("Playlist deleted successfully")
      fetchPlaylists()
    } catch (error) {
      console.error("Error deleting playlist:", error)
      toast.error("Failed to delete playlist")
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AdminsBigDock />
      <div className="admin-container-header mb-6">
        <h1 className="admin-container-title">M3U8 Playlist Management</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Upload Section */}
        <Card className="col-span-1 lg:col-span-1">
          <CardHeader>
            <CardTitle>Upload New Playlist</CardTitle>
            <CardDescription>
              Upload an M3U8 playlist file to create a new playlist
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <M3U8FileUploader
                onUploadComplete={handleUploadComplete}
                existingFileUrl={uploadedFileUrl || undefined}
                existingFileName={uploadedFileName || undefined}
              />
              
              {uploadedFileUrl && (
                <div className="mt-4">
                  <label className="block text-sm font-medium mb-1">Playlist Name</label>
                  <input
                    type="text"
                    value={playlistName}
                    onChange={(e) => setPlaylistName(e.target.value)}
                    className="w-full px-3 py-2 bg-black/50 border border-purple-500/50 rounded-md"
                    placeholder="Enter playlist name"
                  />
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleCreatePlaylist}
              disabled={!uploadedFileUrl || !playlistName || isCreating}
              className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
            >
              {isCreating ? "Creating..." : "Create Playlist"}
            </Button>
          </CardFooter>
        </Card>

        {/* Playlists Section */}
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Your Playlists</CardTitle>
            <CardDescription>
              Manage your M3U8 playlists
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading playlists...</div>
            ) : playlists.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No playlists found. Upload an M3U8 file to create one.
              </div>
            ) : (
              <div className="space-y-4">
                {playlists.map((playlist) => (
                  <div key={playlist.id} className="flex items-center justify-between p-4 border border-purple-500/30 rounded-lg bg-black/30">
                    <div>
                      <h3 className="font-medium">{playlist.name}</h3>
                      <p className="text-xs text-muted-foreground">
                        Created: {new Date(playlist.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadM3U8FromSupabase(playlist.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Link href={`/SCREEN2_sob_Live_lounge/supabase/${playlist.id}`} target="_blank">
                        <Button variant="outline" size="sm">
                          <Play className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => handleDeletePlaylist(playlist.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              onClick={fetchPlaylists}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
