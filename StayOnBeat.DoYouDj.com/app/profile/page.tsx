import { auth, currentUser } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LogOut, Music } from "lucide-react"
import Link from "next/link"
import { PageTransition } from "@/components/page-transition"
import { createServerSupabaseClient } from "@/lib/supabase-auth"

async function getSubmissions(userId: string) {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from("submissions")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching submissions:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Failed to fetch submissions:", error);
    return [];
  }
}

export default async function ProfilePage() {
  // Check if the user is authenticated
  const { userId } = auth();

  // If not authenticated, redirect to sign-in
  if (!userId) {
    redirect("/sign-in");
  }

  // Get the user data
  const user = await currentUser();

  // Get the user's submissions
  const submissions = await getSubmissions(userId);

  // Updated profile page with gradient colors
  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-b from-black via-purple-900/30 to-black relative overflow-hidden">
        {/* Base gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-900/30 to-black z-0"></div>

        {/* Add a horizon line effect */}
        <div
          className="absolute inset-0 bg-gradient-to-t from-transparent via-[#00ffff]/5 to-transparent h-full w-full z-20"
          style={{
            transform: "perspective(1000px) rotateX(80deg)",
            transformOrigin: "bottom",
            bottom: "-5%",
            opacity: 0.7,
          }}
        ></div>

        {/* Add grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20 z-30"></div>

        {/* NavigationDock is now handled by the layout */}
        <div className="container mx-auto px-4 py-8 text-white min-h-screen relative z-40">
          <div className="flex flex-col items-center justify-center">
            <Card className="w-full max-w-md bg-black/80 border border-[#ff00ff]/30 neon-box">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl glow-text-pink">Your Profile</CardTitle>
                <CardDescription>Manage your account and connected services</CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="flex flex-col items-center">
                  <Avatar className="h-24 w-24 mb-4 ring-2 ring-purple-500">
                    <AvatarImage src={user?.imageUrl || ""} />
                    <AvatarFallback className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-2xl">
                      {user?.firstName?.[0] || user?.emailAddresses?.[0]?.emailAddress?.[0]?.toUpperCase() || <Music className="h-12 w-12" />}
                    </AvatarFallback>
                  </Avatar>

                  <h2 className="text-xl font-bold neon-text">
                    {user?.fullName || user?.firstName || user?.emailAddresses?.[0]?.emailAddress || "User"}
                  </h2>

                  {user?.emailAddresses?.[0]?.emailAddress && (
                    <p className="text-sm text-gray-400">{user.emailAddresses[0].emailAddress}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium neon-text-cyan">Your Submissions</h3>
                  <div className="border rounded-lg p-4 border-[#00ffff]/30 bg-black/50">
                    {submissions && submissions.length > 0 ? (
                      <ul className="space-y-2">
                        {submissions.slice(0, 3).map((submission: any) => (
                          <li key={submission.id} className="flex flex-col">
                            <span className="font-medium text-white">{submission.song_title}</span>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-400">{submission.artist_name}</span>
                              <span className={`text-xs ${
                                submission.status === 'played' ? 'text-green-500' :
                                submission.status === 'in_queue' ? 'text-yellow-500' :
                                'text-blue-500'
                              }`}>
                                {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                              </span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm text-gray-400">
                        No submissions yet.{" "}
                        <Link href="/submit" className="text-[#00ffff] hover:underline">
                          Submit a track
                        </Link>
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>

              <CardFooter>
                <Link href="/sign-out">
                  <Button
                    variant="outline"
                    className="w-full bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta"
                    style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign out
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <div className="mt-6">
              <Link href="/" className="text-[#00ffff] hover:text-[#00ffff]/70 neon-text-cyan">
                Return to Home
              </Link>
            </div>
          </div>
        </div>
        {/* Add horizon grid lines */}
        <div
          className="absolute bottom-0 left-0 right-0 h-[30vh] z-30"
          style={{
            background:
              "linear-gradient(to top, rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(to right, rgba(0,255,255,0.1) 1px, transparent 1px)",
            backgroundSize: "40px 20px",
            transform: "perspective(500px) rotateX(60deg)",
            transformOrigin: "bottom",
            opacity: 0.2,
          }}
        ></div>
      </div>
    </PageTransition>
  )
}
