"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, ExternalLink, Music, Calendar, Instagram, Twitter, Globe } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PageTransition } from "@/components/page-transition"
import { NavigationDock } from "@/components/navigation-dock"

// Temporary artist data - to be replaced with API data
const temporaryArtists = [
  {
    id: "temp-1",
    name: "Temporary Artist 1",
    profileImage: "/temp-artist-1.jpg",
    bio: "This is a temporary artist profile that will be replaced with real data.",
    coverImages: [
      "/temp-cover-1.jpg",
      "/temp-cover-2.jpg",
      "/temp-cover-3.jpg"
    ],
    socialLinks: {
      instagram: "https://instagram.com/temp-artist-1",
      twitter: "https://twitter.com/temp-artist-1",
      website: "https://temp-artist-1.com"
    },
    submissions: [
      {
        id: "temp-sub-1",
        songTitle: "Temporary Track 1",
        submissionType: "VIP",
        submissionTime: new Date().toISOString(),
        status: "played",
        platform: "soundcloud",
        url: "https://soundcloud.com/temp-artist-1/temp-track-1"
      }
    ]
  },
  // Add more temporary artists as needed
]

// Helper function to format dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

// Helper function to get platform icon
const getPlatformIcon = (platform: string) => {
  switch (platform) {
    case "soundcloud":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M1.175 12.225c-.051 0-.094.046-.101.1l-.233 2.154.233 2.105c.**************.101.098.05 0 .09-.04.099-.098l.255-2.105-.27-2.154c-.009-.055-.05-.1-.1-.1m-.899.828c-.05 0-.091.04-.1.099L0 14.479l.176 1.299c.***********.101.1.05 0 .09-.04.099-.1l.2-1.3-.2-1.327c-.01-.06-.05-.099-.1-.099m1.8-1.548c-.061 0-.11.05-.12.11l-.216 2.762.216 2.681c.01.06.06.11.12.11.06 0 .11-.05.12-.11l.244-2.681-.244-2.762c-.01-.06-.061-.11-.12-.11m.901-.495c-.07 0-.13.06-.14.13l-.2 3.237.2 3.142c.01.07.07.13.14.13.07 0 .13-.06.14-.13l.22-3.142-.22-3.237c-.01-.07-.07-.13-.14-.13m1.001-.473c-.08 0-.14.07-.15.15l-.181 3.56.181 3.443c.01.08.07.15.15.15.08 0 .14-.07.15-.15l.205-3.443-.205-3.56c-.01-.08-.07-.15-.15-.15m1.002-.328c-.09 0-.16.08-.17.17l-.167 3.867.167 3.74c.01.09.08.17.17.17.09 0 .16-.08.17-.17l.185-3.74-.185-3.867c-.01-.09-.08-.17-.17-.17m1.17-.33c-.1 0-.18.09-.19.19l-.15 4.177.15 4.043c.01.1.09.19.19.19.1 0 .18-.09.19-.19l.17-4.043-.17-4.177c-.01-.1-.09-.19-.19-.19m1.198-.137c-.11 0-.2.1-.21.21l-.137 4.294.137 4.163c.01.11.1.2.21.2.11 0 .2-.09.21-.2l.153-4.163-.153-4.294c-.01-.11-.1-.21-.21-.21m1.3-.112c-.121 0-.221.11-.231.22l-.126 4.396.126 4.267c.01.12.11.22.231.22.12 0 .22-.1.23-.22l.142-4.267-.142-4.396c-.01-.11-.11-.22-.23-.22m1.401-.048c-.131 0-.241.11-.251.24l-.114 4.424.114 4.306c.01.13.12.24.251.24.13 0 .24-.11.25-.24l.127-4.306-.127-4.424c-.01-.13-.12-.24-.25-.24m1.502-.057c-.06 0-.12.01-.17.03-.05.02-.09.05-.13.09-.07.08-.11.17-.11.27l-.001.16-.11 4.191.11 4.414h.001c.01.11.05.2.11.27.04.04.08.07.13.09.05.02.11.03.17.03.06 0 .12-.01.17-.03.05-.02.09-.05.13-.09.07-.07.11-.16.11-.27l.001-.13.119-4.284-.121-4.35c-.01-.11-.04-.2-.11-.27-.04-.04-.08-.07-.13-.09-.05-.02-.11-.03-.17-.03m7.954 9.972c-.377 0-7.255 0-7.29-.003a.425.425 0 0 1-.299-.128.433.433 0 0 1-.126-.307l-.004-8.089a.433.433 0 0 1 .126-.32c.075-.08.177-.126.284-.131.018-.002 4.901-.002 7.162-.002a.39.39 0 0 1 .389.39v.002c.01 2.562.018 7.729.018 8.172 0 .239-.189.431-.43.431z" />
        </svg>
      )
    case "spotify":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z" />
        </svg>
      )
    case "youtube":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
        </svg>
      )
    default:
      return <Music className="h-4 w-4" />
  }
}

// Helper function to get submission type badge
const getTypeBadge = (type: string) => {
  switch (type) {
    case "VIP":
      return <Badge className="bg-purple-600">VIP</Badge>
    case "Skip":
      return <Badge className="bg-blue-600">Skip</Badge>
    case "GA":
      return <Badge className="bg-green-600">GA</Badge>
    case "Free":
      return <Badge variant="outline">Free</Badge>
    default:
      return <Badge variant="outline">{type}</Badge>
  }
}

export default function ArtistProfilePage() {
  const params = useParams()
  const [artist, setArtist] = useState<(typeof temporaryArtists)[0] | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real app, this would be an API call
    const fetchArtist = () => {
      setLoading(true)
      // Simulate API delay
      setTimeout(() => {
        const foundArtist = temporaryArtists.find((a) => a.id === params.id)
        setArtist(foundArtist || null)
        setLoading(false)
      }, 500)
    }

    fetchArtist()
  }, [params.id])

  if (loading) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-12 bg-gray-900 min-h-screen flex items-center justify-center">
          <div className="animate-pulse text-white text-xl">Loading artist profile...</div>
        </div>
      </PageTransition>
    )
  }

  if (!artist) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-12 bg-gray-900 min-h-screen">
          <NavigationDock />
          <Link href="/admin" className="flex items-center text-pink-500 hover:text-pink-400 transition-colors mb-6">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Admin
          </Link>
          <div className="text-center text-white">
            <h1 className="text-3xl font-bold mb-4">Artist Not Found</h1>
            <p className="text-gray-400">The artist you're looking for doesn't exist or has been removed.</p>
          </div>
        </div>
      </PageTransition>
    )
  }

  // Simplify the artist page to remove decorative elements
  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-b from-black via-purple-900/30 to-black relative overflow-hidden">
        {/* Base gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-900/30 to-black z-0"></div>

        {/* Add a horizon line effect */}
        <div
          className="absolute inset-0 bg-gradient-to-t from-transparent via-[#00ffff]/5 to-transparent h-full w-full z-20"
          style={{
            transform: "perspective(1000px) rotateX(80deg)",
            transformOrigin: "bottom",
            bottom: "-5%",
            opacity: 0.7,
          }}
        ></div>

        {/* Add grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20 z-30"></div>

        <NavigationDock />
        <div className="container mx-auto px-4 py-12 text-white min-h-screen relative z-40">
          <Link href="/admin" className="flex items-center text-white hover:text-gray-300 transition-colors mb-6">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Admin
          </Link>

          {/* Artist Header */}
          <div className="mb-8">
            <div className="h-48 md:h-64 rounded-xl overflow-hidden bg-gray-100">
              {artist.coverImages && artist.coverImages.length > 0 && (
                <img
                  src={artist.coverImages[0] || "/placeholder.svg"}
                  alt={`${artist.name} cover`}
                  className="w-full h-full object-cover"
                />
              )}
            </div>
            <div className="absolute -bottom-16 left-8 flex items-end">
              <Avatar className="h-32 w-32 border-4 border-white shadow-sm">
                <AvatarImage src={artist.profileImage || "/placeholder.svg"} alt={artist.name} />
                <AvatarFallback className="bg-gray-200 text-white text-4xl">
                  {artist.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
            </div>
          </div>

          {/* Artist Info */}
          <div className="pt-16 mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white">{artist.name}</h1>
                <div className="flex items-center mt-2 space-x-4">
                  {artist.socialLinks.instagram && (
                    <a
                      href={artist.socialLinks.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-300"
                    >
                      <Instagram className="h-5 w-5" />
                    </a>
                  )}
                  {artist.socialLinks.twitter && (
                    <a
                      href={artist.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-300"
                    >
                      <Twitter className="h-5 w-5" />
                    </a>
                  )}
                  {artist.socialLinks.website && (
                    <a
                      href={artist.socialLinks.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-300"
                    >
                      <Globe className="h-5 w-5" />
                    </a>
                  )}
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <Button className="bg-black hover:bg-gray-800 text-white">Follow Artist</Button>
              </div>
            </div>
            <p className="text-gray-600 mt-4 max-w-3xl">{artist.bio}</p>
          </div>

          {/* Artist Content */}
          <div>
            <Tabs defaultValue="submissions">
              <TabsList className="mb-6">
                <TabsTrigger value="submissions">Submissions</TabsTrigger>
                <TabsTrigger value="gallery">Gallery</TabsTrigger>
              </TabsList>

              <TabsContent value="submissions" className="space-y-4">
                <h2 className="text-xl font-semibold text-white mb-4">Submission History</h2>
                <div className="grid gap-4">
                  {artist.submissions.map((submission) => (
                    <Card key={submission.id} className="bg-black/80 border border-gray-200">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center">
                            <div className="mr-3 text-white">{getPlatformIcon(submission.platform)}</div>
                            <div>
                              <a
                                href={submission.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-white font-medium hover:text-gray-300 flex items-center"
                              >
                                {submission.songTitle}
                                <ExternalLink className="ml-1 h-3 w-3" />
                              </a>
                              <div className="flex items-center mt-1">
                                <Calendar className="h-3 w-3 text-gray-600 mr-1" />
                                <span className="text-xs text-gray-600">{formatDate(submission.submissionTime)}</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <Badge variant="outline" className="bg-black/80 text-white border-gray-300">
                              {submission.submissionType}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="gallery">
                <h2 className="text-xl font-semibold text-white mb-4">Artist Gallery</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {artist.coverImages.map((image, index) => (
                    <div key={index} className="aspect-video rounded-lg overflow-hidden border border-gray-200">
                      <img
                        src={image || "/placeholder.svg"}
                        alt={`${artist.name} gallery ${index + 1}`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
        {/* Add horizon grid lines */}
        <div
          className="absolute bottom-0 left-0 right-0 h-[30vh] z-30"
          style={{
            background:
              "linear-gradient(to top, rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(to right, rgba(0,255,255,0.1) 1px, transparent 1px)",
            backgroundSize: "40px 20px",
            transform: "perspective(500px) rotateX(60deg)",
            transformOrigin: "bottom",
            opacity: 0.2,
          }}
        ></div>
      </div>
    </PageTransition>
  )
}
