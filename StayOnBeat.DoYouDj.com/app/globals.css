@tailwind base;
@tailwind components;
@tailwind utilities;

@import "../styles/fonts.css";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --black: #000;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-neutral-200 dark:border-neutral-800;
  }
  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
    font-family: var(--font-orbitron), system-ui, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
}

/* Custom Styles */
.font-ethnocentric {
  font-family: var(--font-orbitron);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.font-neon {
  font-family: var(--font-tilt-neon);
}

.font-neonderthaw {
  font-family: var(--font-neonderthaw);
}

/* Neon text effects */
.neon-text-small {
  text-shadow: 0 0 2px #00ffff, 0 0 4px #00ffff;
  transition: all 0.3s ease;
}

.neon-text-small:hover {
  text-shadow: 0 0 3px #00ffff, 0 0 6px #00ffff, 0 0 8px #00ffff;
  color: #00ffff !important;
}

.neon-text-red {
  text-shadow: 0 0 3px #ff0000, 0 0 5px #ff0000, 0 0 7px #ff0000, 0 0 9px #ff0000;
  transition: all 0.3s ease;
  color: #ffffff;
}

.neon-text-red:hover {
  text-shadow: 0 0 4px #ff0000, 0 0 8px #ff0000, 0 0 12px #ff0000, 0 0 16px #ff0000;
  color: #ffffff !important;
}

.neon-text-cyan {
  text-shadow: 0 0 3px #ff0000, 0 0 5px #ff0000, 0 0 7px #ff0000, 0 0 9px #ff0000;
  transition: all 0.3s ease;
  color: #ffffff;
}

.neon-text-cyan:hover {
  text-shadow: 0 0 4px #ff0000, 0 0 8px #ff0000, 0 0 12px #ff0000, 0 0 16px #ff0000;
  color: #ffffff !important;
}

/* Footer styles */
footer,
.footer,
[class*="footer"],
[id*="footer"] {
  margin-top: auto;
  padding: 0;
  min-height: auto;
  background: black;
  position: relative;
  z-index: 50;
}

/* Ensure the body extends to the bottom of the viewport */
html {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Add gradient utility classes */
.bg-gradient-primary {
  @apply bg-gradient-to-r from-purple-600 to-indigo-600;
}

.bg-gradient-secondary {
  @apply bg-gradient-to-r from-teal-500 to-emerald-500;
}

.text-gradient-primary {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600;
}

.text-gradient-secondary {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-emerald-500;
}

.border-gradient-primary {
  border-image: linear-gradient(to right, #9333ea, #4f46e5) 1;
}

.border-gradient-secondary {
  border-image: linear-gradient(to right, #14b8a6, #10b981) 1;
}

/* Custom neon box effect */
.neon-box {
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.8), 0 0 10px rgba(255, 0, 0, 0.6), 0 0 15px rgba(255, 0, 0, 0.4), 0 0 20px
    rgba(255, 0, 0, 0.2);
}

.neon-box-cyan {
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6), 0 0 15px rgba(0, 255, 255, 0.4), 0 0 20px
    rgba(0, 255, 255, 0.2);
}

.neon-box-red {
  box-shadow: 0 0 5px rgba(255, 0, 0, 1), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 15px rgba(255, 0, 0, 0.8), 0 0 20px
    rgba(255, 0, 0, 0.7), 0 0 25px rgba(255, 0, 0, 0.6);
}

/* Text scrolling animation */
@keyframes scrollText {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.scroll-text-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

.scroll-text {
  display: inline-block;
  padding-right: 100%; /* Add extra space for seamless looping */
  animation: scrollText 10s linear infinite;
}

/* Pause animation on hover */
.scroll-text-container:hover .scroll-text {
  animation-play-state: paused;
}

/* Custom scrollbar for playlist */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(255, 0, 255, 0.5), rgba(0, 255, 255, 0.5));
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(255, 0, 255, 0.7), rgba(0, 255, 255, 0.7));
}

/* Playlist mode transitions */
body.playlist-mode .neon-player-container {
  transform: translateX(-25%);
}

body.playlist-mode .playlist-panel {
  transform: translateX(0);
  opacity: 1;
  width: 33.333%;
  max-width: 320px;
}

/* Logo styles */
.image-1,
.image-1 * {
  box-sizing: border-box;
}
.image-1 {
  height: auto;
  width: 100%;
  max-width: 100%;
  position: relative;
  overflow: visible;
  object-fit: contain;
}

/* Logo container for different contexts */
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.logo-small {
  height: 60px;
}

.logo-header {
  height: 50px;
  margin-right: 1rem;
}

/* Perspective gradient and horizon effects */
.perspective-grid {
  background-image: linear-gradient(to top, rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to right, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 40px 20px;
  transform: perspective(500px) rotateX(60deg);
  transform-origin: bottom;
  opacity: 0.2;
}

.horizon-glow {
  position: absolute;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.8), transparent);
  box-shadow: 0 0 20px 2px rgba(0, 255, 255, 0.5);
  opacity: 0.6;
}

/* Enhanced neon effects for the perspective theme */
.neon-horizon {
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6), 0 0 15px rgba(0, 255, 255, 0.4), 0 0 20px
    rgba(0, 255, 255, 0.2), 0 20px 40px rgba(0, 0, 0, 0.8);
}

/* Flicker animation for neon effect */
@keyframes flicker {
  0%,
  18%,
  22%,
  25%,
  53%,
  57%,
  100% {
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.5);
  }
  20%,
  24%,
  55% {
    box-shadow: none;
  }
}

.animate-flicker:hover {
  animation: flicker 1.5s infinite alternate;
}

@keyframes shimmer {
  0% {
    opacity: 0.7;
    text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff;
  }
  100% {
    opacity: 1;
    text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff, 0 0 20px #00ffff;
  }
}

/* Navigation button with red glow */
.nav-button-red {
  background: radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%);
  box-shadow: inset 0 0 15px rgba(255, 0, 0, 0.6), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 20px rgba(255, 0, 0, 0.7), 0 0 30px rgba(255, 0, 0, 0.5);
  border: 2px solid #ff0000;
}
