"use client"
import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { Upload, CheckCircle } from "lucide-react"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
// Remove direct NavigationDock import as it's handled by the layout
import { ExpandableTierCards, type TierCardProps } from "@/components/expandable-tier-card"
import { PageTransition } from "@/components/page-transition"
import { supabase, getCurrentUser } from "@/lib/supabase"

// Add Parkinsans font styles
const parkinsansHeading = "parkinsans-submit-heading"
const parkinsansText = "parkinsans-submit-text"
const parkinsansSmall = "parkinsans-submit-small"

// Add CSS for the Parkinsans font
const parkinsansStyles = `
.${parkinsansHeading} {
  font-family: "Parkinsans", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
}
.${parkinsansText} {
  font-family: "Parkinsans", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}
.${parkinsansSmall} {
  font-family: "Parkinsans", sans-serif;
  font-optical-sizing: auto;
  font-weight: 300;
  font-style: normal;
}

/* Add angled gradient glow for the heading */
.neon-turquoise-text {
  color: #00ffff;
  background: linear-gradient(45deg, #ff00ff, #00ffff);
  -webkit-background-clip: text;
  background-clip: text;
  text-shadow: 0 0 2.5px #ff00ff, 0 0 5px #00ffff, 0 0 7.5px rgba(0, 255, 255, 0.5);
}

/* Add neon cyan text effect with reduced glow */
.neon-text-cyan {
  color: #00ffff;
  background: linear-gradient(45deg, #ff00ff, #00ffff);
  -webkit-background-clip: text;
  background-clip: text;
  text-shadow: 0 0 1.5px #ff00ff, 0 0 2.5px #00ffff, 0 0 3.5px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.neon-text-cyan:hover {
  text-shadow: 0 0 2px #ff00ff, 0 0 4px #00ffff, 0 0 6px rgba(0, 255, 255, 0.5);
}

/* Add neon icon glow */
.neon-icon {
  filter: drop-shadow(0 0 5px #00ffff) drop-shadow(0 0 10px #00ffff);
  color: #00ffff;
  transition: all 0.3s ease;
}

.neon-icon:hover {
  filter: drop-shadow(0 0 8px #00ffff) drop-shadow(0 0 15px #00ffff);
  transform: scale(1.05);
}

/* Add neon red glow for buttons */
.neon-red-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.neon-red-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff0000, #ff0000, #ff0000);
  z-index: -1;
  filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.neon-red-button:hover::before {
  opacity: 0.7;
}

/* Add neon button glow for the call to action */
.neon-cta {
  color: #00ffff;
  text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff;
  filter: drop-shadow(0 0 5px #00ffff);
  transition: all 0.3s ease;
}

.neon-cta:hover {
  text-shadow: 0 0 8px #00ffff, 0 0 15px #00ffff;
  filter: drop-shadow(0 0 8px #00ffff);
  transform: scale(1.05);
}
`

export default function SubmitPage() {
  const router = useRouter()
  const [submissionType, setSubmissionType] = useState("url")
  const [submissionTier, setSubmissionTier] = useState("")
  const [artistName, setArtistName] = useState("")
  const [songTitle, setSongTitle] = useState("")
  const [songUrl, setSongUrl] = useState("")
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false) // Simulating login state
  const [activeTier, setActiveTier] = useState<TierCardProps | null>(null)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [userName, setUserName] = useState("")

  // No need for the complex useEffect to connect the tier cards


  // Check authentication status and get current user
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const user = await getCurrentUser()
        if (user) {
          setCurrentUser(user)
          setIsLoggedIn(true)
          setUserName(user.user_metadata?.full_name || user.email || "User")
          setArtistName(user.user_metadata?.full_name || user.email || "Artist")
        } else {
          setIsLoggedIn(false)
          setCurrentUser(null)
        }
      } catch (error) {
        console.error("Error checking auth status:", error)
        setIsLoggedIn(false)
        setCurrentUser(null)
      }
    }

    checkAuthStatus()
  }, [])

  const handleSubmit = async (tier: string) => {
    setSubmissionTier(tier)
    setIsSubmitting(true)

    try {
      // Check if user is authenticated (temporarily allow test submissions)
      if (!currentUser) {
        console.log("No authenticated user, creating test submission...")
        // For testing purposes, create a test user ID
        // In production, this should redirect to login
      }

      // Validate form data
      if (!artistName || !songTitle || (submissionType === "url" && !songUrl)) {
        toast({
          title: "Missing Information",
          description: "Please fill in all required fields.",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // Handle file upload if in upload mode
      let finalUrl = songUrl
      if (submissionType === "upload" && selectedFile) {
        // Create a FormData object to send the file
        const formData = new FormData()
        formData.append("file", selectedFile)

        // Upload the file using the API route
        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) {
          throw new Error("Failed to upload file")
        }

        const data = await response.json()

        // Set the URL to the uploaded file URL
        finalUrl = data.url
        setSongUrl(finalUrl)

        toast({
          title: "File Uploaded Successfully",
          description: "Your audio file has been uploaded.",
        })
      }

      // Submit to Supabase
      try {
        const { data, error } = await supabase
          .from("submissions")
          .insert({
            user_id: currentUser?.id || "test-user-" + Date.now(),
            artist_name: artistName,
            song_title: songTitle,
            url: finalUrl,
            media_url: finalUrl,
            submission_type: tier,
            status: "pending",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (error) throw error

        console.log("Submission successful:", data)

        toast({
          title: "Submission Successful",
          description: `Your track has been added to the queue as a ${tier} submission.`,
        })

        // Reset form
        setSelectedFile(null)
        setSongUrl("")
        setSongTitle("")
        setArtistName("")

        // Redirect to queue page
        router.push("/queue")
      } catch (supabaseError) {
        console.error("Supabase error:", supabaseError)
        throw new Error("Failed to save submission to database")
      }
    } catch (error) {
      console.error("Error submitting track:", error)
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your track. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Form component for each tier - 50% more compact
  const renderTierForm = (tier: string) => {
    return (
      <form
        onSubmit={(e) => {
          e.preventDefault()
          handleSubmit(tier)
        }}
        className="space-y-3"
      >
        {/* Compact layout with grid for artist name and song title */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor={`artistName-${tier}`} className={`text-[#00ffff] text-xs ${parkinsansText}`}>
              Artist Name
            </Label>
            <Input
              id={`artistName-${tier}`}
              placeholder="Artist name"
              value={artistName}
              onChange={(e) => setArtistName(e.target.value)}
              required
              className="h-8 text-sm bg-black border-[#00ffff]/30 text-[#00ffff] focus:border-[#00ffff] focus:ring-[#00ffff]"
            />
          </div>
          <div>
            <Label htmlFor={`songTitle-${tier}`} className={`text-[#00ffff] text-xs ${parkinsansText}`}>
              Song Title
            </Label>
            <Input
              id={`songTitle-${tier}`}
              placeholder="Song title"
              value={songTitle}
              onChange={(e) => setSongTitle(e.target.value)}
              required
              className="h-8 text-sm bg-black border-[#00ffff]/30 text-[#00ffff] focus:border-[#00ffff] focus:ring-[#00ffff]"
            />
          </div>
        </div>

        <Tabs defaultValue="url" onValueChange={setSubmissionType} className="text-white">
          <TabsList className="grid w-full grid-cols-2 bg-gray-800 h-8">
            <TabsTrigger value="url" className="text-xs data-[state=active]:bg-red-900 data-[state=active]:text-white">
              URL Submission
            </TabsTrigger>
            <TabsTrigger value="upload" className="text-xs data-[state=active]:bg-red-900 data-[state=active]:text-white">
              File Upload
            </TabsTrigger>
          </TabsList>
          <TabsContent value="url" className="pt-2">
            <div className="flex flex-col space-y-1">
              <Label htmlFor={`songUrl-${tier}`} className={`text-[#00ffff] text-xs ${parkinsansText}`}>
                Song URL
              </Label>
              <Input
                id={`songUrl-${tier}`}
                placeholder="https://example.com/your-song"
                value={songUrl}
                onChange={(e) => setSongUrl(e.target.value)}
                required={submissionType === "url"}
                className="h-8 text-sm bg-black border-[#00ffff]/30 text-[#00ffff] focus:border-[#00ffff] focus:ring-[#00ffff]"
              />
              <p className={`text-xs text-[#00ffff]/70 ${parkinsansSmall}`}>
                Paste a link from SoundCloud, YouTube, etc.
              </p>
            </div>
          </TabsContent>
          <TabsContent value="upload" className="pt-2">
            <div className="flex flex-col space-y-1">
              <Label htmlFor={`songFile-${tier}`} className={`text-white text-xs ${parkinsansText}`}>
                Upload Song
              </Label>
              <div
                className="border border-dashed border-gray-700 rounded-md p-3 text-center bg-gray-800/50"
                onDragOver={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}
                onDrop={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0]
                    if (file.type.startsWith('audio/') || file.type.startsWith('video/')) {
                      setSelectedFile(file)
                    } else {
                      toast({
                        title: "Invalid File Type",
                        description: "Please upload an audio or video file.",
                        variant: "destructive",
                      })
                    }
                  }
                }}
              >
                {selectedFile ? (
                  <div className="flex items-center space-x-2">
                    <div className="bg-green-900/20 text-green-500 rounded-full p-1">
                      <CheckCircle className="h-4 w-4" />
                    </div>
                    <div className="flex-1 text-left">
                      <p className={`text-xs text-white ${parkinsansText}`}>{selectedFile.name}</p>
                      <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>
                        {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs border-red-700 text-white hover:bg-red-900 hover:text-white"
                      onClick={() => setSelectedFile(null)}
                    >
                      Change
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Upload className="h-4 w-4 text-gray-400" />
                    <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>
                      Drop file or
                    </p>
                    <Input
                      id={`songFile-${tier}`}
                      type="file"
                      accept="audio/*,video/*"
                      className="hidden"
                      required={submissionType === "upload"}
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          setSelectedFile(e.target.files[0])
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs border-red-700 text-white hover:bg-red-900 hover:text-white"
                      onClick={() => {
                        const fileInput = document.getElementById(`songFile-${tier}`) as HTMLInputElement
                        if (fileInput) {
                          fileInput.click()
                        }
                      }}
                    >
                      Browse
                    </Button>
                  </div>
                )}
              </div>
              <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>
                MP3, WAV, MP4, M4A (max 70MB)
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor={`notes-${tier}`} className={`text-[#00ffff] text-xs ${parkinsansText}`}>
              Notes {submissionType === "upload" ? "(Required)" : "(Optional)"}
            </Label>
            <Textarea
              id={`notes-${tier}`}
              placeholder="Additional information"
              rows={2}
              required={submissionType === "upload"}
              className="text-sm bg-black border-[#00ffff]/30 text-[#00ffff] focus:border-[#00ffff] focus:ring-[#00ffff]"
            />
          </div>
          <div className="flex flex-col justify-end">
            <Button
              type="submit"
              className="w-full bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta"
              style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : `Submit ${tier} Track`}
            </Button>
          </div>
        </div>
      </form>
    )
  }

  // Define tier cards
  const tierCards: TierCardProps[] = [
    {
      title: "Premium Priority",
      description: "Highest priority in queue",
      price: "$10",
      imageSrc: "https://4r3ny89ru3.ufs.sh/f/oqT7cVfwtMuVvgEeukWp8NuFTJRyCAxEzOUKoMQ1bHv0Winf",
      color: "rgba(236, 72, 153, 0.7)",
      formContent: renderTierForm("VIP"),
    },
    {
      title: "Skip The Line",
      description: "Get ahead in the queue",
      price: "$5",
      imageSrc: "https://4r3ny89ru3.ufs.sh/f/oqT7cVfwtMuVpT5nr5tIpHQcotxCkg8zbX3O1jndNsMPIGwh",
      color: "rgba(59, 130, 246, 0.7)",
      formContent: renderTierForm("Skip"),
    },
    {
      title: "Guaranteed Play",
      description: "Your track will be played",
      price: "$2",
      imageSrc: "https://4r3ny89ru3.ufs.sh/f/oqT7cVfwtMuVpT5nr5tIpHQcotxCkg8zbX3O1jndNsMPIGwh",
      color: "rgba(22, 163, 74, 0.7)",
      formContent: renderTierForm("GA"),
    },
    {
      title: "Standard Queue",
      description: "Join the standard queue",
      price: "Free",
      imageSrc: "https://4r3ny89ru3.ufs.sh/f/oqT7cVfwtMuVpT5nr5tIpHQcotxCkg8zbX3O1jndNsMPIGwh",
      color: "rgba(255, 255, 255, 0.5)",
      formContent: renderTierForm("Free"),
    },
  ]

  return (
    <PageTransition>
      {/* Using a regular style tag instead of styled-jsx */}
      <div className="min-h-screen bg-gradient-to-b from-black via-purple-900/30 to-black relative overflow-hidden flex flex-col">
        {/* Add the styles directly to a style element */}
        <style dangerouslySetInnerHTML={{ __html: parkinsansStyles }} />
        {/* Base gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-900/30 to-black z-0"></div>

        {/* Add a horizon line effect */}
        <div
          className="absolute inset-0 bg-gradient-to-t from-transparent via-[#00ffff]/5 to-transparent h-full w-full z-20"
          style={{
            transform: "perspective(1000px) rotateX(80deg)",
            transformOrigin: "bottom",
            bottom: "-5%",
            opacity: 0.7,
          }}
        ></div>

        {/* Add grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20 z-30"></div>

        {/* Page Title */}
        <div className="container mx-auto px-4 pt-20 pb-6 relative z-40">
          <h1
            className="text-3xl font-bold neon-turquoise-text text-center"
            style={{ fontFamily: "'Tilt Neon', cursive", display: "inline-block", width: "100%" }}
          >
            Music Submission
          </h1>
        </div>

        {/* Main Content - Two Column Layout with controlled dimensions */}
        <div className="container mx-auto px-4 pb-12 relative z-40 flex-1">
          <div className="flex flex-col lg:flex-row gap-6 justify-center">
            {/* Left Column - Artist Profile - Contained width */}
            <div className="flex flex-col w-full lg:w-[400px]">
              <h2 className={`text-xl font-bold text-[#00ffff] mb-2 ${parkinsansHeading} neon-text-cyan`}>Your Artist Profile</h2>

              {isLoggedIn ? (
                <div className="h-full">
                  {/* Profile Card */}
                  <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box h-full">
                    <CardHeader className="p-3">
                      <CardTitle className={`text-black text-xs ${parkinsansHeading}`}>Artist Profile</CardTitle>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center p-3 bg-black/80 text-white">
                      <Avatar className="h-16 w-16 mb-3 border border-gray-700">
                        <AvatarImage src="/placeholder.svg?height=64&width=64" alt="User Name" />
                        <AvatarFallback className="bg-gray-800 text-white text-sm">
                          {'UN'}
                        </AvatarFallback>
                      </Avatar>
                      <h2 className={`text-sm font-bold text-white mb-1 ${parkinsansText}`}>User Name</h2>
                      <p className={`text-xs text-gray-400 mb-2 ${parkinsansSmall}`}>Artist Profile</p>
                      <div className="bg-black/90 p-2 rounded-lg border border-gray-800 w-full text-center">
                        <p className={`text-gray-300 italic text-xs ${parkinsansText}`}>
                          "Music is the universal language of mankind."
                        </p>
                        <p className={`text-xs text-gray-500 mt-1 ${parkinsansSmall}`}>Latest update: April 1, 2025</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Recent Submissions Card */}
                  <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box">
                    <CardHeader className="p-3">
                      <CardTitle className={`text-black text-xs ${parkinsansHeading}`}>Recent Submissions</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 bg-black/80 text-white">
                      <div className="space-y-2">
                        {/* Sample submissions - would be dynamic in a real app */}
                        <div className="border-b border-gray-800 pb-2">
                          <p className={`font-medium text-white text-xs ${parkinsansText}`}>Midnight Groove</p>
                          <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>Submitted on April 1, 2025</p>
                          <p className={`text-xs text-green-600 mt-1 ${parkinsansSmall}`}>Status: Played</p>
                        </div>
                        <div className="border-b border-gray-800 pb-2">
                          <p className={`font-medium text-white text-xs ${parkinsansText}`}>Urban Echoes</p>
                          <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>Submitted on March 15, 2025</p>
                          <p className={`text-xs text-yellow-600 mt-1 ${parkinsansSmall}`}>Status: In Queue</p>
                        </div>
                        <div>
                          <p className={`font-medium text-white text-xs ${parkinsansText}`}>Deep Dive</p>
                          <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>Submitted on February 28, 2025</p>
                          <p className={`text-xs text-blue-600 mt-1 ${parkinsansSmall}`}>Status: Pending</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Messages Card */}
                  <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box">
                    <CardHeader className="p-3">
                      <CardTitle className={`text-black text-xs ${parkinsansHeading}`}>Messages</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 bg-black/80 text-white">
                      <div className="space-y-2">
                        <div className="bg-black/90 p-2 rounded border border-gray-800">
                          <p className={`text-xs text-gray-300 ${parkinsansText}`}>
                            Loved your last track! Looking forward to hearing more from you.
                          </p>
                          <p className={`text-xs text-gray-500 mt-1 ${parkinsansSmall}`}>From: Host DJ</p>
                          <p className={`text-xs text-gray-500 ${parkinsansSmall}`}>April 1, 2025</p>
                        </div>
                        <div className="bg-black/90 p-2 rounded border border-gray-800">
                          <p className={`text-xs text-gray-300 ${parkinsansText}`}>
                            Your track "Midnight Groove" was featured in our weekly highlights!
                          </p>
                          <p className={`text-xs text-gray-500 mt-1 ${parkinsansSmall}`}>From: Community Manager</p>
                          <p className={`text-xs text-gray-500 ${parkinsansSmall}`}>March 25, 2025</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box h-full">
                  <CardHeader className="p-3">
                    <CardTitle className={`text-black text-xs text-center ${parkinsansHeading}`}>
                      Preview: Your Artist Profile
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3 bg-black/80 text-white">
                    <div className="flex flex-col md:flex-row items-center gap-4">
                      <div className="flex flex-col items-center">
                        <Avatar className="h-16 w-16 mb-2 border border-gray-700">
                          <AvatarImage src="/abstract-profile.png" alt="Your Profile" />
                          <AvatarFallback className="bg-gray-800 text-white">YP</AvatarFallback>
                        </Avatar>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs border-[#ff00ff]/50 text-white hover:bg-[#ff00ff]/20"
                        >
                          Upload Photo
                        </Button>
                      </div>

                      <div className="flex-1 space-y-3 text-center md:text-left">
                        <div>
                          <h3 className={`text-sm font-bold text-white ${parkinsansText}`}>Your Artist Name</h3>
                          <p className={`text-xs text-gray-400 ${parkinsansSmall}`}>Artist / Producer</p>
                        </div>

                        <div className="bg-black/90 p-2 rounded-lg border border-gray-800">
                          <p className={`text-gray-300 italic text-xs ${parkinsansText}`}>
                            "Your artist bio would appear here. Share your story and music style."
                          </p>
                        </div>

                        <div className="flex justify-center md:justify-start gap-2">
                          <span className="inline-flex items-center rounded-full bg-gray-800 px-2 py-1 text-xs">
                            <span className="h-1.5 w-1.5 rounded-full bg-green-500 mr-1"></span>
                            <span className={`${parkinsansSmall}`}>Electronic</span>
                          </span>
                          <span className="inline-flex items-center rounded-full bg-gray-800 px-2 py-1 text-xs">
                            <span className="h-1.5 w-1.5 rounded-full bg-blue-500 mr-1"></span>
                            <span className={`${parkinsansSmall}`}>House</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-3 border-t border-gray-800 text-center">
                      <p className={`text-xs text-gray-400 mb-2 ${parkinsansSmall}`}>
                        Create your profile to track submissions and get feedback
                      </p>
                      <div className="flex justify-center gap-2">
                        <Link href="/login">
                          <Button size="sm" className="bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta" style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}>
                            Sign Up
                          </Button>
                        </Link>
                        <Button size="sm" variant="outline" className="bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta" style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}>
                          Continue as Guest
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Right Column - Submission Tiers - Contained width */}
            <div className="flex flex-col w-full lg:w-[400px]">
              <h2 className={`text-xl font-bold text-[#00ffff] mb-2 ${parkinsansHeading} neon-text-cyan`}>Submit Your Track</h2>

              {/* Expandable Tier Cards */}
              <div className="w-full">
                <ExpandableTierCards tiers={tierCards} onSubmit={handleSubmit} isSubmitting={isSubmitting} />
              </div>
            </div>
          </div>
        </div>

        {/* No duplicate expandable tier cards needed */}
        {/* Add horizon grid lines */}
        <div
          className="absolute bottom-0 left-0 right-0 h-[30vh] z-30"
          style={{
            background:
              "linear-gradient(to top, rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(to right, rgba(0,255,255,0.1) 1px, transparent 1px)",
            backgroundSize: "40px 20px",
            transform: "perspective(500px) rotateX(60deg)",
            transformOrigin: "bottom",
            opacity: 0.2,
          }}
        ></div>
      </div>
    </PageTransition>
  )
}
function setUserName(arg0: string) {
  throw new Error("Function not implemented.")
}

