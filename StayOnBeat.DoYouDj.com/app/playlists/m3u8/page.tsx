"use client"

import { M3U8PlaylistManager } from "@/components/m3u8-playlist-manager"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Upload, Plus } from "lucide-react"

export default function M3U8PlaylistPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">M3U8 Playlist Management</h1>
        <Link href="/playlists/upload">
          <Button className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700">
            <Upload className="h-4 w-4" />
            <span>Upload M3U8</span>
          </Button>
        </Link>
      </div>
      <M3U8PlaylistManager />
    </div>
  )
}
