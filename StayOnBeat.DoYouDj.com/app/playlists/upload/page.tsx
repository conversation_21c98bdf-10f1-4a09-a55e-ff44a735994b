"use client"

import { useState } from "react"
import { M3U8FileUploader } from "@/components/m3u8-file-uploader"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { supabase } from "@/lib/supabase"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

export default function UploadM3U8Page() {
  const [playlistName, setPlaylistName] = useState("")
  const [playlistDescription, setPlaylistDescription] = useState("")
  const [m3u8Url, setM3U8Url] = useState<string | null>(null)
  const [m3u8FileName, setM3U8FileName] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const handleUploadComplete = (url: string, fileName: string) => {
    setM3U8Url(url)
    setM3U8FileName(fileName)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!playlistName || !m3u8Url) {
      toast.error("Please provide a playlist name and upload an M3U8 file")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Create a new playlist entry
      const { data: playlist, error: playlistError } = await supabase
        .from("playlists")
        .insert({
          name: playlistName,
          description: playlistDescription || null,
          visibility: "private", // Default to private
          created_at: new Date().toISOString(),
          is_finalized: true
        })
        .select()
        .single()
      
      if (playlistError) throw playlistError
      
      // Store the M3U8 file reference
      const { error: fileError } = await supabase
        .from("playlist_files")
        .insert({
          playlist_id: playlist.id,
          content: "", // We'll store the URL instead of the content
          format: "m3u8",
          file_url: m3u8Url,
          file_name: m3u8FileName,
          created_at: new Date().toISOString()
        })
      
      if (fileError) throw fileError
      
      toast.success("Playlist created successfully")
      
      // Redirect to the playlists page
      router.push("/playlists/m3u8")
    } catch (error) {
      console.error("Error creating playlist:", error)
      toast.error("Failed to create playlist")
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/playlists/m3u8">
          <Button variant="outline" size="sm" className="flex items-center space-x-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Playlists</span>
          </Button>
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-8">Upload M3U8 Playlist</h1>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Playlist</CardTitle>
          <CardDescription>
            Upload an M3U8 playlist file to create a new playlist
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="playlistName">Playlist Name</Label>
              <Input
                id="playlistName"
                value={playlistName}
                onChange={(e) => setPlaylistName(e.target.value)}
                placeholder="Enter playlist name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="playlistDescription">Description (Optional)</Label>
              <Input
                id="playlistDescription"
                value={playlistDescription}
                onChange={(e) => setPlaylistDescription(e.target.value)}
                placeholder="Enter playlist description"
              />
            </div>
            
            <div className="space-y-2">
              <Label>M3U8 Playlist File</Label>
              <M3U8FileUploader
                onUploadComplete={handleUploadComplete}
                existingFileUrl={m3u8Url || undefined}
                existingFileName={m3u8FileName || undefined}
              />
            </div>
            
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
              disabled={isSubmitting || !m3u8Url}
            >
              {isSubmitting ? "Creating Playlist..." : "Create Playlist"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
