"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { PageTransition } from "@/components/page-transition"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Download, Play, Pause, SkipForward, SkipBack, Volume2 } from "lucide-react"
import { Slider } from "@/components/ui/slider"
import { formatTime } from "@/components/music-player/music-player"

export default function PlaylistPage() {
  const params = useParams()
  const playlistId = params?.id as string

  const [playlist, setPlaylist] = useState<any>(null)
  const [tracks, setTracks] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(80)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  useEffect(() => {
    const fetchPlaylist = async () => {
      try {
        // Fetch playlist details
        const { data: playlistData, error: playlistError } = await supabase
          .from("playlists")
          .select("*")
          .eq("id", playlistId)
          .single()

        if (playlistError) throw playlistError
        setPlaylist(playlistData)

        // Fetch playlist tracks
        const { data: tracksData, error: tracksError } = await supabase
          .from("playlist_items")
          .select("*")
          .eq("playlist_id", playlistId)
          .order("position", { ascending: true })

        if (tracksError) throw tracksError
        setTracks(tracksData || [])
      } catch (error) {
        console.error("Error fetching playlist:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (playlistId) {
      fetchPlaylist()
    }
  }, [playlistId])

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handlePrevious = () => {
    setCurrentTrackIndex((prev) => (prev > 0 ? prev - 1 : 0))
  }

  const handleNext = () => {
    setCurrentTrackIndex((prev) => (prev < tracks.length - 1 ? prev + 1 : prev))
  }

  const handleTrackSelect = (index: number) => {
    setCurrentTrackIndex(index)
    setIsPlaying(true)
  }

  const downloadPlaylist = async () => {
    try {
      const { data, error } = await supabase
        .from("playlist_files")
        .select("content")
        .eq("playlist_id", playlistId)
        .single()

      if (error) throw error

      // Create and download the file
      const blob = new Blob([data.content], { type: "audio/x-mpegurl" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `${playlist.name.replace(/\s+/g, "_")}.m3u8`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading playlist:", error)
    }
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="container mx-auto py-8 px-4">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </PageTransition>
    )
  }

  if (!playlist) {
    return (
      <PageTransition>
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-3xl font-bold mb-6 text-red-500">Playlist Not Found</h1>
          <p>The playlist you're looking for doesn't exist or is not available.</p>
        </div>
      </PageTransition>
    )
  }

  const currentTrack = tracks[currentTrackIndex]

  return (
    <PageTransition>
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold glow-text-pink">{playlist.name}</h1>
            <p className="text-xl neon-text-cyan mt-2">{tracks.length} tracks</p>
          </div>
          <Button onClick={downloadPlaylist} className="bg-purple-600">
            <Download className="h-4 w-4 mr-2" /> Download M3U8
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Player */}
          <div className="lg:col-span-2">
            <div className="bg-black/80 p-6 rounded-lg shadow-sm border border-[#ff00ff]/30 neon-box mb-6">
              <div className="aspect-video bg-black rounded-md mb-4 flex items-center justify-center relative border border-[#00ffff]/50 shadow-[0_0_10px_rgba(0,255,255,0.5),0_0_20px_rgba(0,255,255,0.3)]">
                <div className="text-white text-center">
                  <div className="mb-4">
                    <button
                      onClick={handlePlayPause}
                      className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-black border border-[#00ffff]/50 hover:bg-black/60 transition-all duration-300 shadow-[0_0_10px_rgba(0,255,255,0.5)]"
                    >
                      {isPlaying ? <Pause className="h-6 w-6 text-white" /> : <Play className="h-6 w-6 text-white" />}
                    </button>
                  </div>
                  <h2 className="text-2xl font-bold neon-text-cyan">Now Playing</h2>
                  {currentTrack ? (
                    <p className="text-xl mt-2 neon-text-cyan">
                      {currentTrack.song_title} - {currentTrack.artist_name}
                    </p>
                  ) : (
                    <p className="text-xl mt-2 neon-text-cyan">Select a track to play</p>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-white neon-text-cyan">{formatTime(currentTime)}</span>
                <span className="text-sm text-white neon-text-cyan">{formatTime(duration)}</span>
              </div>

              <Slider
                value={[currentTime]}
                max={duration}
                step={1}
                onValueChange={(value) => setCurrentTime(value[0])}
                className="mb-4"
              />

              <div className="flex items-center justify-center mb-4">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handlePrevious}
                    className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white"
                    disabled={currentTrackIndex === 0}
                  >
                    <SkipBack className="h-5 w-5" />
                  </button>
                  <button
                    onClick={handlePlayPause}
                    className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white"
                  >
                    {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                  </button>
                  <button
                    onClick={handleNext}
                    className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white"
                    disabled={currentTrackIndex === tracks.length - 1}
                  >
                    <SkipForward className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-2 w-48">
                  <Volume2 className="h-4 w-4 text-[#00ffff]" />
                  <Slider value={[volume]} max={100} step={1} onValueChange={(value) => setVolume(value[0])} />
                </div>
              </div>
            </div>
          </div>

          {/* Track List */}
          <div className="lg:col-span-1">
            <div className="bg-black p-6 rounded-lg shadow-sm border border-[#00ffff]/50 neon-box-cyan">
              <h3 className="text-xl font-bold text-white neon-text-cyan mb-4">Tracks</h3>
              <div className="space-y-1 max-h-[500px] overflow-y-auto pr-2 custom-scrollbar">
                {tracks.map((track, index) => (
                  <div
                    key={track.id}
                    className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${
                      index === currentTrackIndex
                        ? "bg-black/60 border border-[#00ffff]/30"
                        : "hover:bg-black/40 border border-[#00ffff]/10"
                    } transition-colors duration-200`}
                    onClick={() => handleTrackSelect(index)}
                  >
                    <div className="flex items-center">
                      <span className="font-mono text-sm w-6 text-[#00ffff]">
                        {(index + 1).toString().padStart(2, "0")}
                      </span>
                      <div className="ml-2">
                        <p className="font-medium line-clamp-1 text-white">{track.song_title}</p>
                        <p className="text-sm text-[#00ffff]/70">{track.artist_name}</p>
                      </div>
                    </div>
                    {index === currentTrackIndex && isPlaying && (
                      <div className="w-4 h-4 rounded-full bg-[#00ffff] animate-pulse"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}
