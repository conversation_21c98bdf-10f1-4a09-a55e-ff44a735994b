"use client"

import { useState, useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Pause, Play, Volume2, RefreshCw } from "lucide-react"
import { <PERSON>lider } from "@/components/ui/slider"
import Link from "next/link"
// Remove direct NavigationDock import as it's handled by the layout
import { supabase } from "@/lib/supabase"
import { PlayerProvider } from "@/contexts/music-player-context"
import { MusicPlayer } from "@/components/music-player/music-player"
import { usePlayer } from "@/contexts/music-player-context"
import type { Track } from "@/contexts/music-player-context"
import { Button } from "@/components/ui/button"
import { Toaster } from "sonner"

function PublicPage() {
  const { setPlaylist, play, pause, state } = usePlayer()
  const [queue, setQueue] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(180)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [showLogo, setShowLogo] = useState(true)

  useEffect(() => {
    // Load logo preference from localStorage
    const savedLogoPreference = localStorage.getItem("showLogo")
    if (savedLogoPreference !== null) {
      setShowLogo(savedLogoPreference === "true")
    }
  }, [])

  useEffect(() => {
    // Simulate playback progress
    let interval: NodeJS.Timeout
    if (isPlaying) {
      interval = setInterval(() => {
        setCurrentTime((prev) => {
          if (prev >= duration) {
            setIsPlaying(false)
            return 0
          }
          return prev + 1
        })
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isPlaying, duration])

  useEffect(() => {
    // Audio visualization simulation
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const drawVisualization = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Only draw active visualization when playing
      if (isPlaying) {
        const barCount = 60
        const barWidth = canvas.width / barCount
        const barMaxHeight = canvas.height * 0.8

        for (let i = 0; i < barCount; i++) {
          // Generate random heights for the visualization bars
          const height = Math.random() * barMaxHeight * (0.5 + Math.sin(Date.now() * 0.001 + i * 0.2) * 0.5)

          // Use a gradient from primary to secondary color
          const gradient = ctx.createLinearGradient(0, canvas.height - height, 0, canvas.height)
          gradient.addColorStop(0, "rgb(236, 72, 153)") // Pink
          gradient.addColorStop(1, "rgb(147, 51, 234)") // Purple

          ctx.fillStyle = gradient
          ctx.fillRect(i * barWidth, canvas.height - height, barWidth - 1, height)
        }
      } else {
        // Draw a flat line when paused
        ctx.beginPath()
        ctx.moveTo(0, canvas.height / 2)
        ctx.lineTo(canvas.width, canvas.height / 2)
        ctx.strokeStyle = "rgba(236, 72, 153, 0.5)" // Pink with opacity
        ctx.lineWidth = 2
        ctx.stroke()
      }

      animationRef.current = requestAnimationFrame(drawVisualization)
    }

    drawVisualization()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "VIP":
        return (
          <Badge className="bg-gradient-to-r from-yellow-500 to-amber-500 border border-yellow-600 text-yellow-100 shadow-sm">
            VIP
          </Badge>
        )
      case "Skip":
        return (
          <Badge className="bg-gradient-to-r from-gray-400 to-slate-400 border border-gray-500 text-white shadow-sm">
            Skip
          </Badge>
        )
      case "GA":
        return (
          <Badge className="bg-gradient-to-r from-amber-700 to-yellow-800 border border-amber-800 text-amber-100 shadow-sm">
            GA
          </Badge>
        )
      case "Free":
        return (
          <Badge className="bg-gradient-to-r from-emerald-600 to-green-600 border border-emerald-700 text-white shadow-sm">
            Free
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  // Function to fetch queue data from Supabase
  const fetchQueueFromSupabase = async () => {
    try {
      setIsLoading(true)
      const { data, error } = await supabase.from("playlist").select("*").order("position", { ascending: true })

      if (error) throw error
      setQueue(data || [])

      // Convert to Track format for the music player
      if (data && data.length > 0) {
        const tracks: Track[] = data.map((item) => ({
          id: item.id,
          title: item.songTitle,
          artist: item.artistName,
          artwork_url: item.artworkUrl || "/placeholder.svg",
          audio_url: item.url,
          duration: item.duration || 180,
          created_at: new Date().toISOString(),
        }))

        // Set the playlist in the player context
        setPlaylist(tracks)
      }
    } catch (error) {
      console.error("Error fetching queue from Supabase:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch queue data on component mount
  useEffect(() => {
    fetchQueueFromSupabase()

    // Set up realtime subscription
    const subscription = supabase
      .channel("playlist_changes")
      .on("postgres_changes", { event: "*", schema: "public", table: "playlist" }, (payload) => {
        // Refresh the queue when changes occur
        fetchQueueFromSupabase()
      })
      .subscribe()

    return () => {
      // Clean up subscription
      subscription.unsubscribe()
    }
  }, [setPlaylist])

  // Update player state from Supabase
  useEffect(() => {
    const fetchPlayerState = async () => {
      try {
        // Check if the player_status table exists first
        const { error: tableCheckError } = await supabase.from("player_status").select("count").limit(1).single()

        // If the table doesn't exist, create default state and return
        if (
          tableCheckError &&
          (tableCheckError.code === "42P01" || tableCheckError.message.includes("does not exist"))
        ) {
          console.log("Player status table doesn't exist yet. Using default values.")
          return // Just use the default state values
        }

        // If we get here, the table exists, so try to fetch the data
        const { data, error } = await supabase.from("player_status").select("*").eq("id", 1).single()

        if (error) {
          if (error.code !== "PGRST116") {
            // PGRST116 is "Row not found" error
            console.error("Error fetching player state:", error)
          }
          return
        }

        if (data) {
          setIsPlaying(data.is_playing || false)
          setCurrentTime(data.current_time || 0)
        }
      } catch (error) {
        console.error("Error in fetchPlayerState:", error)
        // Continue with default values
      }
    }

    fetchPlayerState()

    // Set up realtime subscription for player status
    const subscription = supabase
      .channel("player_status_changes")
      .on("postgres_changes", { event: "*", schema: "public", table: "player_status" }, (payload) => {
        if (payload.new) {
          setIsPlaying(payload.new.is_playing || false)
          setCurrentTime(payload.new.current_time || 0)
        }
      })
      .subscribe()

    return () => {
      // Clean up subscription
      subscription.unsubscribe()
    }
  }, [])

  return (
    <div className="container mx-auto px-4 py-8 text-white min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold glow-text-pink">Live Music Queue</h1>
        <p className="text-xl neon-text-cyan mt-3.5">Watch and listen to the current playlist</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Music Player */}
        <div className="lg:col-span-4">
          <MusicPlayer />
        </div>

        {/* Queue List */}
        <div className="lg:col-span-4">
          <div className="bg-black p-6 rounded-lg shadow-sm border border-[#00ffff]/50 neon-box-cyan">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white neon-text-cyan">Current Queue</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchQueueFromSupabase}
                disabled={isLoading}
                className="border-[#00ffff]/50 text-[#00ffff]"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
                Refresh
              </Button>
            </div>
            <div className="space-y-1 max-h-[500px] overflow-y-auto pr-2 custom-scrollbar">
              {queue.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-[#00ffff]/70">No items in queue</p>
                </div>
              ) : (
                queue.map((item) => (
                  <div
                    key={item.id}
                    className={`flex items-center justify-between p-2 rounded-md ${
                      item.position === 1
                        ? "bg-black/60 border border-[#00ffff]/30"
                        : "hover:bg-black/40 border border-[#00ffff]/10"
                    } transition-colors duration-200`}
                  >
                    <div className="flex items-center">
                      <span className="font-mono text-sm w-6 text-[#00ffff]">
                        {item.position.toString().padStart(2, "0")}
                      </span>
                      <div className="ml-2">
                        <p className="font-medium line-clamp-1 text-white">{item.songTitle}</p>
                        <p className="text-sm text-[#00ffff]/70">{item.artistName}</p>
                      </div>
                    </div>
                    <div>
                      <Badge variant="outline" className="bg-black text-white border-[#00ffff]/50 neon-text-cyan">
                        {item.type}
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center">
        <Link href="/playlists" className="text-[#00ffff] hover:text-[#00ffff]/70 transition-colors neon-text-cyan">
          View All Playlists
        </Link>
      </div>
      <Toaster />
    </div>
  )
}

// Export the page wrapped with PlayerProvider
export default function QueuePage() {
  return (
    <PlayerProvider>
      <PublicPage />
    </PlayerProvider>
  )
}
