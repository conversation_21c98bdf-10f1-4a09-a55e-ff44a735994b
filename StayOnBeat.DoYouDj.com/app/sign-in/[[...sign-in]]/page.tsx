import { SignIn } from "@clerk/nextjs";
import { PageTransition } from "@/components/page-transition";

export default function SignInPage() {
  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-b from-black via-purple-900/30 to-black relative overflow-hidden flex flex-col items-center justify-center">
        {/* Base gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-900/30 to-black z-0"></div>

        {/* Add a horizon line effect */}
        <div
          className="absolute inset-0 bg-gradient-to-t from-transparent via-[#00ffff]/5 to-transparent h-full w-full z-20"
          style={{
            transform: "perspective(1000px) rotateX(80deg)",
            transformOrigin: "bottom",
            bottom: "-5%",
            opacity: 0.7,
          }}
        ></div>

        {/* Add grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20 z-30"></div>

        {/* Page Title */}
        <div className="container mx-auto px-4 pt-20 pb-6 relative z-40">
          <h1
            className="text-3xl font-bold neon-turquoise-text text-center"
            style={{ fontFamily: "var(--font-neonderthaw)", display: "inline-block", width: "100%" }}
          >
            Sign In to StayOnBeat
          </h1>
        </div>

        {/* Sign In Component */}
        <div className="relative z-40 w-full max-w-md">
          <SignIn
            appearance={{
              elements: {
                formButtonPrimary:
                  "bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10",
                card:
                  "bg-black/80 border border-[#00ffff]/30 shadow-[0_0_10px_#00ffff]",
                headerTitle:
                  "text-[#00ffff]",
                headerSubtitle:
                  "text-[#00ffff]/70",
                formFieldLabel:
                  "text-[#00ffff]",
                formFieldInput:
                  "bg-black/50 border-[#00ffff]/30 text-white",
                footerActionLink:
                  "text-[#00ffff] hover:text-[#00ffff]/70",
              },
            }}
            routing="path"
            path="/sign-in"
            signUpUrl="/sign-up"
          />
        </div>

        {/* Add horizon grid lines */}
        <div
          className="absolute bottom-0 left-0 right-0 h-[30vh] z-30"
          style={{
            background:
              "linear-gradient(to top, rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(to right, rgba(0,255,255,0.1) 1px, transparent 1px)",
            backgroundSize: "40px 20px",
            transform: "perspective(500px) rotateX(60deg)",
            transformOrigin: "bottom",
            opacity: 0.2,
          }}
        ></div>
      </div>
    </PageTransition>
  );
}
