"use client"

import { MusicPlayerProvider } from "@/contexts/music-player-context"
import { SpotifyTrackPlayer } from "@/components/spotify-track-player"
import { PageTransition } from "@/components/page-transition"
import { NavigationDock } from "@/components/navigation-dock"

export default function SpotifyTracksPage() {
  return (
    <PageTransition>
      <MusicPlayerProvider>
        <div className="container mx-auto px-4 py-8">
          <NavigationDock />

          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold glow-text-pink">Spotify Indie Artists</h1>
            <p className="text-xl neon-text-cyan mt-3.5">Discover and play tracks from independent artists</p>
          </div>

          <div className="max-w-4xl mx-auto">
            <SpotifyTrackPlayer />
          </div>
        </div>
      </MusicPlayerProvider>
    </PageTransition>
  )
}
