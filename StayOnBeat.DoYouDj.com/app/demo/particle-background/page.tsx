"use client"

import { useState } from "react"
import ParticleBackground from "@/components/particle-background"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export default function ParticleBackgroundDemo() {
  const [particleCount, setParticleCount] = useState(100)
  const [particleColor, setParticleColor] = useState("#00ffff")
  const [interactive, setInteractive] = useState(true)
  const [connectParticles, setConnectParticles] = useState(true)
  const [maxSpeed, setMaxSpeed] = useState(0.5)

  return (
    <div className="min-h-screen">
      <ParticleBackground
        particleCount={particleCount}
        particleColor={particleColor}
        interactive={interactive}
        connectParticles={connectParticles}
        maxSpeed={maxSpeed}
      />

      <div className="container mx-auto p-4 relative z-10">
        <div className="max-w-md mx-auto bg-black/80 p-6 rounded-lg border border-[#00ffff]/50 neon-box-cyan mt-10">
          <h1 className="text-2xl font-bold mb-6 text-center text-white">Particle Background Demo</h1>

          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="particleCount">Particle Count: {particleCount}</Label>
              </div>
              <Slider
                id="particleCount"
                min={10}
                max={300}
                step={10}
                value={[particleCount]}
                onValueChange={(value) => setParticleCount(value[0])}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="particleColor">Particle Color</Label>
              <div className="flex gap-4">
                <input
                  type="color"
                  id="particleColor"
                  value={particleColor}
                  onChange={(e) => setParticleColor(e.target.value)}
                  className="w-10 h-10 rounded cursor-pointer"
                />
                <div className="flex-1 grid grid-cols-4 gap-2">
                  {["#00ffff", "#ff00ff", "#ffff00", "#ffffff"].map((color) => (
                    <button
                      key={color}
                      className="w-full h-10 rounded-md border"
                      style={{ backgroundColor: color }}
                      onClick={() => setParticleColor(color)}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="maxSpeed">Max Speed: {maxSpeed.toFixed(1)}</Label>
              </div>
              <Slider
                id="maxSpeed"
                min={0.1}
                max={2}
                step={0.1}
                value={[maxSpeed]}
                onValueChange={(value) => setMaxSpeed(value[0])}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="interactive">Interactive</Label>
              <Switch id="interactive" checked={interactive} onCheckedChange={setInteractive} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="connectParticles">Connect Particles</Label>
              <Switch id="connectParticles" checked={connectParticles} onCheckedChange={setConnectParticles} />
            </div>

            <Button className="w-full" onClick={() => window.history.back()}>
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
