"use client"

import { useState } from "react"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import VercelGridBackground from "@/components/vercel-grid-background"

export default function VercelBackgroundDemo() {
  const [dotColor, setDotColor] = useState("rgba(0, 255, 255, 0.3)")
  const [dotSize, setDotSize] = useState(1.2)
  const [dotSpacing, setDotSpacing] = useState(25)
  const [animationSpeed, setAnimationSpeed] = useState(0.3)
  const [backgroundColor, setBackgroundColor] = useState("#000000")

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md bg-black/80 backdrop-blur-sm p-6 rounded-lg border border-cyan-500/30 space-y-6 z-10">
        <h1 className="text-2xl font-bold text-center text-white">Vercel Grid Background</h1>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="dotColor" className="text-white">
              Dot Color
            </Label>
            <Input
              id="dotColor"
              type="text"
              value={dotColor}
              onChange={(e) => setDotColor(e.target.value)}
              className="bg-black/50 border-cyan-500/30 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dotSize" className="text-white">
              Dot Size: {dotSize}
            </Label>
            <Slider
              id="dotSize"
              min={0.5}
              max={3}
              step={0.1}
              value={[dotSize]}
              onValueChange={(value) => setDotSize(value[0])}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dotSpacing" className="text-white">
              Dot Spacing: {dotSpacing}px
            </Label>
            <Slider
              id="dotSpacing"
              min={10}
              max={50}
              step={1}
              value={[dotSpacing]}
              onValueChange={(value) => setDotSpacing(value[0])}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="animationSpeed" className="text-white">
              Animation Speed: {animationSpeed}
            </Label>
            <Slider
              id="animationSpeed"
              min={0.1}
              max={2}
              step={0.1}
              value={[animationSpeed]}
              onValueChange={(value) => setAnimationSpeed(value[0])}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="backgroundColor" className="text-white">
              Background Color
            </Label>
            <Input
              id="backgroundColor"
              type="text"
              value={backgroundColor}
              onChange={(e) => setBackgroundColor(e.target.value)}
              className="bg-black/50 border-cyan-500/30 text-white"
            />
          </div>
        </div>
      </div>

      <VercelGridBackground
        dotColor={dotColor}
        dotSize={dotSize}
        dotSpacing={dotSpacing}
        backgroundColor={backgroundColor}
        animationSpeed={animationSpeed}
      />
    </div>
  )
}
