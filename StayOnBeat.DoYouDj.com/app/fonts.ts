import { <PERSON><PERSON>ron, Tilt_Neon, <PERSON><PERSON><PERSON>_Glitch, <PERSON><PERSON><PERSON>_Burned } from 'next/font/google';

export const orbitron = Orbitron({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-orbitron',
});

export const tiltNeon = Tilt_Neon({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-tilt-neon',
});

// Using Rubik Glitch as a neon-style font (replacement for Neonderthaw)
export const neonderthaw = Rubik_Glitch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-neonderthaw',
  weight: ['400'],
});

// Adding Rubik Burned as another neon-style font option
export const neonify = Rubik_Burned({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-neonify',
  weight: ['400'],
});