-- Clear existing data and insert your real URLs
DELETE FROM playlist;
DELETE FROM submissions;

-- Insert your real URLs into submissions
INSERT INTO submissions (artist_name, song_title, submission_type, url, media_url, status, created_at) VALUES
('Real Artist 1', 'SoundCloud Track', 'Free', 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', 'confirmed', NOW()),
('Real Artist 2', 'YouTube Music Track 1', 'Free', 'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'confirmed', NOW()),
('Real Artist 3', 'Spotify Track', 'Free', 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185', 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185', 'confirmed', NOW()),
('Real Artist 4', 'YouTube Music Track 2', 'Free', 'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'confirmed', NOW());

-- Insert into playlist table (2025-05-26 Episode#Test001)
INSERT INTO playlist (position, artist_name, song_title, type, url, media_url, created_at) VALUES
(1, 'Real Artist 1', 'SoundCloud Track', 'Free', 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', NOW()),
(2, 'Real Artist 2', 'YouTube Music Track 1', 'Free', 'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', NOW()),
(3, 'Real Artist 3', 'Spotify Track', 'Free', 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185', 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185', NOW()),
(4, 'Real Artist 4', 'YouTube Music Track 2', 'Free', 'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', NOW());
