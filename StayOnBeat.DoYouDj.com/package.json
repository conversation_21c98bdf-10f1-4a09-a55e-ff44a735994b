{"name": "webhook-service", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "test": "jest"}, "dependencies": {"@clerk/nextjs": "6.19.4", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@planetscale/database": "1.19.0", "@radix-ui/react-alert-dialog": "1.1.13", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-radio-group": "1.3.6", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-slider": "1.3.4", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-switch": "1.2.4", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-toast": "1.2.13", "@supabase/auth-ui-react": "0.4.7", "@supabase/auth-ui-shared": "0.1.8", "@supabase/supabase-js": "2.49.4", "@types/pg": "8.15.2", "@types/react-beautiful-dnd": "13.1.8", "@uploadthing/react": "7.3.1", "@vercel/kv": "1.0.1", "@videojs/http-streaming": "3.17.0", "axios": "^1.6.7", "class-variance-authority": "0.7.1", "clsx": "^2.1.0", "date-fns": "^3.3.1", "framer-motion": "^11.0.3", "frontend-collective-react-dnd-scrollzone": "1.0.2", "hoist-non-react-statics": "3.3.2", "lucide-react": "0.330.0", "mysql2": "3.14.1", "next": "15.3.2", "next-themes": "0.2.1", "pg": "8.16.0", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-display-name": "0.2.5", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.2.0", "react-hook-form": "^7.50.1", "react-icons": "5.5.0", "sonner": "2.0.3", "swr": "^2.2.4", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uploadthing": "7.7.2", "video.js": "8.22.0", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.16", "@types/react": "^18.2.52", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.2.28", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "supabase": "2.23.4", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}