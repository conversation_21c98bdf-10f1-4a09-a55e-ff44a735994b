-- Sample data for StayOnBeat local development
-- This creates test data for the complete URL processing workflow

-- Insert sample admin user (you'll need to sign up first, then update is_admin)
-- This is just a placeholder - real users are created through auth

-- Insert sample submissions with real URLs for testing
INSERT INTO public.submissions (
  artist_name, 
  song_title, 
  submission_type, 
  platform, 
  platform_id,
  content_type,
  processing_status,
  metadata_extracted,
  url, 
  media_url,
  status,
  duration,
  artwork_url,
  genre,
  is_explicit,
  created_at
) VALUES 
-- Spotify submissions
(
  'Daft Punk', 
  '<PERSON> <PERSON> (feat. <PERSON><PERSON><PERSON>)', 
  'Free', 
  'spotify', 
  '2FNvO68ODWQD4XOmm6gzEB',
  'track',
  'completed',
  true,
  'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185', 
  'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB',
  'confirmed',
  248,
  'https://i.scdn.co/image/ab67616d0000b273d144c3d52c8d347c13eabef6',
  'Electronic',
  false,
  NOW() - INTERVAL '2 hours'
),
-- YouTube submissions  
(
  'The Weeknd', 
  'Blinding Lights', 
  'VIP', 
  'youtube', 
  '7vj1S9qqq7o',
  'track',
  'completed',
  true,
  'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 
  'https://music.youtube.com/watch?v=7vj1S9qqq7o',
  'confirmed',
  200,
  'https://i.ytimg.com/vi/7vj1S9qqq7o/maxresdefault.jpg',
  'Pop',
  false,
  NOW() - INTERVAL '1 hour'
),
-- SoundCloud submissions
(
  'SoundCloud Artist', 
  'Electronic Vibes', 
  'GA', 
  'soundcloud', 
  'dvpjnwPokiHuvvtG9',
  'track',
  'completed',
  true,
  'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', 
  'https://on.soundcloud.com/dvpjnwPokiHuvvtG9',
  'confirmed',
  180,
  'https://placehold.co/400x400/orange/white?text=SoundCloud',
  'Electronic',
  false,
  NOW() - INTERVAL '30 minutes'
),
-- Another YouTube submission
(
  'YouTube Artist', 
  'Music Video Track', 
  'Free', 
  'youtube', 
  'dbx8Yf9U3qk',
  'track',
  'completed',
  true,
  'https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW', 
  'https://music.youtube.com/watch?v=dbx8Yf9U3qk',
  'confirmed',
  220,
  'https://i.ytimg.com/vi/dbx8Yf9U3qk/maxresdefault.jpg',
  'Pop',
  false,
  NOW() - INTERVAL '15 minutes'
),
-- Pending submission for testing
(
  'Pending Artist', 
  'Awaiting Review', 
  'Skip', 
  'spotify', 
  'pending123',
  'track',
  'processing',
  false,
  'https://open.spotify.com/track/pending123', 
  'https://open.spotify.com/track/pending123',
  'pending',
  NULL,
  NULL,
  NULL,
  false,
  NOW() - INTERVAL '5 minutes'
);

-- Insert sample playlist items (approved submissions)
INSERT INTO public.playlist (
  position,
  artist_name,
  song_title,
  type,
  submission_id,
  platform,
  url,
  media_url,
  created_at
) VALUES 
(1, 'Daft Punk', 'Get Lucky (feat. Pharrell Williams)', 'Free', 1, 'spotify', 
 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185',
 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB', NOW() - INTERVAL '1 hour'),
(2, 'The Weeknd', 'Blinding Lights', 'VIP', 2, 'youtube',
 'https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW',
 'https://music.youtube.com/watch?v=7vj1S9qqq7o', NOW() - INTERVAL '45 minutes'),
(3, 'SoundCloud Artist', 'Electronic Vibes', 'GA', 3, 'soundcloud',
 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9',
 'https://on.soundcloud.com/dvpjnwPokiHuvvtG9', NOW() - INTERVAL '30 minutes');

-- Insert sample tracks (normalized metadata)
INSERT INTO public.tracks (
  title,
  artist,
  album,
  duration,
  artwork_url,
  platform,
  platform_id,
  genre,
  is_explicit,
  spotify_id,
  youtube_id,
  metadata,
  created_at
) VALUES 
('Get Lucky (feat. Pharrell Williams)', 'Daft Punk', 'Random Access Memories', 248,
 'https://i.scdn.co/image/ab67616d0000b273d144c3d52c8d347c13eabef6',
 'spotify', '2FNvO68ODWQD4XOmm6gzEB', 'Electronic', false,
 '2FNvO68ODWQD4XOmm6gzEB', NULL,
 '{"release_date": "2013-05-17", "popularity": 85, "explicit": false}',
 NOW() - INTERVAL '2 hours'),
('Blinding Lights', 'The Weeknd', 'After Hours', 200,
 'https://i.ytimg.com/vi/7vj1S9qqq7o/maxresdefault.jpg',
 'youtube', '7vj1S9qqq7o', 'Pop', false,
 NULL, '7vj1S9qqq7o',
 '{"release_date": "2019-11-29", "views": "1.2B", "explicit": false}',
 NOW() - INTERVAL '1 hour'),
('Electronic Vibes', 'SoundCloud Artist', NULL, 180,
 'https://placehold.co/400x400/orange/white?text=SoundCloud',
 'soundcloud', 'dvpjnwPokiHuvvtG9', 'Electronic', false,
 NULL, NULL,
 '{"plays": "50K", "likes": "2.1K", "reposts": "450"}',
 NOW() - INTERVAL '30 minutes');

-- Insert sample artist library entries
INSERT INTO public.artist_library (
  name,
  normalized_name,
  bio,
  profile_image,
  social_links,
  platforms,
  submission_count,
  play_count,
  last_submission,
  is_verified,
  created_at
) VALUES 
('Daft Punk', 'daft punk', 
 'French electronic music duo formed in 1993.',
 'https://i.scdn.co/image/ab6761610000e5eb0c68f6c95232e716f0abee8d',
 '{"spotify": "https://open.spotify.com/artist/4tZwfgrHOc3mvqYlEYSvVi", "website": "https://daftpunk.com"}',
 '{"spotify": {"id": "4tZwfgrHOc3mvqYlEYSvVi", "followers": 8500000}, "youtube": {"id": "UC_kRDKYrUlrbtrSiyu5Tflg", "subscribers": 2800000}}',
 1, 0, NOW() - INTERVAL '2 hours', true, NOW() - INTERVAL '2 hours'),
('The Weeknd', 'the weeknd',
 'Canadian singer, songwriter, and record producer.',
 'https://i.scdn.co/image/ab6761610000e5eb0c68f6c95232e716f0abee8d',
 '{"spotify": "https://open.spotify.com/artist/1Xyo4u8uXC1ZmMpatF05PJ", "instagram": "@theweeknd"}',
 '{"spotify": {"id": "1Xyo4u8uXC1ZmMpatF05PJ", "followers": 45000000}, "youtube": {"id": "UC0WP5P-ufpRfjbNrmOWwLBQ", "subscribers": 15000000}}',
 1, 0, NOW() - INTERVAL '1 hour', true, NOW() - INTERVAL '1 hour'),
('SoundCloud Artist', 'soundcloud artist',
 'Independent electronic music producer.',
 'https://placehold.co/400x400/orange/white?text=SC',
 '{"soundcloud": "https://soundcloud.com/soundcloudartist"}',
 '{"soundcloud": {"id": "soundcloudartist", "followers": 5000}}',
 1, 0, NOW() - INTERVAL '30 minutes', false, NOW() - INTERVAL '30 minutes');

-- Insert sample played tracks (for history)
INSERT INTO public.played_tracks (
  artist_name,
  song_title,
  played_at,
  submission_id,
  platform,
  url,
  media_url,
  created_at
) VALUES 
('Previous Artist 1', 'Previously Played Track 1', NOW() - INTERVAL '3 hours', NULL, 'spotify',
 'https://open.spotify.com/track/example1', 'https://open.spotify.com/track/example1', NOW() - INTERVAL '3 hours'),
('Previous Artist 2', 'Previously Played Track 2', NOW() - INTERVAL '2.5 hours', NULL, 'youtube',
 'https://music.youtube.com/watch?v=example2', 'https://music.youtube.com/watch?v=example2', NOW() - INTERVAL '2.5 hours');

-- Create a test admin user profile (this will need to be updated after actual user signup)
-- You'll need to sign up through the app first, then run:
-- UPDATE public.profiles SET is_admin = true WHERE email = '<EMAIL>';
