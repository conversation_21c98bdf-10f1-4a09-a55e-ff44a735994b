-- Create player_status table for storing the current player state
CREATE TABLE IF NOT EXISTS public.player_status (
    id BIGINT PRIMARY KEY,
    is_playing BOOLEAN DEFAULT false,
    current_track_id BIGINT,
    current_time FLOAT DEFAULT 0,
    duration FLOAT DEFAULT 0,
    volume FLOAT DEFAULT 0.7,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to update playlist positions after reordering
CREATE OR REPLACE FUNCTION update_playlist_positions()
RETURNS VOID AS $$
BEGIN
    -- Update positions based on current order
    WITH indexed AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY position) AS new_position
        FROM public.playlist
    )
    UPDATE public.playlist p
    SET position = i.new_position
    FROM indexed i
    WHERE p.id = i.id;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies
ALTER TABLE public.player_status ENABLE ROW LEVEL SECURITY;

-- Allow anyone to read player status
CREATE POLICY "Allow anyone to read player status" 
ON public.player_status FOR SELECT 
USING (true);

-- Allow authenticated users to update player status
CREATE POLICY "Allow authenticated users to update player status" 
ON public.player_status FOR UPDATE 
USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert player status
CREATE POLICY "Allow authenticated users to insert player status" 
ON public.player_status FOR INSERT 
WITH CHECK (auth.role() = 'authenticated');

-- Insert default player status record
INSERT INTO public.player_status (id, is_playing, current_track_id, current_time, duration, volume, updated_at)
VALUES (1, false, null, 0, 0, 0.7, NOW())
ON CONFLICT (id) DO NOTHING;
