-- Enable realtime for the tables we want to subscribe to
ALTER PUBLICATION supabase_realtime ADD TABLE public.submissions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.playlist;
ALTER PUBLICATION supabase_realtime ADD TABLE public.played_tracks;
ALTER PUBLICATION supabase_realtime ADD TABLE public.settings;

-- Create a function to broadcast player status changes
CREATE OR REPLACE FUNCTION broadcast_player_status()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    'player_status_change',
    json_build_object(
      'track_id', NEW.id,
      'artist_name', NEW.artist_name,
      'song_title', NEW.song_title,
      'position', NEW.position,
      'updated_at', NEW.updated_at
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to broadcast player status changes
DROP TRIGGER IF EXISTS on_playlist_update ON public.playlist;
CREATE TRIGGER on_playlist_update
  AFTER UPDATE ON public.playlist
  FOR EACH ROW
  WHEN (OLD.position = 1 OR NEW.position = 1)
  EXECUTE FUNCTION broadcast_player_status();
