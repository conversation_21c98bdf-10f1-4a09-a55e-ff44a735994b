-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.settings (
    id TEXT PRIMARY KEY,
    value JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Grant access to authenticated users
CREATE POLICY "Allow authenticated users to read settings" 
ON public.settings FOR SELECT 
TO authenticated 
USING (true);

-- Grant access to service role
CREATE POLICY "Allow service role to manage settings" 
ON public.settings FOR ALL 
TO service_role 
USING (true);

-- Insert default settings if they don't exist
INSERT INTO public.settings (id, value)
VALUES 
  ('default_background_image', '{"url": null}'),
  ('default_logo_image', '{"url": null}'),
  ('default_profile_image', '{"url": null}')
ON CONFLICT (id) DO NOTHING;
