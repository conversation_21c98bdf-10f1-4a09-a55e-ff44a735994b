-- Create playlists table if it doesn't exist
CREATE TABLE IF NOT EXISTS playlists (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  visibility TEXT NOT NULL DEFAULT 'admin', -- 'admin', 'private', or 'public'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  track_count INTEGER DEFAULT 0,
  is_finalized BOOLEAN DEFAULT FALSE
);

-- Create playlist_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS playlist_items (
  id SERIAL PRIMARY KEY,
  playlist_id INTEGER REFERENCES playlists(id) ON DELETE CASCADE,
  track_id INTEGER NOT NULL,
  position INTEGER NOT NULL,
  artist_name TEXT,
  song_title TEXT,
  url TEXT,
  platform TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create playlist_files table to store M3U8 content
CREATE TABLE IF NOT EXISTS playlist_files (
  id SERIAL PRIMARY KEY,
  playlist_id INTEGER REFERENCES playlists(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  format TEXT DEFAULT 'm3u8',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS playlist_items_playlist_id_idx ON playlist_items(playlist_id);
CREATE INDEX IF NOT EXISTS playlist_files_playlist_id_idx ON playlist_files(playlist_id);
CREATE INDEX IF NOT EXISTS playlists_visibility_idx ON playlists(visibility);

-- Add RLS policies
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_files ENABLE ROW LEVEL SECURITY;

-- Admin can do anything
CREATE POLICY admin_all_playlists ON playlists FOR ALL TO authenticated USING (true);
CREATE POLICY admin_all_playlist_items ON playlist_items FOR ALL TO authenticated USING (true);
CREATE POLICY admin_all_playlist_files ON playlist_files FOR ALL TO authenticated USING (true);

-- Public can only see public playlists
CREATE POLICY public_view_public_playlists ON playlists FOR SELECT TO anon USING (visibility = 'public');
CREATE POLICY public_view_public_playlist_items ON playlist_items FOR SELECT TO anon USING (
  EXISTS (SELECT 1 FROM playlists WHERE playlists.id = playlist_items.playlist_id AND playlists.visibility = 'public')
);
CREATE POLICY public_view_public_playlist_files ON playlist_files FOR SELECT TO anon USING (
  EXISTS (SELECT 1 FROM playlists WHERE playlists.id = playlist_files.playlist_id AND playlists.visibility = 'public')
);
