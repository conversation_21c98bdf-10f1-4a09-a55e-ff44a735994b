-- Add Spotify tracks to the songs table for use in admin playlists
-- These will be available for the admin/DJ to add to playlists

-- Make sure we have the necessary columns in the songs table
ALTER TABLE IF EXISTS songs 
ADD COLUMN IF NOT EXISTS spotify_id TEXT,
ADD COLUMN IF NOT EXISTS isrc TEXT;

-- Insert tracks from Spotify library
INSERT INTO songs (title, artist_name, media_url, spotify_id, isrc, duration, created_at)
VALUES
  ('<PERSON> Nota', 'Zambra<PERSON>', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', '1hCuL9k57U5jAqZjAs9pLs', 'TCAFZ2118532', 180, NOW()),
  ('Payroll', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3', '04Lf0tQ8rVSwsctTg5VQyq', 'QZMEQ2123203', 210, NOW()),
  ('Keep My Head Up', 'P-Ca$h', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3', '79dik5E8GkK2Tmn2O4QHWg', 'QZHN32106927', 195, NOW()),
  ('Duele Quererte', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3', '5yvj2K6XRcoQDVKgPiZtCI', 'QZNWT2189516', 225, NOW()),
  ('Symmetry', 'Zaug', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3', '2UU4AqkJRlQt4ZSRCZ0rhr', 'QZS672136723', 190, NOW()),
  ('FAKE LOVE', 'Yung Billion', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3', '64UbhRMl3DxkWK7V7R0jNN', 'QZNMX2186296', 205, NOW()),
  ('Cut Right', 'Zambrana', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3', '7CofB0RcyculeCUB1K3wXj', 'TCAFZ2118511', 215, NOW()),
  ('Other Guys', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3', '0sUAoYqgEQJNqsX0ZVTSHD', 'QZFZ62159515', 185, NOW()),
  ('In the Rain', 'Zaug', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3', '1vitSqezvkLVy9OW7LFDrA', 'QZK3A2100029', 230, NOW()),
  ('La Playa', 'Zambrana', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3', '2tbfqpBC2XDK0QW60Ku1zu', 'TCAFZ2118482', 200, NOW()),
  ('Alright', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-11.mp3', '3TMCAKUELp0H4wyAtPTqCa', 'QZNWR2044701', 220, NOW()),
  ('12/17', 'Zambrana', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-12.mp3', '1DzDrMyyDxsulUp9MMIBWx', 'TCAFZ2118455', 195, NOW()),
  ('Guidance', 'Zambrana', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-13.mp3', '7iHg4QNKnnc1vsHleW3M9L', 'TCAFZ2118448', 210, NOW()),
  ('Don\'t Go Away', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-14.mp3', '5V0Gg3BwDRIp1Z6CywkEQF', 'QZFYW2100331', 240, NOW()),
  ('Butterflies', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-15.mp3', '0fsoMxXMwqL1KgWJCzK1JL', 'QZK6P2189521', 185, NOW()),
  ('It\'s Not Right, But It\'s Okay', 'Goshfather', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-16.mp3', '4lVUw0wa4aM8K1mIOIpqKY', 'QZMEN2168439', 215, NOW()),
  ('Devil Woman', 'Manno', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-17.mp3', '6PurNOgpHjWyOJ5PbI9LBp', 'QZHN72120531', 195, NOW()),
  ('Heart On Fire', 'Phillip Berry', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-18.mp3', '4Pqry1FWyJRWBAcPnbbl9C', 'QZK3A2100030', 225, NOW()),
  ('U.S.O', 'Chris Kane', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-19.mp3', '5OVmv6BJbyBGlkiYaf50Mx', 'QZMEM2215751', 190, NOW()),
  ('LOOK AT ME', 'Harley Mac', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-20.mp3', '1q3zrtMTLx1Y2FmveSG6YJ', 'QZPLS2264768', 205, NOW())
ON CONFLICT (spotify_id) DO NOTHING;

-- Create a playlist for these tracks
INSERT INTO playlists (name, description, created_at)
VALUES ('Indie Artist Showcase', 'A collection of indie artists from Spotify submissions', NOW())
ON CONFLICT (name) DO NOTHING;

-- Get the playlist ID
DO $$
DECLARE
    playlist_id UUID;
BEGIN
    SELECT id INTO playlist_id FROM playlists WHERE name = 'Indie Artist Showcase' LIMIT 1;
    
    -- Add tracks to the playlist
    WITH song_ids AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as position
        FROM songs
        WHERE spotify_id IS NOT NULL
        LIMIT 20
    )
    INSERT INTO playlist_items (playlist_id, song_id, position)
    SELECT playlist_id, id, position
    FROM song_ids
    ON CONFLICT (playlist_id, song_id) DO NOTHING;
END $$;
