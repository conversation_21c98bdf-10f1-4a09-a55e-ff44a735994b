-- Create images table to store references to uploaded images
CREATE TABLE IF NOT EXISTS public.images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  url TEXT NOT NULL,
  category TEXT NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Set up Row Level Security
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Anyone can view images
CREATE POLICY "Anyone can view images" 
  ON public.images 
  FOR SELECT 
  USING (true);

-- Only admins can modify images
CREATE POLICY "Only admins can modify images" 
  ON public.images 
  FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_images_category ON public.images(category);
