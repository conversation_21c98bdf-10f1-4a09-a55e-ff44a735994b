-- Complete StayOnBeat Schema for Local Development
-- This includes all tables and enhancements for the URL processing workflow

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends <PERSON>pabase Auth)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  provider TEXT,
  provider_id TEXT,
  is_complete BOOLEAN DEFAULT FALSE,
  spotify_profile JSONB,
  youtube_profile JSONB,
  soundcloud_profile JSONB,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create enhanced submissions table with URL processing capabilities
CREATE TABLE IF NOT EXISTS public.submissions (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id),
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  submission_type TEXT NOT NULL CHECK (submission_type IN ('VIP', 'Skip', 'GA', 'Free')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected', 'played')),
  platform TEXT,
  platform_id TEXT,
  content_type TEXT CHECK (content_type IN ('track', 'playlist', 'album', 'artist')),
  processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  metadata_extracted BOOLEAN DEFAULT FALSE,
  url TEXT,
  media_url TEXT,
  stream_url TEXT,
  notes TEXT,
  position INTEGER,
  duration INTEGER,
  artwork_url TEXT,
  album_name TEXT,
  genre TEXT,
  is_explicit BOOLEAN DEFAULT FALSE,
  user_email TEXT,
  images JSONB,
  previous_submissions JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create playlist table
CREATE TABLE IF NOT EXISTS public.playlist (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  position INTEGER NOT NULL,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('VIP', 'Skip', 'GA', 'Free')),
  user_id UUID REFERENCES public.profiles(id),
  submission_id BIGINT REFERENCES public.submissions(id),
  platform TEXT,
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create tracks table for normalized track metadata
CREATE TABLE IF NOT EXISTS public.tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  album TEXT,
  duration INTEGER,
  artwork_url TEXT,
  audio_url TEXT,
  stream_url TEXT,
  platform TEXT NOT NULL,
  platform_id TEXT NOT NULL,
  genre TEXT,
  release_date DATE,
  is_explicit BOOLEAN DEFAULT FALSE,
  isrc TEXT,
  spotify_id TEXT,
  youtube_id TEXT,
  soundcloud_id TEXT,
  bandcamp_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(platform, platform_id)
);

-- Create artist_library table for artist management
CREATE TABLE IF NOT EXISTS public.artist_library (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  normalized_name TEXT NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  platforms JSONB,
  submission_count INTEGER DEFAULT 0,
  play_count INTEGER DEFAULT 0,
  last_submission TIMESTAMP WITH TIME ZONE,
  last_played TIMESTAMP WITH TIME ZONE,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(normalized_name)
);

-- Create played_tracks table
CREATE TABLE IF NOT EXISTS public.played_tracks (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  played_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  feedback TEXT,
  feedback_sent BOOLEAN DEFAULT FALSE,
  user_id UUID REFERENCES public.profiles(id),
  submission_id BIGINT REFERENCES public.submissions(id),
  platform TEXT,
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create artists table
CREATE TABLE IF NOT EXISTS public.artists (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  user_id UUID REFERENCES public.profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create user_oauth_tokens table
CREATE TABLE IF NOT EXISTS public.user_oauth_tokens (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  provider TEXT NOT NULL,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, provider)
);

-- Create settings table
CREATE TABLE IF NOT EXISTS public.settings (
  id TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_by UUID REFERENCES public.profiles(id)
);

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create a profile for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to normalize artist names
CREATE OR REPLACE FUNCTION public.normalize_artist_name(artist_name TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(trim(regexp_replace(artist_name, '[^a-zA-Z0-9\s]', '', 'g')));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to update artist library when submissions are created
CREATE OR REPLACE FUNCTION public.update_artist_library_on_submission()
RETURNS TRIGGER AS $$
DECLARE
    normalized_name TEXT;
BEGIN
    normalized_name := public.normalize_artist_name(NEW.artist_name);

    INSERT INTO public.artist_library (name, normalized_name, submission_count, last_submission)
    VALUES (NEW.artist_name, normalized_name, 1, NEW.created_at)
    ON CONFLICT (normalized_name)
    DO UPDATE SET
        submission_count = artist_library.submission_count + 1,
        last_submission = NEW.created_at,
        updated_at = now();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_submissions_updated_at ON public.submissions;
CREATE TRIGGER update_submissions_updated_at
    BEFORE UPDATE ON public.submissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_playlist_updated_at ON public.playlist;
CREATE TRIGGER update_playlist_updated_at
    BEFORE UPDATE ON public.playlist
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_tracks_updated_at ON public.tracks;
CREATE TRIGGER update_tracks_updated_at
    BEFORE UPDATE ON public.tracks
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_artist_library_updated_at ON public.artist_library;
CREATE TRIGGER update_artist_library_updated_at
    BEFORE UPDATE ON public.artist_library
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create trigger to automatically update artist library
DROP TRIGGER IF EXISTS update_artist_library_on_submission ON public.submissions;
CREATE TRIGGER update_artist_library_on_submission
    AFTER INSERT ON public.submissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_artist_library_on_submission();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_submissions_user_id ON public.submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_submissions_status ON public.submissions(status);
CREATE INDEX IF NOT EXISTS idx_submissions_platform ON public.submissions(platform);
CREATE INDEX IF NOT EXISTS idx_submissions_platform_id ON public.submissions(platform_id);
CREATE INDEX IF NOT EXISTS idx_submissions_processing_status ON public.submissions(processing_status);
CREATE INDEX IF NOT EXISTS idx_submissions_metadata_extracted ON public.submissions(metadata_extracted);

CREATE INDEX IF NOT EXISTS idx_playlist_position ON public.playlist(position);
CREATE INDEX IF NOT EXISTS idx_played_tracks_user_id ON public.played_tracks(user_id);
CREATE INDEX IF NOT EXISTS idx_played_tracks_played_at ON public.played_tracks(played_at);

CREATE INDEX IF NOT EXISTS idx_tracks_platform ON public.tracks(platform);
CREATE INDEX IF NOT EXISTS idx_tracks_platform_id ON public.tracks(platform_id);
CREATE INDEX IF NOT EXISTS idx_tracks_artist ON public.tracks(artist);
CREATE INDEX IF NOT EXISTS idx_tracks_title ON public.tracks(title);
CREATE INDEX IF NOT EXISTS idx_tracks_spotify_id ON public.tracks(spotify_id);
CREATE INDEX IF NOT EXISTS idx_tracks_youtube_id ON public.tracks(youtube_id);

CREATE INDEX IF NOT EXISTS idx_artist_library_normalized_name ON public.artist_library(normalized_name);
CREATE INDEX IF NOT EXISTS idx_artist_library_submission_count ON public.artist_library(submission_count);
CREATE INDEX IF NOT EXISTS idx_artist_library_play_count ON public.artist_library(play_count);

-- Set up Row Level Security (RLS)
-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.playlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.played_tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artists ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_oauth_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Profiles policies
CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

-- Submissions policies
CREATE POLICY "Users can view all submissions"
  ON public.submissions FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create their own submissions"
  ON public.submissions FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own submissions"
  ON public.submissions FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Allow system URL processing (for API endpoints)
CREATE POLICY "Allow system URL processing"
  ON public.submissions FOR INSERT
  USING (true);

CREATE POLICY "Allow system metadata updates"
  ON public.submissions FOR UPDATE
  USING (true);

-- Playlist policies
CREATE POLICY "Anyone can view playlist"
  ON public.playlist FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify playlist"
  ON public.playlist FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Tracks policies
CREATE POLICY "Anyone can view tracks"
  ON public.tracks FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify tracks"
  ON public.tracks FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Artist library policies
CREATE POLICY "Anyone can view artist library"
  ON public.artist_library FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify artist library"
  ON public.artist_library FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Played tracks policies
CREATE POLICY "Anyone can view played tracks"
  ON public.played_tracks FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify played tracks"
  ON public.played_tracks FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Artists policies
CREATE POLICY "Anyone can view artists"
  ON public.artists FOR SELECT
  USING (true);

CREATE POLICY "Users can update their own artist profile"
  ON public.artists FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- OAuth tokens policies
CREATE POLICY "Users can only view their own tokens"
  ON public.user_oauth_tokens FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can only modify their own tokens"
  ON public.user_oauth_tokens FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Settings policies
CREATE POLICY "Anyone can view settings"
  ON public.settings FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify settings"
  ON public.settings FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Insert initial settings
INSERT INTO public.settings (id, value)
VALUES
  ('player_settings', '{"show_logo": true, "auto_play": false, "volume": 80}'),
  ('queue_settings', '{"max_vip_slots": 5, "max_skip_slots": 10, "max_free_slots": 20}'),
  ('submission_settings', '{"vip_price": 10, "skip_price": 5, "ga_price": 2, "free_enabled": true}')
ON CONFLICT (id) DO NOTHING;
