-- Add submissions for 20 tracks from the Spotify library
INSERT INTO submissions (artist_name, song_title, submission_type, url, status, created_at)
VALUES
  ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON> Nota', 'GA', 'https://open.spotify.com/track/1hCuL9k57U5jAqZjAs9pLs', 'confirmed', NOW()),
  ('<PERSON><PERSON>', 'Payroll', 'GA', 'https://open.spotify.com/track/04Lf0tQ8rVSwsctTg5VQyq', 'confirmed', NOW()),
  ('P-Ca$h', 'Keep My Head Up', 'GA', 'https://open.spotify.com/track/79dik5E8GkK2Tmn2O4QHWg', 'confirmed', NOW()),
  ('<PERSON><PERSON>', '<PERSON>le Quererte', 'GA', 'https://open.spotify.com/track/5yvj2K6XRcoQDVKgPiZtCI', 'confirmed', NOW()),
  ('Zaug', 'Symmetry', 'GA', 'https://open.spotify.com/track/2UU4AqkJRlQt4ZSRCZ0rhr', 'confirmed', NOW()),
  ('Yung <PERSON>ion', '<PERSON>KE LOVE', 'GA', 'https://open.spotify.com/track/64UbhRMl3DxkWK7V7R0jNN', 'confirmed', NOW()),
  ('Zambrana', 'Cut Right', 'GA', 'https://open.spotify.com/track/7CofB0RcyculeCUB1K3wXj', 'confirmed', NOW()),
  ('Manno', 'Other Guys', 'GA', 'https://open.spotify.com/track/0sUAoYqgEQJNqsX0ZVTSHD', 'confirmed', NOW()),
  ('Zaug', 'In the Rain', 'GA', 'https://open.spotify.com/track/1vitSqezvkLVy9OW7LFDrA', 'confirmed', NOW()),
  ('Zambrana', 'La Playa', 'GA', 'https://open.spotify.com/track/2tbfqpBC2XDK0QW60Ku1zu', 'confirmed', NOW()),
  ('Manno', 'Alright', 'GA', 'https://open.spotify.com/track/3TMCAKUELp0H4wyAtPTqCa', 'confirmed', NOW()),
  ('Zambrana', '12/17', 'GA', 'https://open.spotify.com/track/1DzDrMyyDxsulUp9MMIBWx', 'confirmed', NOW()),
  ('Zambrana', 'Guidance', 'GA', 'https://open.spotify.com/track/7iHg4QNKnnc1vsHleW3M9L', 'confirmed', NOW()),
  ('Manno', 'Don\'t Go Away', 'GA', 'https://open.spotify.com/track/5V0Gg3BwDRIp1Z6CywkEQF', 'confirmed', NOW()),
  ('Manno', 'Butterflies', 'GA', 'https://open.spotify.com/track/0fsoMxXMwqL1KgWJCzK1JL', 'confirmed', NOW()),
  ('Goshfather', 'It\'s Not Right, But It\'s Okay', 'GA', 'https://open.spotify.com/track/4lVUw0wa4aM8K1mIOIpqKY', 'confirmed', NOW()),
  ('Manno', 'Devil Woman', 'GA', 'https://open.spotify.com/track/6PurNOgpHjWyOJ5PbI9LBp', 'confirmed', NOW()),
  ('Phillip Berry', 'Heart On Fire', 'GA', 'https://open.spotify.com/track/4Pqry1FWyJRWBAcPnbbl9C', 'confirmed', NOW()),
  ('Chris Kane', 'U.S.O', 'GA', 'https://open.spotify.com/track/5OVmv6BJbyBGlkiYaf50Mx', 'confirmed', NOW()),
  ('Harley Mac', 'LOOK AT ME', 'GA', 'https://open.spotify.com/track/1q3zrtMTLx1Y2FmveSG6YJ', 'confirmed', NOW());

-- Now add these to the playlist
WITH new_submissions AS (
  SELECT id, artist_name, song_title, url, submission_type
  FROM submissions
  ORDER BY created_at DESC
  LIMIT 20
),
max_position AS (
  SELECT COALESCE(MAX(position), 0) as max_pos FROM playlist
)
INSERT INTO playlist (position, artist_name, song_title, url, type)
SELECT 
  (SELECT max_pos FROM max_position) + ROW_NUMBER() OVER (ORDER BY id),
  artist_name,
  song_title,
  url,
  submission_type
FROM new_submissions;
