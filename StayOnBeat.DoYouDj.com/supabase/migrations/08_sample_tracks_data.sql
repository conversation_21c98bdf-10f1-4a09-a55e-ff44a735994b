-- Create a migration to populate the tracks table with sample data from Spotify library

-- Make sure we have the tracks table with all necessary fields
CREATE TABLE IF NOT EXISTS tracks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  audio_url TEXT NOT NULL,
  artwork_url TEXT,
  duration INTEGER,
  spotify_id TEXT,
  isrc TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert 20 sample tracks from the Spotify library
INSERT INTO tracks (title, artist, audio_url, artwork_url, spotify_id, isrc, duration)
VALUES
  ('Se Nota', 'Zambrana', 'https://open.spotify.com/track/1hCuL9k57U5jAqZjAs9pLs', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '1hCuL9k57U5jAqZjAs9pLs', 'TCAFZ2118532', 180),
  ('Payroll', 'Mann<PERSON>', 'https://open.spotify.com/track/04Lf0tQ8rVSwsctTg5VQyq', 'https://i.scdn.co/image/ab67616d0000b273b5e5b3e882e0454e406c70f1', '04Lf0tQ8rVSwsctTg5VQyq', 'QZMEQ2123203', 195),
  ('Keep My Head Up', 'P-Ca$h', 'https://open.spotify.com/track/79dik5E8GkK2Tmn2O4QHWg', 'https://i.scdn.co/image/ab67616d0000b2738c5b7e8d9e4a9a4300272314', '79dik5E8GkK2Tmn2O4QHWg', 'QZHN32106927', 210),
  ('Symmetry', 'Zaug', 'https://open.spotify.com/track/2UU4AqkJRlQt4ZSRCZ0rhr', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '2UU4AqkJRlQt4ZSRCZ0rhr', 'QZS672136723', 225),
  ('FAKE LOVE', 'Yung Billion', 'https://open.spotify.com/track/64UbhRMl3DxkWK7V7R0jNN', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '64UbhRMl3DxkWK7V7R0jNN', 'QZNMX2186296', 240),
  ('In the Rain', 'Zaug', 'https://open.spotify.com/track/2tbfqpBC2XDK0QW60Ku1zu', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '2tbfqpBC2XDK0QW60Ku1zu', 'QZK3A2100029', 255),
  ('Alright', 'Manno', 'https://open.spotify.com/track/3TMCAKUELp0H4wyAtPTqCa', 'https://i.scdn.co/image/ab67616d0000b273b5e5b3e882e0454e406c70f1', '3TMCAKUELp0H4wyAtPTqCa', 'QZNWR2044701', 270),
  ('Butterflies', 'Manno', 'https://open.spotify.com/track/0fsoMxXMwqL1KgWJCzK1JL', 'https://i.scdn.co/image/ab67616d0000b273b5e5b3e882e0454e406c70f1', '0fsoMxXMwqL1KgWJCzK1JL', 'QZK6P2189521', 285),
  ('Heart On Fire', 'Phillip Berry', 'https://open.spotify.com/track/4Pqry1FWyJRWBAcPnbbl9C', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '4Pqry1FWyJRWBAcPnbbl9C', 'QZK3A2100030', 300),
  ('LOOK AT ME', 'Harley Mac', 'https://open.spotify.com/track/1q3zrtMTLx1Y2FmveSG6YJ', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '1q3zrtMTLx1Y2FmveSG6YJ', 'QZPLS2264768', 315),
  ('Rapper\'s Tale', 'Grindhouse Trey', 'https://open.spotify.com/track/12a4nwiVpVjgdWbG9eoCqD', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '12a4nwiVpVjgdWbG9eoCqD', 'QZRP42291423', 330),
  ('Kingdom Moves On', 'Sirk', 'https://open.spotify.com/track/5N2ci1Z4Bxfnfdcbovczz6', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '5N2ci1Z4Bxfnfdcbovczz6', 'QZMEQ2246481', 345),
  ('Ocean', 'Ocean Blu', 'https://open.spotify.com/track/032elEe1dcIWZAsQjYX5ot', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '032elEe1dcIWZAsQjYX5ot', 'QZES52206982', 360),
  ('Paralyzed', 'Off Kadence', 'https://open.spotify.com/track/3nRPX109lhQzWa1NuqD5Ny', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '3nRPX109lhQzWa1NuqD5Ny', 'QZPLR2063895', 375),
  ('BALLIN', 'Yung Billion', 'https://open.spotify.com/track/2js4fTyZVphNpo9eieOYOI', 'https://i.scdn.co/image/ab67616d0000b273a1c37f3fd8e436f39d36a7a3', '2js4fTyZVphNpo9eieOYOI', 'QZL382293003', 390),
  ('ON MY WAY TO THE MOON', 'MONEY MOGLY', 'https://open.spotify.com/track/3OUzuWANEQb9j6YuRT3zsU', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '3OUzuWANEQb9j6YuRT3zsU', 'QZES72170293', 405),
  ('Mascara', 'Melen Kälē', 'https://open.spotify.com/track/2ZXsSSyvYBPx5HGXH66AWp', 'https://i.scdn.co/image/ab67616d0000b273b5e5b3e882e0454e406c70f1', '2ZXsSSyvYBPx5HGXH66AWp', 'QZMEV2109309', 420),
  ('Passion Fruit', 'Chris Kane', 'https://open.spotify.com/track/5NQKc24dPSwj1hTqr7NcwF', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '5NQKc24dPSwj1hTqr7NcwF', 'QZRP52260031', 435),
  ('Morning Air', 'Chris Kane', 'https://open.spotify.com/track/59GzH7zjHhQ5gz92YyRelE', 'https://i.scdn.co/image/ab67616d0000b273c5b7e8d9e4a9a4300272314', '59GzH7zjHhQ5gz92YyRelE', 'QZNWT2233108', 450),
  ('Block Hot', 'P-Ca$h', 'https://open.spotify.com/track/5Ttx73XLXRy8dJMcnJGdpA', 'https://i.scdn.co/image/ab67616d0000b2738c5b7e8d9e4a9a4300272314', '5Ttx73XLXRy8dJMcnJGdpA', 'QZES52244869', 465);

-- Create a playlist for these tracks
INSERT INTO playlists (name, description, created_at, updated_at)
VALUES ('Indie Artist Showcase', 'A collection of tracks from independent artists', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- Get the playlist ID
DO $$
DECLARE
  playlist_id UUID;
BEGIN
  SELECT id INTO playlist_id FROM playlists WHERE name = 'Indie Artist Showcase' LIMIT 1;
  
  -- Add tracks to the playlist
  INSERT INTO playlist_items (playlist_id, song_id, position)
  SELECT playlist_id, id, ROW_NUMBER() OVER () 
  FROM tracks 
  WHERE spotify_id IN (
    '1hCuL9k57U5jAqZjAs9pLs',
    '04Lf0tQ8rVSwsctTg5VQyq',
    '79dik5E8GkK2Tmn2O4QHWg',
    '2UU4AqkJRlQt4ZSRCZ0rhr',
    '64UbhRMl3DxkWK7V7R0jNN',
    '2tbfqpBC2XDK0QW60Ku1zu',
    '3TMCAKUELp0H4wyAtPTqCa',
    '0fsoMxXMwqL1KgWJCzK1JL',
    '4Pqry1FWyJRWBAcPnbbl9C',
    '1q3zrtMTLx1Y2FmveSG6YJ',
    '12a4nwiVpVjgdWbG9eoCqD',
    '5N2ci1Z4Bxfnfdcbovczz6',
    '032elEe1dcIWZAsQjYX5ot',
    '3nRPX109lhQzWa1NuqD5Ny',
    '2js4fTyZVphNpo9eieOYOI',
    '3OUzuWANEQb9j6YuRT3zsU',
    '2ZXsSSyvYBPx5HGXH66AWp',
    '5NQKc24dPSwj1hTqr7NcwF',
    '59GzH7zjHhQ5gz92YyRelE',
    '5Ttx73XLXRy8dJMcnJGdpA'
  )
  ON CONFLICT DO NOTHING;
END $$;
