-- Enhanced Submissions Schema for URL Processing Pipeline
-- Adds columns needed for the complete URL-to-playlist workflow

-- Add new columns to submissions table for enhanced metadata and processing
ALTER TABLE public.submissions 
ADD COLUMN IF NOT EXISTS platform_id TEXT,
ADD COLUMN IF NOT EXISTS content_type TEXT CHECK (content_type IN ('track', 'playlist', 'album', 'artist')),
ADD COLUMN IF NOT EXISTS processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS metadata_extracted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS duration INTEGER,
ADD COLUMN IF NOT EXISTS artwork_url TEXT,
ADD COLUMN IF NOT EXISTS album_name TEXT,
ADD COLUMN IF NOT EXISTS genre TEXT,
ADD COLUMN IF NOT EXISTS is_explicit BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS stream_url TEXT,
ADD COLUMN IF NOT EXISTS user_email TEXT;

-- Add indexes for better performance on new columns
CREATE INDEX IF NOT EXISTS idx_submissions_platform ON public.submissions(platform);
CREATE INDEX IF NOT EXISTS idx_submissions_platform_id ON public.submissions(platform_id);
CREATE INDEX IF NOT EXISTS idx_submissions_processing_status ON public.submissions(processing_status);
CREATE INDEX IF NOT EXISTS idx_submissions_metadata_extracted ON public.submissions(metadata_extracted);

-- Update RLS policies to allow system processing
-- Allow anonymous inserts for URL processing (will be enhanced with proper auth later)
CREATE POLICY "Allow system URL processing" 
  ON public.submissions FOR INSERT 
  USING (true);

-- Allow system updates for metadata processing
CREATE POLICY "Allow system metadata updates" 
  ON public.submissions FOR UPDATE 
  USING (true);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on submissions
DROP TRIGGER IF EXISTS update_submissions_updated_at ON public.submissions;
CREATE TRIGGER update_submissions_updated_at
    BEFORE UPDATE ON public.submissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Add similar trigger for playlist table
DROP TRIGGER IF EXISTS update_playlist_updated_at ON public.playlist;
CREATE TRIGGER update_playlist_updated_at
    BEFORE UPDATE ON public.playlist
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create tracks table for normalized track metadata (if not exists)
CREATE TABLE IF NOT EXISTS public.tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  album TEXT,
  duration INTEGER,
  artwork_url TEXT,
  audio_url TEXT,
  stream_url TEXT,
  platform TEXT NOT NULL,
  platform_id TEXT NOT NULL,
  genre TEXT,
  release_date DATE,
  is_explicit BOOLEAN DEFAULT FALSE,
  isrc TEXT,
  spotify_id TEXT,
  youtube_id TEXT,
  soundcloud_id TEXT,
  bandcamp_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(platform, platform_id)
);

-- Enable RLS on tracks table
ALTER TABLE public.tracks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for tracks
CREATE POLICY "Anyone can view tracks" 
  ON public.tracks FOR SELECT 
  USING (true);

CREATE POLICY "Only admins can modify tracks" 
  ON public.tracks FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Add indexes for tracks table
CREATE INDEX IF NOT EXISTS idx_tracks_platform ON public.tracks(platform);
CREATE INDEX IF NOT EXISTS idx_tracks_platform_id ON public.tracks(platform_id);
CREATE INDEX IF NOT EXISTS idx_tracks_artist ON public.tracks(artist);
CREATE INDEX IF NOT EXISTS idx_tracks_title ON public.tracks(title);
CREATE INDEX IF NOT EXISTS idx_tracks_spotify_id ON public.tracks(spotify_id);
CREATE INDEX IF NOT EXISTS idx_tracks_youtube_id ON public.tracks(youtube_id);

-- Create artist_library table for artist management (if not exists)
CREATE TABLE IF NOT EXISTS public.artist_library (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  normalized_name TEXT NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  platforms JSONB,
  submission_count INTEGER DEFAULT 0,
  play_count INTEGER DEFAULT 0,
  last_submission TIMESTAMP WITH TIME ZONE,
  last_played TIMESTAMP WITH TIME ZONE,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(normalized_name)
);

-- Enable RLS on artist_library table
ALTER TABLE public.artist_library ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for artist_library
CREATE POLICY "Anyone can view artist library" 
  ON public.artist_library FOR SELECT 
  USING (true);

CREATE POLICY "Only admins can modify artist library" 
  ON public.artist_library FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Add indexes for artist_library table
CREATE INDEX IF NOT EXISTS idx_artist_library_normalized_name ON public.artist_library(normalized_name);
CREATE INDEX IF NOT EXISTS idx_artist_library_submission_count ON public.artist_library(submission_count);
CREATE INDEX IF NOT EXISTS idx_artist_library_play_count ON public.artist_library(play_count);

-- Add triggers for updated_at on new tables
DROP TRIGGER IF EXISTS update_tracks_updated_at ON public.tracks;
CREATE TRIGGER update_tracks_updated_at
    BEFORE UPDATE ON public.tracks
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_artist_library_updated_at ON public.artist_library;
CREATE TRIGGER update_artist_library_updated_at
    BEFORE UPDATE ON public.artist_library
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to normalize artist names
CREATE OR REPLACE FUNCTION public.normalize_artist_name(artist_name TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(trim(regexp_replace(artist_name, '[^a-zA-Z0-9\s]', '', 'g')));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to update artist library when submissions are created
CREATE OR REPLACE FUNCTION public.update_artist_library_on_submission()
RETURNS TRIGGER AS $$
DECLARE
    normalized_name TEXT;
BEGIN
    normalized_name := public.normalize_artist_name(NEW.artist_name);
    
    INSERT INTO public.artist_library (name, normalized_name, submission_count, last_submission)
    VALUES (NEW.artist_name, normalized_name, 1, NEW.created_at)
    ON CONFLICT (normalized_name) 
    DO UPDATE SET 
        submission_count = artist_library.submission_count + 1,
        last_submission = NEW.created_at,
        updated_at = now();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update artist library
DROP TRIGGER IF EXISTS update_artist_library_on_submission ON public.submissions;
CREATE TRIGGER update_artist_library_on_submission
    AFTER INSERT ON public.submissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_artist_library_on_submission();
