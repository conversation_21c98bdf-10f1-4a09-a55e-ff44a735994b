-- Create tracks table
CREATE TABLE IF NOT EXISTS tracks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  audio_url TEXT NOT NULL,
  artwork_url TEXT,
  duration INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add sample tracks
INSERT INTO tracks (title, artist, audio_url, artwork_url, duration)
VALUES 
  ('Stay On Beat', 'DJ Rhythm', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', 'https://example.com/artwork1.jpg', 372),
  ('Midnight Groove', 'Smooth Operators', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3', 'https://example.com/artwork2.jpg', 289),
  ('Electric Dreams', 'Synth Wave', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3', 'https://example.com/artwork3.jpg', 318),
  ('Urban Pulse', 'City Beats', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3', 'https://example.com/artwork4.jpg', 245),
  ('Sunset Vibes', 'Chill Masters', 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3', 'https://example.com/artwork5.jpg', 302);

-- Enable RLS
ALTER TABLE tracks ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Tracks are viewable by everyone" 
  ON tracks FOR SELECT 
  USING (true);

-- Create policy for authenticated users to insert tracks
CREATE POLICY "Authenticated users can insert tracks" 
  ON tracks FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

-- Create policy for track owners to update their tracks
CREATE POLICY "Users can update their own tracks" 
  ON tracks FOR UPDATE 
  TO authenticated 
  USING (auth.uid() = created_by);

-- Add created_by column to track ownership
ALTER TABLE tracks ADD COLUMN created_by UUID REFERENCES auth.users(id);
