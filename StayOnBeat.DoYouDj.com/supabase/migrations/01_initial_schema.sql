-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends <PERSON>pabase Auth)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  provider TEXT,
  provider_id TEXT,
  is_complete BOOLEAN DEFAULT FALSE,
  spotify_profile JSONB,
  youtube_profile JSONB,
  soundcloud_profile JSONB,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create submissions table
CREATE TABLE IF NOT EXISTS public.submissions (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id),
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  submission_type TEXT NOT NULL CHECK (submission_type IN ('VIP', 'Skip', 'GA', 'Free')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected', 'played')),
  platform TEXT,
  url TEXT,
  notes TEXT,
  position INTEGER,
  media_url TEXT,
  images JSONB,
  previous_submissions JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create playlist table
CREATE TABLE IF NOT EXISTS public.playlist (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  position INTEGER NOT NULL,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('VIP', 'Skip', 'GA', 'Free')),
  user_id UUID REFERENCES public.profiles(id),
  submission_id BIGINT REFERENCES public.submissions(id),
  platform TEXT,
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create played_tracks table
CREATE TABLE IF NOT EXISTS public.played_tracks (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  played_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  feedback TEXT,
  feedback_sent BOOLEAN DEFAULT FALSE,
  user_id UUID REFERENCES public.profiles(id),
  submission_id BIGINT REFERENCES public.submissions(id),
  platform TEXT,
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create artists table
CREATE TABLE IF NOT EXISTS public.artists (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  user_id UUID REFERENCES public.profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create user_oauth_tokens table
CREATE TABLE IF NOT EXISTS public.user_oauth_tokens (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  provider TEXT NOT NULL,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, provider)
);

-- Create settings table
CREATE TABLE IF NOT EXISTS public.settings (
  id TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_by UUID REFERENCES public.profiles(id)
);

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    NEW.id, 
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create a profile for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Set up Row Level Security (RLS)
-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.playlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.played_tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artists ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_oauth_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Profiles policies
CREATE POLICY "Users can view their own profile" 
  ON public.profiles FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
  ON public.profiles FOR UPDATE 
  USING (auth.uid() = id);

-- Submissions policies
CREATE POLICY "Users can view all submissions" 
  ON public.submissions FOR SELECT 
  TO authenticated
  USING (true);

CREATE POLICY "Users can create their own submissions" 
  ON public.submissions FOR INSERT 
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own submissions" 
  ON public.submissions FOR UPDATE 
  TO authenticated
  USING (auth.uid() = user_id);

-- Playlist policies
CREATE POLICY "Anyone can view playlist" 
  ON public.playlist FOR SELECT 
  USING (true);

CREATE POLICY "Only admins can modify playlist" 
  ON public.playlist FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Played tracks policies
CREATE POLICY "Anyone can view played tracks" 
  ON public.played_tracks FOR SELECT 
  USING (true);

CREATE POLICY "Only admins can modify played tracks" 
  ON public.played_tracks FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Artists policies
CREATE POLICY "Anyone can view artists" 
  ON public.artists FOR SELECT 
  USING (true);

CREATE POLICY "Users can update their own artist profile" 
  ON public.artists FOR UPDATE 
  TO authenticated
  USING (auth.uid() = user_id);

-- OAuth tokens policies
CREATE POLICY "Users can only view their own tokens" 
  ON public.user_oauth_tokens FOR SELECT 
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can only modify their own tokens" 
  ON public.user_oauth_tokens FOR ALL 
  TO authenticated
  USING (auth.uid() = user_id);

-- Settings policies
CREATE POLICY "Anyone can view settings" 
  ON public.settings FOR SELECT 
  USING (true);

CREATE POLICY "Only admins can modify settings" 
  ON public.settings FOR ALL 
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND is_admin = true
  ));

-- Insert initial settings
INSERT INTO public.settings (id, value)
VALUES 
  ('player_settings', '{"show_logo": true, "auto_play": false, "volume": 80}'),
  ('queue_settings', '{"max_vip_slots": 5, "max_skip_slots": 10, "max_free_slots": 20}'),
  ('submission_settings', '{"vip_price": 10, "skip_price": 5, "ga_price": 2, "free_enabled": true}')
ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_submissions_user_id ON public.submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_submissions_status ON public.submissions(status);
CREATE INDEX IF NOT EXISTS idx_playlist_position ON public.playlist(position);
CREATE INDEX IF NOT EXISTS idx_played_tracks_user_id ON public.played_tracks(user_id);
CREATE INDEX IF NOT EXISTS idx_played_tracks_played_at ON public.played_tracks(played_at);
