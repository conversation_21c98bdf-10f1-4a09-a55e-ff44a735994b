-- Create tables for the Stay On Beat application

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- Create users table (extends Supabase auth.users)
create table if not exists public.users (
  id uuid references auth.users on delete cascade not null primary key,
  username text unique,
  full_name text,
  avatar_url text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create submissions table
create table if not exists public.submissions (
  id uuid default uuid_generate_v4() primary key,
  artist_name text not null,
  song_title text not null,
  platform text not null,
  url text not null,
  submission_type text not null,
  status text default 'pending',
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create playlist table
create table if not exists public.playlist (
  id uuid default uuid_generate_v4() primary key,
  position integer not null,
  artist_name text not null,
  song_title text not null,
  type text not null,
  platform text not null,
  url text not null,
  submission_time timestamp with time zone default timezone('utc'::text, now()) not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create played_tracks table
create table if not exists public.played_tracks (
  id uuid default uuid_generate_v4() primary key,
  artist_name text not null,
  song_title text not null,
  played_at timestamp with time zone default timezone('utc'::text, now()) not null,
  feedback text,
  feedback_sent boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create player_status table
create table if not exists public.player_status (
  id integer primary key default 1,
  is_playing boolean default false,
  current_track_id uuid references public.playlist(id),
  current_time integer default 0,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create RLS policies
alter table public.users enable row level security;
alter table public.submissions enable row level security;
alter table public.playlist enable row level security;
alter table public.played_tracks enable row level security;
alter table public.player_status enable row level security;

-- Create policies
create policy "Users can view their own data" on public.users
  for select using (auth.uid() = id);

create policy "Anyone can view submissions" on public.submissions
  for select using (true);

create policy "Admins can manage submissions" on public.submissions
  for all using (auth.uid() in (select id from public.users where username = 'admin'));

create policy "Anyone can view playlist" on public.playlist
  for select using (true);

create policy "Admins can manage playlist" on public.playlist
  for all using (auth.uid() in (select id from public.users where username = 'admin'));

create policy "Anyone can view played tracks" on public.played_tracks
  for select using (true);

create policy "Admins can manage played tracks" on public.played_tracks
  for all using (auth.uid() in (select id from public.users where username = 'admin'));

create policy "Anyone can view player status" on public.player_status
  for select using (true);

create policy "Admins can manage player status" on public.player_status
  for all using (auth.uid() in (select id from public.users where username = 'admin'));

-- Create functions
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.users (id, username, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'username', new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$$ language plpgsql security definer;

-- Create trigger for new user
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user(); 