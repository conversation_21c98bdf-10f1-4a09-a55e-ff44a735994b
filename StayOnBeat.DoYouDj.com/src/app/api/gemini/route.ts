import { NextResponse } from 'next/server';
import { geminiService } from '@/lib/services/geminiService';

export async function POST(request: Request) {
  try {
    const { prompt } = await request.json();

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    const response = await geminiService.generateContent(prompt);
    return NextResponse.json({ response });
  } catch (error) {
    console.error('Gemini API Route Error:', error);
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    );
  }
} 