'use client';

import { useState } from 'react';

// Base Track Card Interface
interface BaseTrackCard {
  id: number | string
  position?: number
  artistName: string
  songTitle: string
  type: string
  platform?: string
  url?: string
  isPlaying?: boolean
}

// Queue Card (Admin View)
interface QueueCardItem extends BaseTrackCard {
  artistId?: string
  submissionTime?: string
  isDraggable?: boolean
  isHighlighted?: boolean
  isExpanded?: boolean
  isUnlockMode?: boolean
}

// Playlist Card (Public View)
interface PlaylistCard extends BaseTrackCard {
  artwork_url?: string
  duration?: number
  albumName?: string
  mediaUrl?: string
}

// Track Card (Music Player)
interface TrackCard extends BaseTrackCard {
  artwork_url?: string
  duration?: number
  albumName?: string
  mediaUrl?: string
  isCurrentTrack?: boolean
}

export default function Home() {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', marginBottom: '1rem' }}>
        Button Demo
      </h1>
      
      <div style={{ display: 'flex', gap: '1rem', flexDirection: 'column' }}>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button 
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#0070f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Primary Button
          </button>
          
          <button 
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f3f3f3',
              color: '#333',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Secondary Button
          </button>
        </div>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button 
            onClick={handleClick}
            disabled={isLoading}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#0070f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              opacity: isLoading ? 0.7 : 1
            }}
          >
            {isLoading ? 'Loading...' : 'Click Me'}
          </button>
        </div>
      </div>
    </div>
  );
} 