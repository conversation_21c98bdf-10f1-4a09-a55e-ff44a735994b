interface CacheEntry<T> {
  value: T;
  expiry: number;
}

export class CachedDataService {
  private static instance: CachedDataService;
  private cache: Map<string, CacheEntry<any>>;

  private constructor() {
    this.cache = new Map();
  }

  static getInstance(): CachedDataService {
    if (!CachedDataService.instance) {
      CachedDataService.instance = new CachedDataService();
    }
    return CachedDataService.instance;
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (entry.expiry < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return entry.value as T;
  }

  set<T>(key: string, value: T, ttlMs: number = 3600000): void {
    const expiry = Date.now() + ttlMs;
    this.cache.set(key, { value, expiry });
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }
}

export const cachedDataService = CachedDataService.getInstance(); 