import { kvService } from './kvService';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

export class GeminiService {
  private static instance: GeminiService;
  private readonly API_KEY: string;
  private readonly API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  private readonly CACHE_PREFIX = 'gemini:';
  private readonly CACHE_TTL = 60 * 60; // 1 hour

  private constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }
    this.API_KEY = apiKey;
  }

  static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  async generateContent(prompt: string): Promise<string> {
    const cacheKey = `${this.CACHE_PREFIX}${prompt}`;
    
    // Try to get from cache first
    const cachedResponse = await kvService.get<string>(cacheKey);
    if (cachedResponse) {
      return cachedResponse;
    }

    try {
      const response = await fetch(`${this.API_URL}?key=${this.API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
        throw new Error('Invalid response format from Gemini API');
      }

      const result = data.candidates[0].content.parts[0].text;
      
      // Cache the response
      await kvService.set(cacheKey, result, this.CACHE_TTL);
      
      return result;
    } catch (error) {
      console.error('Gemini API Error:', error);
      throw error;
    }
  }
}

export const geminiService = GeminiService.getInstance(); 