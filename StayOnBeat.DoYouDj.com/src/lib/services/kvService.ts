import { kv } from '@vercel/kv';

export class KVService {
  private static instance: KVService;
  private readonly DEFAULT_TTL = 60 * 60; // 1 hour default TTL

  private constructor() {}

  static getInstance(): KVService {
    if (!KVService.instance) {
      KVService.instance = new KVService();
    }
    return KVService.instance;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      return await kv.get<T>(key);
    } catch (error) {
      console.error('KV Get Error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl: number = this.DEFAULT_TTL): Promise<void> {
    try {
      await kv.set(key, value, { ex: ttl });
    } catch (error) {
      console.error('KV Set Error:', error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await kv.del(key);
    } catch (error) {
      console.error('KV Delete Error:', error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      return await kv.exists(key) === 1;
    } catch (error) {
      console.error('KV Exists Error:', error);
      return false;
    }
  }
}

export const kvService = KVService.getInstance();
