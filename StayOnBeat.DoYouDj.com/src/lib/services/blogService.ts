  if (data) {
    // Store in KV cache
    await kvService.set(cacheKey, data, this.CACHE_TTL);
  }

  return data || [];
}

private async invalidateCaches(): Promise<void> {
  // Delete all blog-related caches
  const keys = await kvService.get(`${this.KV_PREFIX}*`);
  if (keys) {
    await Promise.all(keys.map(key => kvService.delete(key)));
  }
}

private async invalidateListCaches(): Promise<void> {
  // Delete only list-related caches
  const keys = await kvService.get(`${this.KV_PREFIX}posts:*`);
  if (keys) {
    await Promise.all(keys.map(key => kvService.delete(key)));
  }
} 