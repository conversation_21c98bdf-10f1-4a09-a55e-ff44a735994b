# Webhook Service

A simple webhook service built with Next.js and Supabase for handling incoming webhook events.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env.local` file with the following variables:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret
```

3. Create the following table in your Supabase database:
```sql
create table webhook_events (
  id uuid default uuid_generate_v4() primary key,
  event_type text not null,
  payload jsonb not null,
  received_at timestamp with time zone not null,
  signature text not null,
  created_at timestamp with time zone default now()
);
```

## Usage

The webhook endpoint is available at `/api/webhook`. Send POST requests with the following structure:

```json
{
  "event": "event.name",
  "data": {
    // Your event data here
  },
  "timestamp": "2024-03-14T12:00:00Z"
}
```

Include the `x-webhook-signature` header for authentication.

## Development

Run the development server:
```bash
npm run dev
```

## Production

Build and start the production server:
```bash
npm run build
npm start
```