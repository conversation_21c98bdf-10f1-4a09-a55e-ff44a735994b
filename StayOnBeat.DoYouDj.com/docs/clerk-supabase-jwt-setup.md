# Setting Up Clerk JWT Template for Supa<PERSON>

To integrate <PERSON> with Supa<PERSON>, you need to create a JWT template in <PERSON> that generates tokens compatible with Supabase's authentication system.

## Step 1: Create a JWT Template in Clerk Dashboard

1. Go to the [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Navigate to your application
3. Go to "JWT Templates" in the sidebar
4. Create a new template named "supabase"
5. Paste the following JSON into the template editor:

```json
{
  "sub": "{{user.id}}",
  "aud": "authenticated",
  "role": "authenticated",
  "exp": "{{jwt.exp}}",
  "iat": "{{jwt.iat}}",
  
  "user_id": "{{user.id}}",
  "email": "{{user.primary_email_address}}",
  "phone": "{{user.primary_phone_number}}",
  "full_name": "{{user.first_name}} {{user.last_name}}",
  "avatar_url": "{{user.image_url}}",
  
  "app_metadata": {
    "provider": "clerk"
  },
  
  "user_metadata": {
    "first_name": "{{user.first_name}}",
    "last_name": "{{user.last_name}}",
    "email": "{{user.primary_email_address}}",
    "phone": "{{user.primary_phone_number}}",
    "image_url": "{{user.image_url}}",
    "created_at": "{{user.created_at}}",
    "updated_at": "{{user.updated_at}}"
  }
}
```

6. Save the template

## Step 2: Configure Supabase to Accept Clerk JWTs

1. Go to the [Supabase Dashboard](https://app.supabase.io/)
2. Navigate to your project
3. Go to "Authentication" > "Settings" > "JWT Settings"
4. Set the JWT Secret to the same value as your Clerk JWT Secret
   - You can find your Clerk JWT Secret in the Clerk Dashboard under "API Keys"
5. Save the settings

## Step 3: Use the JWT in Your Code

When making requests to Supabase, include the JWT in the Authorization header:

```typescript
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'

export async function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${await auth().getToken({ template: 'supabase' })}`
        }
      }
    }
  )
}
```

## Example JWT Payload

Here's an example of what the JWT payload will look like after the template is applied:

```json
{
  "sub": "user_1deJLArSTiWiF1YdsEWysnhJLLY",
  "aud": "authenticated",
  "role": "authenticated",
  "exp": **********,
  "iat": **********,
  
  "user_id": "user_1deJLArSTiWiF1YdsEWysnhJLLY",
  "email": "<EMAIL>",
  "phone": null,
  "full_name": "Maria Doe",
  "avatar_url": "https://example.com/avatar.jpg",
  
  "app_metadata": {
    "provider": "clerk"
  },
  
  "user_metadata": {
    "first_name": "Maria",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": null,
    "image_url": "https://example.com/avatar.jpg",
    "created_at": **********,
    "updated_at": **********
  },
  
  // Default claims included automatically by Clerk
  "iss": "https://clean-mayfly-62.clerk.accounts.dev",
  "nbf": **********,
  "sid": "sess_2ehYpzsasKNOZrpqPZ9yDWhrYVe",
  "azp": "http://localhost:3000"
}
```

This JWT will be recognized by Supabase as a valid authentication token, allowing you to access protected resources.
