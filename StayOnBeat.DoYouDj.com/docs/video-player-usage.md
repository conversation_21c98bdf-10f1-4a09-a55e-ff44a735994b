# VideoJS HLS Player Documentation

This document explains how to use the VideoJS-based HLS player implementation in the STAYONBEAT application.

## Overview

The player implementation uses VideoJS to handle HLS (.m3u8) playlists for streaming audio and video content. It provides a complete solution for:

- Playing HLS streams
- Managing playlists
- Custom controls
- Responsive design
- Track metadata display

## Components

### 1. VideoPlayer

The base component that wraps VideoJS functionality.

\`\`\`tsx
import { VideoPlayer } from '@/components/video-player/video-player'

// Usage
<VideoPlayer 
  options={videoJsOptions} 
  onReady={handlePlayerReady}
  onTimeUpdate={handleTimeUpdate}
  onEnded={handleTrackEnded}
/>
\`\`\`

### 2. HLSPlayer

A higher-level component that handles HLS playlists and provides additional functionality.

\`\`\`tsx
import { HLSPlayer } from '@/components/video-player/hls-player'

// Usage
<HLSPlayer
  playlist={playlist}
  currentIndex={currentIndex}
  showControls={true}
  autoplay={false}
  muted={false}
  onTimeUpdate={handleTimeUpdate}
  onTrackChange={handleTrackChange}
  showLogo={true}
  logoUrl="/images/stayonbeat.png"
/>
\`\`\`

### 3. useHLSPlayer Hook

A custom hook that provides state management and controls for the HLS player.

\`\`\`tsx
import { useHLSPlayer } from '@/components/video-player/use-hls-player'

// Usage
const {
  playlist,
  currentIndex,
  isPlaying,
  currentTime,
  duration,
  volume,
  formatTime,
  togglePlay,
  playNext,
  playPrevious,
  seekTo,
  updateVolume,
  addToPlaylist,
  removeFromPlaylist,
  reorderPlaylist,
  clearPlaylist,
  currentTrack
} = useHLSPlayer(initialPlaylist)
\`\`\`

## Creating .m3u8 Playlists

The system includes utilities for generating and parsing .m3u8 playlists:

\`\`\`tsx
import { 
  generateM3U8, 
  downloadM3U8, 
  parseM3U8 
} from '@/components/video-player/playlist-generator'

// Generate playlist content
const m3u8Content = generateM3U8(tracks, 'My Playlist')

// Download playlist file
downloadM3U8(tracks, 'my-playlist.m3u8', 'My Playlist')

// Parse playlist content
const parsedTracks = parseM3U8(m3u8Content)
\`\`\`

## Integration with Supabase

To use the player with Supabase data:

1. Ensure your tracks have a `mediaUrl` field that points to an HLS stream URL
2. Convert your database tracks to the `PlaylistItem` format
3. Use the player components with the converted data

Example:

\`\`\`tsx
// Fetch tracks from Supabase
const { data: tracks } = await supabase
  .from('playlist')
  .select('*')
  .order('position', { ascending: true })

// Convert to PlaylistItem format
const playlistItems = tracks.map(track => ({
  id: track.id,
  position: track.position,
  artistName: track.artist_name,
  songTitle: track.song_title,
  type: track.type,
  mediaUrl: track.url, // This should be an HLS stream URL
  platform: track.platform
}))

// Use with the player
<HLSPlayer playlist={playlistItems} />
\`\`\`

## Example Implementation

Here's a complete example of implementing the player in a component:

\`\`\`tsx
'use client'

import { useState, useEffect } from 'react'
import { HLSPlayer } from '@/components/video-player/hls-player'
import { useHLSPlayer } from '@/components/video-player/use-hls-player'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Play, Pause, SkipForward, SkipBack, Volume2 } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import type { PlaylistItem } from '@/lib/types'

export default function MusicPlayer() {
  const [isLoading, setIsLoading] = useState(true)
  
  // Initialize player with empty playlist
  const {
    playlist,
    currentIndex,
    isPlaying,
    currentTime,
    duration,
    volume,
    formatTime,
    handleTimeUpdate,
    handleTrackChange,
    togglePlay,
    playNext,
    playPrevious,
    seekTo,
    updateVolume,
    currentTrack
  } = useHLSPlayer([])
  
  // Fetch playlist from Supabase
  useEffect(() => {
    async function fetchPlaylist() {
      try {
        const { data, error } = await supabase
          .from('playlist')
          .select('*')
          .order('position', { ascending: true })
          
        if (error) throw error
        
        // Convert to PlaylistItem format
        const playlistItems: PlaylistItem[] = data.map(track => ({
          id: track.id,
          position: track.position,
          artistName: track.artist_name,
          songTitle: track.song_title,
          type: track.type,
          mediaUrl: track.url, // This should be an HLS stream URL
          platform: track.platform
        }))
        
        // Update player playlist
        setPlaylist(playlistItems)
      } catch (error) {
        console.error('Error fetching playlist:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPlaylist()
  }, [])
  
  if (isLoading) {
    return <div>Loading player...</div>
  }
  
  return (
    <div className="player-container">
      <div className="video-container">
        <HLSPlayer
          playlist={playlist}
          currentIndex={currentIndex}
          showControls={false} // We'll use our custom controls
          onTimeUpdate={handleTimeUpdate}
          onTrackChange={handleTrackChange}
          showLogo={true}
        />
      </div>
      
      <div className="custom-controls">
        <div className="track-info">
          <h3>{currentTrack?.songTitle || 'No track selected'}</h3>
          <p>{currentTrack?.artistName || ''}</p>
        </div>
        
        <div className="progress-bar">
          <span>{formatTime(currentTime)}</span>
          <Slider
            value={[currentTime]}
            max={duration}
            step={1}
            onValueChange={(value) => seekTo(value[0])}
          />
          <span>{formatTime(duration)}</span>
        </div>
        
        <div className="control-buttons">
          <Button variant="ghost" size="icon" onClick={playPrevious}>
            <SkipBack />
          </Button>
          
          <Button variant="default" size="icon" onClick={togglePlay}>
            {isPlaying ? <Pause /> : <Play />}
          </Button>
          
          <Button variant="ghost" size="icon" onClick={playNext}>
            <SkipForward />
          </Button>
          
          <div className="volume-control">
            <Volume2 />
            <Slider
              value={[volume]}
              max={100}
              step={1}
              onValueChange={(value) => updateVolume(value[0])}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
\`\`\`

## HLS Stream Requirements

For the player to work correctly:

1. The `mediaUrl` should point to a valid HLS stream (.m3u8 file)
2. The server hosting the HLS stream must have proper CORS headers
3. For audio-only streams, use HLS audio playlists
4. For video streams, ensure proper encoding for web playback

## Troubleshooting

Common issues:

- **CORS errors**: Ensure the server hosting the HLS streams has proper CORS headers
- **Playback errors**: Check that the HLS stream is properly formatted and accessible
- **Mobile playback issues**: Some mobile browsers have restrictions on autoplay
\`\`\`

Let's create an example implementation for the admin page:
