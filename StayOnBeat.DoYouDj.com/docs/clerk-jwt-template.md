# Clerk JWT Template for Supabase Integration

To integrate <PERSON> with Supabase, you need to create a JWT template in the Clerk dashboard. This template will generate JWTs that Supabase can verify.

## JWT Template for Supabase

```json
{
  "sub": "{{user.id}}",
  "aud": "authenticated",
  "role": "authenticated",
  "exp": "{{jwt.exp}}",
  "iat": "{{jwt.iat}}",
  
  "user_id": "{{user.id}}",
  "email": "{{user.primary_email_address}}",
  "phone": "{{user.primary_phone_number}}",
  "full_name": "{{user.first_name}} {{user.last_name}}",
  "avatar_url": "{{user.image_url}}",
  
  "app_metadata": {
    "provider": "clerk"
  },
  
  "user_metadata": {
    "first_name": "{{user.first_name}}",
    "last_name": "{{user.last_name}}",
    "email": "{{user.primary_email_address}}",
    "phone": "{{user.primary_phone_number}}",
    "image_url": "{{user.image_url}}",
    "created_at": "{{user.created_at}}",
    "updated_at": "{{user.updated_at}}",
    "public_metadata": "{{user.public_metadata}}",
    "unsafe_metadata": "{{user.unsafe_metadata}}"
  }
}
```

## How to Set Up the JWT Template

1. Go to the [Clerk Dashboard](https://dashboard.clerk.dev/)
2. Navigate to your application
3. Go to "JWT Templates" in the sidebar
4. Create a new template named "supabase"
5. Paste the above JSON into the template editor
6. Save the template

## Using the JWT Template in Your Code

When making requests to Supabase, you need to include the JWT in the Authorization header:

```typescript
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'

export async function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${await auth().getToken({ template: 'supabase' })}`
        }
      }
    }
  )
}
```

This will ensure that Supabase receives a properly formatted JWT that it can verify and use to authenticate the user.
