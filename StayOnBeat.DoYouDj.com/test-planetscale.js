#!/usr/bin/env node

/**
 * PlanetScale Integration Test for StayOnBeat
 * Tests the complete database setup and migration
 */

console.log('🚀 PlanetScale Integration Test for StayOnBeat')
console.log('==============================================')

// Test environment variables
console.log('\n📋 Environment Check:')
console.log('DATABASE_URL:', process.env.DATABASE_URL ? '✅ Set' : '❌ Not set')
console.log('PLANETSCALE_DATABASE_URL:', process.env.PLANETSCALE_DATABASE_URL ? '✅ Set' : '❌ Not set')
console.log('NEXT_PUBLIC_USE_LOCAL_DB:', process.env.NEXT_PUBLIC_USE_LOCAL_DB || 'not set')

// Test if PlanetScale packages are installed
console.log('\n📦 Package Check:')
try {
  require('@planetscale/database')
  console.log('@planetscale/database: ✅ Installed')
} catch (error) {
  console.log('@planetscale/database: ❌ Not installed')
  console.log('Run: npm install @planetscale/database mysql2')
  process.exit(1)
}

try {
  require('mysql2')
  console.log('mysql2: ✅ Installed')
} catch (error) {
  console.log('mysql2: ❌ Not installed')
  console.log('Run: npm install @planetscale/database mysql2')
  process.exit(1)
}

// Test database client selection
console.log('\n🔍 Database Client Selection:')
try {
  // Import the URL processor to test database selection
  const { processUrl } = require('./lib/url-processor.js')
  console.log('URL processor: ✅ Loaded')
  
  // Test which database client would be selected
  if (process.env.DATABASE_URL || process.env.PLANETSCALE_DATABASE_URL) {
    console.log('Selected database: 🚀 PlanetScale')
  } else if (process.env.NEXT_PUBLIC_USE_LOCAL_DB === 'true') {
    console.log('Selected database: 📁 Local Database')
  } else {
    console.log('Selected database: ☁️ Supabase (fallback)')
  }
} catch (error) {
  console.log('URL processor: ❌ Error loading:', error.message)
}

// Test PlanetScale connection (if configured)
async function testPlanetScaleConnection() {
  if (!process.env.DATABASE_URL && !process.env.PLANETSCALE_DATABASE_URL) {
    console.log('\n⏭️ Skipping PlanetScale connection test (no DATABASE_URL set)')
    console.log('To test PlanetScale:')
    console.log('1. Set DATABASE_URL in .env.local')
    console.log('2. Run this test again')
    return
  }

  console.log('\n🔍 Testing PlanetScale Connection:')
  try {
    const { testPlanetScaleConnection } = require('./lib/planetscale.js')
    const connected = await testPlanetScaleConnection()
    
    if (connected) {
      console.log('PlanetScale connection: ✅ Success')
    } else {
      console.log('PlanetScale connection: ❌ Failed')
    }
  } catch (error) {
    console.log('PlanetScale connection: ❌ Error:', error.message)
    console.log('Make sure your DATABASE_URL is correct in .env.local')
  }
}

// Test URL processing with current database
async function testUrlProcessing() {
  console.log('\n🎵 Testing URL Processing:')
  try {
    const { processUrl } = require('./lib/url-processor.js')
    
    const testUrl = 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB'
    console.log(`Processing: ${testUrl}`)
    
    const result = await processUrl({
      url: testUrl,
      submissionType: 'Free',
      userId: 'test-user',
      userEmail: '<EMAIL>'
    })
    
    if (result.success) {
      console.log('URL processing: ✅ Success')
      console.log(`Submission ID: ${result.submissionId}`)
      console.log(`Platform: ${result.platformDetection?.platform}`)
      console.log(`Track: ${result.trackCard?.title} by ${result.trackCard?.artist}`)
    } else {
      console.log('URL processing: ❌ Failed')
      console.log(`Error: ${result.error}`)
    }
  } catch (error) {
    console.log('URL processing: ❌ Error:', error.message)
  }
}

// Main test function
async function runTests() {
  await testPlanetScaleConnection()
  await testUrlProcessing()
  
  console.log('\n🎉 Test Summary:')
  console.log('================')
  console.log('✅ Dependencies installed')
  console.log('✅ Database client selection working')
  
  if (process.env.DATABASE_URL || process.env.PLANETSCALE_DATABASE_URL) {
    console.log('🚀 PlanetScale configuration detected')
    console.log('📋 Next steps:')
    console.log('   1. Create database schema in PlanetScale')
    console.log('   2. Run: node scripts/migrate-data.js (if you have local data)')
    console.log('   3. Test your app: http://localhost:3003')
  } else {
    console.log('📁 Using local database (no PlanetScale configured)')
    console.log('📋 To set up PlanetScale:')
    console.log('   1. Create account at https://planetscale.com')
    console.log('   2. Create database and get connection string')
    console.log('   3. Add DATABASE_URL to .env.local')
    console.log('   4. Run this test again')
  }
  
  console.log('\n📖 For detailed setup instructions, see:')
  console.log('   - PLANETSCALE_SETUP.md')
  console.log('   - STAYONBEAT_WORKFLOW_DOCUMENTATION.md')
}

// Run the tests
runTests().catch(console.error)
