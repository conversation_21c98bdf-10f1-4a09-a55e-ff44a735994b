#!/bin/bash

# Docker Installation Script for Debian 12 (bookworm)
# Run this script with: bash install-docker.sh

echo "🐳 Installing Docker on Debian 12"
echo "=================================="

# Update package index
echo "📦 Updating package index..."
sudo apt update

# Install prerequisites
echo "🔧 Installing prerequisites..."
sudo apt install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker's official GPG key
echo "🔑 Adding Docker GPG key..."
sudo mkdir -m 0755 -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Add Docker repository
echo "📋 Adding Docker repository..."
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package index again
echo "📦 Updating package index with Docker repository..."
sudo apt update

# Install Docker Engine
echo "🐳 Installing Docker Engine..."
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Add current user to docker group (to run docker without sudo)
echo "👤 Adding user to docker group..."
sudo usermod -aG docker $USER

# Start and enable Docker service
echo "🚀 Starting Docker service..."
sudo systemctl start docker
sudo systemctl enable docker

# Test Docker installation
echo "🧪 Testing Docker installation..."
sudo docker run hello-world

echo ""
echo "✅ Docker installation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Log out and log back in (or run: newgrp docker)"
echo "2. Test Docker without sudo: docker run hello-world"
echo "3. Run the StayOnBeat local setup: ./scripts/start-local.sh"
echo ""
echo "🔧 Docker version:"
sudo docker --version
echo ""
echo "🐳 Docker Compose version:"
sudo docker compose version
