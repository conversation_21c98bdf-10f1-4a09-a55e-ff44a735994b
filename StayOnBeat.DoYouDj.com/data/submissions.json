[{"url": "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB", "media_url": "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB", "submission_type": "Free", "platform": "spotify", "platform_id": "2FNvO68ODWQD4XOmm6gzEB", "content_type": "track", "status": "confirmed", "processing_status": "completed", "metadata_extracted": true, "user_id": null, "user_email": null, "notes": null, "artist_name": "Spotify Artist", "song_title": "Spotify Track", "created_at": "2025-05-26T20:49:51.250Z", "updated_at": "2025-05-26T20:49:51.253Z", "id": 1748292591250, "duration": 200, "artwork_url": "https://placehold.co/400x400/purple/white?text=StayOnBeat", "genre": "Pop", "is_explicit": false}, {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "media_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "submission_type": "VIP", "platform": "youtube", "platform_id": "dQw4w9WgXcQ", "content_type": "track", "status": "confirmed", "processing_status": "completed", "metadata_extracted": true, "user_id": null, "user_email": null, "notes": null, "artist_name": "YouTube Artist", "song_title": "YouTube Track", "created_at": "2025-05-26T20:50:30.244Z", "updated_at": "2025-05-26T20:50:30.304Z", "id": 1748292630244, "duration": 180, "artwork_url": "https://placehold.co/400x400/purple/white?text=StayOnBeat", "genre": "Unknown", "is_explicit": false}]