# Neon Database Setup for StayOnBeat (FREE)

## 🚀 **Quick Setup (5 minutes) - $0/month**

### Step 1: Create Neon Account
1. Go to [neon.tech](https://neon.tech)
2. Click "Sign up" 
3. Use GitHub or email (FREE - no credit card needed!)

### Step 2: Create Database
1. Click "Create Project"
2. **Project name**: `stayonbeat`
3. **Database name**: `stayonbeat_db`
4. **Region**: Choose closest to you
5. Click "Create Project"

### Step 3: Get Connection String
1. In your project dashboard, click "Connection Details"
2. Copy the **PostgreSQL connection string**:
   ```
   ************************************************************
   ```

### Step 4: Configure StayOnBeat
1. Update your `.env.local`:
   ```env
   # Neon Database (FREE)
   DATABASE_URL="postgresql://your-neon-connection-string"
   NEXT_PUBLIC_USE_LOCAL_DB=false
   
   # Keep Supabase as fallback
   NEXT_PUBLIC_SUPABASE_URL=https://isyjasyljxweidcjvmpv.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
   ```

### Step 5: Create Database Schema
1. In Neon Console → SQL Editor
2. Copy/paste your existing Supabase schema:
   ```sql
   -- Your existing schema from supabase/migrations/
   -- (Use the PostgreSQL version, not MySQL)
   ```

### Step 6: Test Connection
```bash
curl -X POST http://localhost:3003/api/process-url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB", "submissionType": "Free"}'
```

## 🎯 **Why Neon is Perfect for StayOnBeat:**

### ✅ **FREE TIER Benefits:**
- **512MB storage** - Perfect for music metadata
- **Auto-pause** - Saves compute when not in use
- **PostgreSQL** - Same as Supabase (easy migration)
- **Branching** - Test schema changes safely
- **Backups** - Point-in-time recovery

### 🚀 **Scaling Path:**
- **FREE**: $0/month (perfect for startup)
- **LAUNCH**: $19/month (when you need more)
- **SCALE**: $69/month (for high traffic)

### 🔧 **Technical Benefits:**
- **Serverless** - No connection limits
- **Fast queries** - Optimized PostgreSQL
- **Global CDN** - Fast worldwide access
- **Zero downtime** - Seamless scaling

## 🛠️ **Migration from Local Database:**

### Option 1: Keep Both (Recommended)
```env
# Development: Use local database
NEXT_PUBLIC_USE_LOCAL_DB=true

# Production: Use Neon
DATABASE_URL="postgresql://your-neon-connection"
```

### Option 2: Migrate Data
```bash
# Export local data
node -e "
const fs = require('fs');
const submissions = JSON.parse(fs.readFileSync('data/submissions.json'));
console.log('INSERT INTO submissions VALUES...');
// Convert to SQL INSERT statements
"
```

## 📊 **Cost Comparison:**

| Database | FREE Tier | Monthly Cost | Best For |
|----------|-----------|--------------|----------|
| **Neon** | 512MB, auto-pause | $0 → $19 | **RECOMMENDED** |
| Railway | $5 credit | $0 → $20 | Multi-database |
| Turso | 9GB, 1B reads | $0 → $29 | SQLite/Edge |
| Local + Backup | Unlimited | $0 → $5 | Development |

## 🎵 **StayOnBeat Integration:**

Your app will automatically use Neon when you set `DATABASE_URL`:

```typescript
// Automatic database selection
function getDatabaseClient() {
  if (process.env.DATABASE_URL) {
    return neonClient        // 🚀 Neon PostgreSQL
  } else if (shouldUseLocalDatabase()) {
    return localDatabase     // 📁 Local
  } else {
    return supabase         // ☁️ Supabase
  }
}
```

## 🎉 **Success Checklist:**

- [ ] Neon account created (FREE)
- [ ] Database project created
- [ ] Connection string copied
- [ ] DATABASE_URL added to .env.local
- [ ] Schema migrated
- [ ] Test API call successful
- [ ] Admin dashboard working

## 🔗 **Useful Links:**

- [Neon Dashboard](https://console.neon.tech/)
- [Neon Documentation](https://neon.tech/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

**Perfect free database for your music startup! 🎵**
