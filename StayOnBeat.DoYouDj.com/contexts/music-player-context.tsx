"use client"

import React, { create<PERSON>ontext, useContext, useReducer, useEffect, useRef } from "react"
import { useSupabasePlayer } from "@/hooks/use-supabase-player"

export type Track = {
  id: string
  title: string
  artist: string
  artwork_url: string
  audio_url: string
  duration: number
  created_at: string
  spotify_id?: string
  isrc?: string
}

type PlayerState = {
  currentTrack: Track | null
  isPlaying: boolean
  volume: number
  progress: number
  duration: number
  playlist: Track[]
  currentIndex: number
}

type PlayerAction =
  | { type: "SET_TRACK"; payload: Track | null }
  | { type: "SET_PLAYLIST"; payload: Track[] }
  | { type: "SET_INDEX"; payload: number }
  | { type: "SET_PLAYING"; payload: boolean }
  | { type: "SET_VOLUME"; payload: number }
  | { type: "SET_PROGRESS"; payload: number }
  | { type: "SET_DURATION"; payload: number }

const initialState: PlayerState = {
  currentTrack: null,
  isPlaying: false,
  volume: 1,
  progress: 0,
  duration: 0,
  playlist: [],
  currentIndex: 0,
}

function playerReducer(state: PlayerState, action: PlayerAction): PlayerState {
  switch (action.type) {
    case "SET_TRACK":
      return { ...state, currentTrack: action.payload }
    case "SET_PLAYLIST":
      return { ...state, playlist: action.payload }
    case "SET_INDEX":
      return { ...state, currentIndex: action.payload }
    case "SET_PLAYING":
      return { ...state, isPlaying: action.payload }
    case "SET_VOLUME":
      return { ...state, volume: action.payload }
    case "SET_PROGRESS":
      return { ...state, progress: action.payload }
    case "SET_DURATION":
      return { ...state, duration: action.payload }
    default:
      return state
  }
}

type PlayerContextType = {
  state: PlayerState
  play: () => void
  pause: () => void
  next: () => void
  previous: () => void
  setVolume: (volume: number) => void
  setProgress: (progress: number) => void
  setPlaylist: (playlist: Track[]) => void
  setCurrentIndex: (index: number) => void
  addToPlaylist: (track: Track) => void
  removeFromPlaylist: (trackId: string) => void
  clearPlaylist: () => void
}

const PlayerContext = createContext<PlayerContextType | undefined>(undefined)

export function PlayerProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(playerReducer, initialState)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const { tracks, playlists, loading, error, fetchTracks, fetchPlaylists } = useSupabasePlayer()

  // Initialize audio element
  useEffect(() => {
    audioRef.current = new Audio()
    audioRef.current.volume = state.volume

    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.src = ""
      }
    }
  }, [])

  // Handle track changes
  useEffect(() => {
    if (state.currentTrack && audioRef.current) {
      audioRef.current.src = state.currentTrack.audio_url
      if (state.isPlaying) {
        audioRef.current.play()
      }
    }
  }, [state.currentTrack])

  // Handle volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = state.volume
    }
  }, [state.volume])

  // Handle audio events
  useEffect(() => {
    if (!audioRef.current) return

    const audio = audioRef.current

    const handleTimeUpdate = () => {
      dispatch({ type: "SET_PROGRESS", payload: audio.currentTime })
    }

    const handleDurationChange = () => {
      dispatch({ type: "SET_DURATION", payload: audio.duration })
    }

    const handleEnded = () => {
      next()
    }

    audio.addEventListener("timeupdate", handleTimeUpdate)
    audio.addEventListener("durationchange", handleDurationChange)
    audio.addEventListener("ended", handleEnded)

    return () => {
      audio.removeEventListener("timeupdate", handleTimeUpdate)
      audio.removeEventListener("durationchange", handleDurationChange)
      audio.removeEventListener("ended", handleEnded)
    }
  }, [])

  const play = () => {
    if (audioRef.current) {
      audioRef.current.play()
      dispatch({ type: "SET_PLAYING", payload: true })
    }
  }

  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      dispatch({ type: "SET_PLAYING", payload: false })
    }
  }

  const next = () => {
    if (state.currentIndex < state.playlist.length - 1) {
      const nextIndex = state.currentIndex + 1
      dispatch({ type: "SET_INDEX", payload: nextIndex })
      dispatch({ type: "SET_TRACK", payload: state.playlist[nextIndex] })
    }
  }

  const previous = () => {
    if (state.currentIndex > 0) {
      const prevIndex = state.currentIndex - 1
      dispatch({ type: "SET_INDEX", payload: prevIndex })
      dispatch({ type: "SET_TRACK", payload: state.playlist[prevIndex] })
    }
  }

  const setVolume = (volume: number) => {
    dispatch({ type: "SET_VOLUME", payload: volume })
  }

  const setProgress = (progress: number) => {
    if (audioRef.current && !isNaN(progress)) {
      audioRef.current.currentTime = progress
      dispatch({ type: "SET_PROGRESS", payload: progress })
    }
  }

  const setPlaylist = (playlist: Track[]) => {
    dispatch({ type: "SET_PLAYLIST", payload: playlist })
    if (playlist.length > 0) {
      dispatch({ type: "SET_INDEX", payload: 0 })
      dispatch({ type: "SET_TRACK", payload: playlist[0] })
    }
  }

  const setCurrentIndex = (index: number) => {
    if (index >= 0 && index < state.playlist.length) {
      dispatch({ type: "SET_INDEX", payload: index })
      dispatch({ type: "SET_TRACK", payload: state.playlist[index] })
    }
  }

  const addToPlaylist = (track: Track) => {
    const newPlaylist = [...state.playlist, track]
    dispatch({ type: "SET_PLAYLIST", payload: newPlaylist })
  }

  const removeFromPlaylist = (trackId: string) => {
    const newPlaylist = state.playlist.filter((track) => track.id !== trackId)
    dispatch({ type: "SET_PLAYLIST", payload: newPlaylist })
  }

  const clearPlaylist = () => {
    dispatch({ type: "SET_PLAYLIST", payload: [] })
    dispatch({ type: "SET_TRACK", payload: null })
    dispatch({ type: "SET_INDEX", payload: 0 })
  }

  return (
    <PlayerContext.Provider
      value={{
        state,
        play,
        pause,
        next,
        previous,
        setVolume,
        setProgress,
        setPlaylist,
        setCurrentIndex,
        addToPlaylist,
        removeFromPlaylist,
        clearPlaylist,
      }}
    >
      {children}
    </PlayerContext.Provider>
  )
}

export function usePlayer() {
  const context = useContext(PlayerContext)
  if (context === undefined) {
    throw new Error("usePlayer must be used within a PlayerProvider")
  }
  return context
}
