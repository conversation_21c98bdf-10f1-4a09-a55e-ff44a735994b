#!/usr/bin/env node

/**
 * Direct URL Processing Test Script
 * Tests the URL processing workflow without going through the API
 */

// Set environment variables for testing
process.env.NODE_ENV = 'development'
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://production-down.supabase.co' // This will trigger testing mode

console.log('🧪 StayOnBeat URL Processing Test')
console.log('================================')

// Test URLs for different platforms
const testUrls = [
  {
    url: 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB',
    submissionType: 'Free',
    description: 'Spotify Track'
  },
  {
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    submissionType: 'GA',
    description: 'YouTube Video'
  },
  {
    url: 'https://soundcloud.com/artist/track',
    submissionType: 'VIP',
    description: 'SoundCloud Track'
  },
  {
    url: 'https://bandcamp.com/track/example',
    submissionType: 'Skip',
    description: 'Bandcamp Track'
  }
]

async function testUrlProcessing() {
  try {
    // Import the URL processor
    const { processUrl } = await import('./lib/url-processor.js')
    
    console.log('📦 URL processor imported successfully')
    
    for (const testCase of testUrls) {
      console.log(`\n🔄 Testing: ${testCase.description}`)
      console.log(`   URL: ${testCase.url}`)
      console.log(`   Type: ${testCase.submissionType}`)
      
      try {
        const result = await processUrl({
          url: testCase.url,
          submissionType: testCase.submissionType,
          userId: 'test-user-123',
          userEmail: '<EMAIL>'
        })
        
        if (result.success) {
          console.log('   ✅ SUCCESS')
          console.log(`   📝 Submission ID: ${result.submissionId}`)
          console.log(`   🎵 Track: ${result.trackCard?.title} by ${result.trackCard?.artist}`)
          console.log(`   🎯 Platform: ${result.platformDetection?.platform}`)
          console.log(`   ⏱️ Status: ${result.processingStatus}`)
        } else {
          console.log('   ❌ FAILED')
          console.log(`   💥 Error: ${result.error}`)
          console.log(`   🎯 Platform: ${result.platformDetection?.platform}`)
          console.log(`   ⏱️ Status: ${result.processingStatus}`)
        }
      } catch (error) {
        console.log('   💥 EXCEPTION')
        console.log(`   Error: ${error.message}`)
      }
    }
    
    console.log('\n🎉 URL Processing Test Complete!')
    
  } catch (error) {
    console.error('❌ Failed to import URL processor:', error.message)
    console.error('Stack:', error.stack)
  }
}

// Test platform detection separately
async function testPlatformDetection() {
  try {
    console.log('\n🔍 Testing Platform Detection')
    console.log('=============================')
    
    const { detectPlatform } = await import('./lib/platform-detector.js')
    
    for (const testCase of testUrls) {
      console.log(`\n🔗 URL: ${testCase.url}`)
      const detection = detectPlatform(testCase.url)
      
      console.log(`   Platform: ${detection.platform}`)
      console.log(`   Valid: ${detection.isValid}`)
      console.log(`   Platform ID: ${detection.platformId}`)
      console.log(`   Content Type: ${detection.contentType}`)
      if (detection.error) {
        console.log(`   Error: ${detection.error}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Failed to test platform detection:', error.message)
  }
}

// Run tests
async function runAllTests() {
  await testPlatformDetection()
  await testUrlProcessing()
}

runAllTests().catch(console.error)
