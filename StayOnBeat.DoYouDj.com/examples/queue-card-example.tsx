"use client"

import { useState } from "react"
import { QueueCard, type QueueCardItem } from "@/components/queue-card"

export default function QueueCardExample() {
  const [items, setItems] = useState<QueueCardItem[]>([
    {
      id: 1,
      position: 1,
      artistName: "DJ Harmony",
      artistId: "1",
      songTitle: "Midnight Groove",
      type: "VIP",
      submissionTime: "10:15 AM",
      platform: "soundcloud",
      url: "https://soundcloud.com/djharmony/midnight_groove",
      isPlaying: true,
    },
    {
      id: 2,
      position: 2,
      artistName: "Beatsmith",
      artistId: "2",
      songTitle: "Urban Echoes",
      type: "Skip",
      submissionTime: "10:22 AM",
      platform: "spotify",
      url: "https://open.spotify.com/track/123456",
    },
    {
      id: 3,
      position: 3,
      artistName: "Melody Maker",
      artistId: "3",
      songTitle: "Sunset Dreams",
      type: "GA",
      submissionTime: "10:30 AM",
      platform: "youtube",
      url: "https://youtube.com/watch?v=123456",
    },
  ])

  const [expandedItems, setExpandedItems] = useState<Record<number, boolean>>({})

  const toggleExpand = (id: number) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Queue Card Examples</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Standard Queue Cards</h2>
          <div className="space-y-4">
            {items.map((item) => (
              <QueueCard
                key={item.id}
                item={item}
                isHighlighted={item.position === 1}
                isExpanded={expandedItems[item.id]}
                onExpand={toggleExpand}
              >
                <div className="text-sm">
                  <p>This is the expanded content for {item.songTitle}.</p>
                  <p className="mt-2">You can put any content here!</p>
                </div>
              </QueueCard>
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Simplified Cards (No Position)</h2>
          <div className="space-y-4">
            {items.map((item) => (
              <QueueCard
                key={item.id}
                item={{
                  ...item,
                  position: undefined,
                  submissionTime: undefined,
                }}
                className="border-cyan-800/30 hover:border-cyan-600/50"
              />
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Usage Instructions</h2>
        <div className="bg-black/40 p-4 rounded-md border border-purple-500/30">
          <p className="mb-2">Import the component:</p>
          <pre className="bg-black/60 p-2 rounded overflow-x-auto">
            {`import { QueueCard, type QueueCardItem } from "@/components/queue-card"`}
          </pre>

          <p className="mt-4 mb-2">Basic usage:</p>
          <pre className="bg-black/60 p-2 rounded overflow-x-auto">
            {`<QueueCard
  item={{
    id: 1,
    artistName: "DJ Harmony",
    songTitle: "Midnight Groove",
    type: "VIP",
    platform: "soundcloud"
  }}
/>`}
          </pre>

          <p className="mt-4 mb-2">With expanded content:</p>
          <pre className="bg-black/60 p-2 rounded overflow-x-auto">
            {`<QueueCard
  item={item}
  isExpanded={expandedItems[item.id]}
  onExpand={toggleExpand}
>
  <div>Your custom expanded content here</div>
</QueueCard>`}
          </pre>
        </div>
      </div>
    </div>
  )
}
