"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Music, Play, Calendar, ListMusic } from "lucide-react"
import { supabase } from "@/lib/supabase"
import Link from "next/link"

export function FinalizedPlaylists() {
  const [playlists, setPlaylists] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchPlaylists = async () => {
      try {
        const { data, error } = await supabase
          .from("playlists")
          .select("*")
          .eq("visibility", "public")
          .eq("is_finalized", true)
          .order("created_at", { ascending: false })

        if (error) throw error
        setPlaylists(data || [])
      } catch (error) {
        console.error("Error fetching playlists:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPlaylists()
  }, [])

  const downloadPlaylist = async (playlistId: number, playlistName: string) => {
    try {
      const { data, error } = await supabase
        .from("playlist_files")
        .select("content")
        .eq("playlist_id", playlistId)
        .single()

      if (error) throw error

      // Create and download the file
      const blob = new Blob([data.content], { type: "audio/x-mpegurl" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `${playlistName.replace(/\s+/g, "_")}.m3u8`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading playlist:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    )
  }

  if (playlists.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Music className="mx-auto h-12 w-12 text-gray-400 mb-3" />
            <p className="text-gray-500">No public playlists available yet</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Finalized Playlists</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {playlists.map((playlist) => (
          <Card key={playlist.id} className="overflow-hidden border border-purple-500/30 bg-black/80">
            <CardHeader className="bg-gradient-to-r from-purple-900/50 to-indigo-900/50 pb-2">
              <CardTitle>{playlist.name}</CardTitle>
              <CardDescription className="flex items-center text-gray-400">
                <Calendar className="h-4 w-4 mr-1" />
                {new Date(playlist.created_at).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <ListMusic className="h-5 w-5 mr-2 text-purple-500" />
                  <span>{playlist.track_count} tracks</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadPlaylist(playlist.id, playlist.name)}
                    className="text-purple-500 border-purple-500/50"
                  >
                    <Download className="h-4 w-4 mr-1" /> M3U8
                  </Button>
                  <Link href={`/playlists/${playlist.id}`}>
                    <Button size="sm" className="bg-purple-600">
                      <Play className="h-4 w-4 mr-1" /> Play
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
