"use client"

import type React from "react"

import { useEffect, useId, useRef, useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { useOutsideClick } from "@/hooks/use-outside-click"
import { Card, CardContent } from "@/components/ui/card"
import { X, Music, ArrowRight } from "lucide-react"

export type TierCardProps = {
  title: string
  description: string
  price: string
  imageSrc: string
  color: string
  formContent: React.ReactNode
}

export function ExpandableTierCards({
  tiers,
  onSubmit,
  isSubmitting,
}: {
  tiers: TierCardProps[]
  onSubmit: (tier: string) => void
  isSubmitting: boolean
}) {
  const [activeTier, setActiveTier] = useState<TierCardProps | null>(null)
  const id = useId()
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function onKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setActiveTier(null)
      }
    }

    if (activeTier) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    window.addEventListener("keydown", onKeyDown)
    return () => window.removeEventListener("keydown", onKeyDown)
  }, [activeTier])

  useOutsideClick(ref, () => setActiveTier(null))

  return (
    <>
      <AnimatePresence>
        {activeTier && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-md h-full w-full z-10"
          />
        )}
      </AnimatePresence>
      <AnimatePresence>
        {activeTier ? (
          <div className="fixed inset-0 grid place-items-center z-[1000] p-4">
            <motion.button
              key={`button-${activeTier.title}-${id}`}
              layout
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0, transition: { duration: 0.05 } }}
              className="flex absolute top-4 right-4 lg:top-8 lg:right-8 items-center justify-center bg-black text-[#00ffff] rounded-full h-8 w-8 z-[1001] border border-[#00ffff] shadow-[0_0_5px_#00ffff]"
              onClick={() => setActiveTier(null)}
            >
              <X className="h-5 w-5" />
            </motion.button>
            <motion.div
              layoutId={`card-${activeTier.title}-${id}`}
              ref={ref}
              className="w-full max-w-2xl h-full md:h-fit md:max-h-[90%] flex flex-col bg-black border-2 border-[#00ffff] shadow-[0_0_10px_#00ffff] rounded-xl overflow-hidden relative"
            >
              <div className="relative z-10 p-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <motion.h3 layoutId={`title-${activeTier.title}-${id}`} className="text-[#00ffff] text-lg font-bold neon-text-cyan">
                      {activeTier.title}
                    </motion.h3>
                    <motion.p layoutId={`description-${activeTier.title}-${id}`} className="text-sm text-[#00ffff]/70">
                      {activeTier.description}
                    </motion.p>
                  </div>
                  <motion.div layoutId={`price-${activeTier.title}-${id}`} className="text-[#00ffff] font-bold text-xl neon-text-cyan">
                    {activeTier.price}
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ delay: 0.1 }}
                  className="overflow-auto max-h-[60vh]"
                >
                  {activeTier.formContent}
                </motion.div>
              </div>
            </motion.div>
          </div>
        ) : null}
      </AnimatePresence>

      <div className="grid grid-cols-2 grid-rows-2 gap-8 w-full" style={{ perspective: "1000px" }}>
        {tiers.map((tier) => (
          <div className="mx-auto w-full" key={tier.title}>
            <motion.div
              layoutId={`card-${tier.title}-${id}`}
              onClick={() => setActiveTier(tier)}
              className="relative cursor-pointer group"
            >
              <Card
                className={`relative bg-black border-2 border-[#00ffff] shadow-[0_0_10px_#00ffff] transition-all duration-300 hover:shadow-[0_0_20px_#00ffff] overflow-hidden aspect-square`}
                style={{
                  transform: `translateZ(${
                    tier.title.toLowerCase().includes("premium") ? "20px" :
                    tier.title.toLowerCase().includes("skip") ? "15px" :
                    tier.title.toLowerCase().includes("guaranteed") ? "10px" : "5px"
                  })`,
                  zIndex: tier.title.toLowerCase().includes("premium") ? 4 :
                          tier.title.toLowerCase().includes("skip") ? 3 :
                          tier.title.toLowerCase().includes("guaranteed") ? 2 : 1
                }}
              >
                <CardContent className="p-0 relative z-10 h-full flex flex-col items-center justify-between">
                  <motion.div layoutId={`image-${tier.title}-${id}`} className="w-full">
                    <img
                      src={tier.imageSrc}
                      alt={tier.title}
                      className="w-full max-w-full mx-auto object-contain object-top pt-2"
                    />
                  </motion.div>

                  {/* Call to Action Button */}
                  <div className="w-full flex justify-center items-center mb-4 mt-2">
                    <div className="flex items-center justify-center bg-black px-4 py-2 rounded-full border border-[#00ffff] shadow-[0_0_5px_#00ffff] group-hover:shadow-[0_0_10px_#00ffff] transition-all duration-300">
                      <Music className="h-4 w-4 mr-2 text-[#00ffff] filter drop-shadow-[0_0_2px_#00ffff]" />
                      <span className="text-[#00ffff] font-bold text-sm filter drop-shadow-[0_0_2px_#00ffff]">
                        CLICK NOW
                      </span>
                      <ArrowRight className="h-4 w-4 ml-2 text-[#00ffff] filter drop-shadow-[0_0_2px_#00ffff]" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        ))}
      </div>
    </>
  )
}
