'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export function FixedAdminButton() {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith('/admin');

  if (isAdminRoute) {
    return null;
  }

  return (
    <div className="fixed bottom-8 left-0 right-0 z-50 flex justify-center items-center pointer-events-none">
      <div className="pointer-events-auto flex flex-col items-center justify-center">
        <Link href="/admin">
          <button
            className="admin-login-button w-10 h-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 transition-all duration-300 shadow-lg hover:scale-110 mx-auto"
            style={{
              background:
                "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
              boxShadow:
                "inset 0 0 15px rgba(255, 0, 0, 0.6), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 20px rgba(255, 0, 0, 0.7), 0 0 30px rgba(255, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
              transform: "translateZ(0)",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-white neon-text-red"
            >
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <span className="sr-only">Admin</span>
          </button>
        </Link>
        <p className="text-white neon-text-red mt-2 text-sm">Admin</p>
      </div>
    </div>
  );
}
