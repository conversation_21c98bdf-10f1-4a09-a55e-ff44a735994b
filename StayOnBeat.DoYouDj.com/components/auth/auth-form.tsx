"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Loader2 } from "lucide-react"
import { signInWithEmail, signUpWithEmail } from "@/lib/supabase"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

export function AuthForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      setError("Please enter both email and password")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const { data, error } = await signInWithEmail(email, password)

      if (error) throw error

      if (data.user) {
        toast.success("Signed in successfully")
        router.push("/profile")
        router.refresh()
      }
    } catch (err) {
      console.error("Sign in error:", err)
      setError((err as Error).message || "Failed to sign in")
      toast.error("Failed to sign in")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      setError("Please enter both email and password")
      return
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const { data, error } = await signUpWithEmail(email, password)

      if (error) throw error

      if (data.user) {
        toast.success("Account created successfully")

        // If email confirmation is required
        if (data.user.identities && data.user.identities.length === 0) {
          setError("Check your email for a confirmation link")
        } else {
          router.push("/profile")
          router.refresh()
        }
      }
    } catch (err) {
      console.error("Sign up error:", err)
      setError((err as Error).message || "Failed to create account")
      toast.error("Failed to create account")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto bg-black/80 border border-purple-500/30 shadow-[0_0_15px_rgba(147,51,234,0.2)] text-white">
      <Tabs defaultValue="signin" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-black/40">
          <TabsTrigger value="signin" className="text-white data-[state=active]:bg-purple-900 data-[state=active]:text-white">Sign In</TabsTrigger>
          <TabsTrigger value="signup" className="text-white data-[state=active]:bg-purple-900 data-[state=active]:text-white">Sign Up</TabsTrigger>
        </TabsList>

        <TabsContent value="signin">
          <form onSubmit={handleSignIn}>
            <CardHeader className="text-white">
              <CardTitle>Sign In</CardTitle>
              <CardDescription className="text-gray-300">
                Enter your email and password to sign in to your account
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="bg-black/40 border-purple-500/30 focus:border-purple-500 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-black/40 border-purple-500/30 focus:border-purple-500 text-white"
                />
              </div>

              {error && (
                <div className="text-sm text-red-500">{error}</div>
              )}
            </CardContent>

            <CardFooter>
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </CardFooter>
          </form>
        </TabsContent>

        <TabsContent value="signup">
          <form onSubmit={handleSignUp}>
            <CardHeader className="text-white">
              <CardTitle>Create Account</CardTitle>
              <CardDescription className="text-gray-300">
                Enter your email and create a password to sign up
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-signup" className="text-white">Email</Label>
                <Input
                  id="email-signup"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="bg-black/40 border-purple-500/30 focus:border-purple-500 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password-signup" className="text-white">Password</Label>
                <Input
                  id="password-signup"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-black/40 border-purple-500/30 focus:border-purple-500 text-white"
                />
                <p className="text-xs text-purple-400">
                  Password must be at least 6 characters
                </p>
              </div>

              {error && (
                <div className="text-sm text-red-500">{error}</div>
              )}
            </CardContent>

            <CardFooter>
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </CardFooter>
          </form>
        </TabsContent>
      </Tabs>
    </Card>
  )
}
