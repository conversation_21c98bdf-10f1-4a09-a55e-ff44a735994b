"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { PlatformButton } from "@/components/auth/platform-button"
// Using Supabase's built-in OAuth
import type { SocialPlatform } from "@/lib/types/auth"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, Loader2 } from "lucide-react"
// Dialog components removed - showing all options by default
import { signInWithOAuth, signInWithEmail, signUpWithEmail } from "@/lib/supabase"
import { useRouter } from "next/navigation"

// Featured platforms shown in the main grid
const featuredPlatforms: SocialPlatform[] = ["spotify", "apple", "youtube", "soundcloud"]

// Other platforms shown in the "More options" section
const otherPlatforms: SocialPlatform[] = ["amazon", "deezer", "instagram", "tiktok", "facebook", "twitter"]

interface SocialLoginCardProps {
  title?: string
  description?: string
  onSuccess?: (userData: any) => void
  onError?: (error: Error) => void
  initialTab?: "connect" | "login" | "register"
}

export function SocialLoginCard({
  title = "Connect with your music services",
  description = "Sign in with your favorite music or social platform to access your personalized experience",
  onSuccess,
  onError,
  initialTab = "connect",
}: SocialLoginCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<"connect" | "login" | "register">(initialTab)
  const [showMoreOptions, setShowMoreOptions] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState<string | null>(null)

  // Set the active tab when initialTab prop changes
  useEffect(() => {
    setActiveTab(initialTab)
  }, [initialTab])

  // Add specific handler functions for major platforms
  const handleSpotifyAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const { data, error } = await signInWithOAuth("spotify")
      if (error) throw error
    } catch (err) {
      setError("Spotify authentication failed. Please try again.")
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  const handleYouTubeAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const { data, error } = await signInWithOAuth("google")
      if (error) throw error
    } catch (err) {
      setError("YouTube authentication failed. Please try again.")
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  const handleSoundCloudAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)
      // Use Supabase's standard OAuth implementation for SoundCloud
      const { data, error } = await signInWithOAuth("soundcloud")
      if (error) throw error
    } catch (err) {
      setError("SoundCloud authentication failed. Please try again.")
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  // Update the handlePlatformAuth function to use specific handlers for major platforms
  const handlePlatformAuth = async (platform: SocialPlatform) => {
    try {
      setIsLoading(true)
      setError(null)

      // Map our platform names to Supabase provider names
      const supabaseProvider = mapPlatformToSupabaseProvider(platform)

      if (!supabaseProvider) {
        throw new Error(`Authentication with ${platform} is not supported yet.`)
      }

      // Use Supabase's standard OAuth implementation
      const { data, error } = await signInWithOAuth(supabaseProvider)

      if (error) {
        // Log the specific error for debugging
        console.error(`${platform} authentication error:`, error)

        // Provide a user-friendly error message
        if (error.message?.includes("popup_closed_by_user")) {
          throw new Error("Authentication was cancelled. Please try again.")
        } else if (error.message?.includes("network")) {
          throw new Error("Network error. Please check your connection and try again.")
        } else {
          throw error
        }
      }

      // Supabase will handle the redirect
      return
    } catch (err) {
      console.error(`${platform} authentication error:`, err)
      const errorMessage = "Authentication service unavailable. Please try again later."
      setError(errorMessage)
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  // Add this helper function inside the component
  function mapPlatformToSupabaseProvider(platform: SocialPlatform): string | null {
    // Map our platform names to Supabase provider names
    const mapping: Record<SocialPlatform, string | null> = {
      spotify: "spotify",
      apple: "apple",
      amazon: null, // Not supported by Supabase
      deezer: null, // Not supported by Supabase
      instagram: null, // Not supported by Supabase
      tiktok: null, // Not supported by Supabase
      youtube: "google", // Use Google provider for YouTube
      soundcloud: "soundcloud", // If configured as a custom OAuth provider
      facebook: "facebook",
      twitter: "twitter",
    }

    return mapping[platform]
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setIsLoading(true)
      setError(null)

      const { data, error } = await signInWithEmail(email, password)

      if (error) {
        throw error
      }

      if (data.user) {
        onSuccess?.(data.user)
        router.push("/profile")
      }

      setIsLoading(false)
    } catch (err) {
      setError((err as Error).message || "Login failed. Please try again.")
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  const handleEmailRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    // Similar to login but for registration
    try {
      setIsLoading(true)
      setError(null)

      const { data, error } = await signUpWithEmail(email, password)

      if (error) {
        throw error
      }

      if (data.user) {
        onSuccess?.(data.user)
        // For email confirmation flow, show a success message
        setError("Check your email for a confirmation link.")
      }

      setIsLoading(false)
    } catch (err) {
      setError((err as Error).message || "Registration failed. Please try again.")
      setIsLoading(false)
      onError?.(err as Error)
    }
  }

  // Add router to the component
  const router = useRouter()

  return (
    <Card className="w-full max-w-md relative overflow-hidden border border-[#00ffff]/50 neon-box-cyan bg-gradient-to-br from-purple-900/50 to-black">
      {/* Card header removed */}

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid grid-cols-2 mb-6">
          <TabsTrigger value="connect">Connect</TabsTrigger>
          <TabsTrigger value="login">Login</TabsTrigger>
        </TabsList>

        <TabsContent value="connect" className="space-y-6">
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
              {/* Show all platforms in a single grid */}
              {[...featuredPlatforms, ...otherPlatforms].map((platform) => (
                <PlatformButton key={platform} platform={platform} onClick={handlePlatformAuth} disabled={isLoading} />
              ))}
            </div>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            )}

            <div className="pt-4 border-t border-gray-200 dark:border-gray-800">
              <Button onClick={() => setActiveTab("login")} variant="outline" className="w-full mt-2">
                Login with Email
              </Button>
            </div>
          </CardContent>
        </TabsContent>

        <TabsContent value="login">
          <CardContent>
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>

              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    <span>{error}</span>
                  </div>
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Logging in...
                  </>
                ) : (
                  "Log in"
                )}
              </Button>
            </form>
          </CardContent>
        </TabsContent>
      </Tabs>

      <CardFooter className="flex justify-center border-t pt-6">
        <p className="text-xs text-gray-500">
          By connecting, you agree to our{" "}
          <a href="#" className="text-red-500 hover:underline">
            Terms of Service
          </a>{" "}
          and{" "}
          <a href="#" className="text-red-500 hover:underline">
            Privacy Policy
          </a>
        </p>
      </CardFooter>
    </Card>
  )
}
