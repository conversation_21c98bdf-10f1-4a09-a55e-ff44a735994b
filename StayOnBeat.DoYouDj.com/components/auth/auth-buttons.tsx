'use client'

import { SignIn<PERSON>utton, SignUpButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'

export function AuthButtons() {
  return (
    <div className="flex items-center gap-4">
      <SignedOut>
        <SignInButton mode="modal">
          <Button variant="outline" className="bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta" style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}>
            Sign In
          </Button>
        </SignInButton>
        <SignUpButton mode="modal">
          <Button className="bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta" style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}>
            Sign Up
          </Button>
        </SignUpButton>
      </SignedOut>
      <SignedIn>
        <UserButton 
          appearance={{
            elements: {
              userButtonAvatarBox: "border-2 border-[#00ffff] shadow-[0_0_5px_#00ffff]",
            }
          }}
        />
      </SignedIn>
    </div>
  )
}
