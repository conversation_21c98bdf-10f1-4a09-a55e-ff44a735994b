"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Music, Youtube, Instagram, Facebook, Twitter } from "lucide-react"

interface PlatformButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  platform: string
  variant?: "default" | "outline" | "ghost"
}

const platformIcons = {
  spotify: Music,
  youtube: Youtube,
  instagram: Instagram,
  facebook: Facebook,
  twitter: Twitter,
}

export function PlatformButton({ platform, className, variant = "default", ...props }: PlatformButtonProps) {
  const Icon = platformIcons[platform as keyof typeof platformIcons] || Music

  return (
    <Button
      variant={variant}
      className={cn("w-full", className)}
      {...props}
    >
      <Icon className="mr-2 h-4 w-4" />
      {platform.charAt(0).toUpperCase() + platform.slice(1)}
    </Button>
  )
}
