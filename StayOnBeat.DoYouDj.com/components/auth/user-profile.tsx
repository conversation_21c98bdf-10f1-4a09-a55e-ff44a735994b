'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'

interface Submission {
  id: string
  artist_name: string
  song_title: string
  status: string
  created_at: string
}

export function UserProfile() {
  const { user, isLoaded, isSignedIn } = useUser()
  const [submissions, setSubmissions] = useState<Submission[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Fetch user submissions when the user is signed in
  useEffect(() => {
    if (isSignedIn) {
      fetchSubmissions()
    }
  }, [isSignedIn])

  // Function to fetch user submissions
  const fetchSubmissions = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/submissions')
      const data = await response.json()
      
      if (response.ok) {
        setSubmissions(data.submissions || [])
      } else {
        console.error('Error fetching submissions:', data.error)
      }
    } catch (error) {
      console.error('Failed to fetch submissions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // If Clerk is still loading, show a loading state
  if (!isLoaded) {
    return (
      <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box h-full">
        <CardHeader className="p-3">
          <CardTitle className="text-black text-xs">Loading Profile...</CardTitle>
        </CardHeader>
      </Card>
    )
  }

  // If user is not signed in, show a sign-in prompt
  if (!isSignedIn) {
    return (
      <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box h-full">
        <CardHeader className="p-3">
          <CardTitle className="text-black text-xs">Sign in to view your profile</CardTitle>
        </CardHeader>
      </Card>
    )
  }

  // User is signed in, show their profile
  return (
    <Card className="bg-black/80 border border-[#ff00ff]/30 neon-box h-full">
      <CardHeader className="p-3">
        <CardTitle className="text-black text-xs">Artist Profile</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center p-3 bg-black/80 text-white">
        <Avatar className="h-16 w-16 mb-3 border border-gray-700">
          <AvatarImage src={user.imageUrl} alt={user.fullName || 'User'} />
          <AvatarFallback className="bg-gray-800 text-white text-sm">
            {user.firstName?.[0]}{user.lastName?.[0]}
          </AvatarFallback>
        </Avatar>
        <h2 className="text-sm font-bold text-white mb-1">{user.fullName || 'Artist Name'}</h2>
        <p className="text-xs text-gray-400 mb-2">{user.primaryEmailAddress?.emailAddress || 'Email not provided'}</p>
        
        <div className="bg-black/90 p-2 rounded-lg border border-gray-800 w-full text-center mb-4">
          <p className="text-gray-300 italic text-xs">
            "Music is the universal language of mankind."
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Member since: {new Date(user.createdAt).toLocaleDateString()}
          </p>
        </div>
        
        <h3 className="text-sm font-bold text-white mb-2 self-start">Recent Submissions</h3>
        
        {isLoading ? (
          <p className="text-xs text-gray-400">Loading submissions...</p>
        ) : submissions.length > 0 ? (
          <div className="space-y-2 w-full">
            {submissions.map((submission) => (
              <div key={submission.id} className="border-b border-gray-800 pb-2">
                <p className="font-medium text-white text-xs">{submission.song_title}</p>
                <p className="text-xs text-gray-400">
                  Submitted on {new Date(submission.created_at).toLocaleDateString()}
                </p>
                <p className={`text-xs mt-1 ${
                  submission.status === 'played' ? 'text-green-600' :
                  submission.status === 'in_queue' ? 'text-yellow-600' :
                  'text-blue-600'
                }`}>
                  Status: {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-xs text-gray-400">No submissions yet</p>
        )}
        
        <Button 
          onClick={fetchSubmissions}
          className="mt-4 bg-black border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 neon-cta"
          style={{ boxShadow: "0 0 2.5px #00ffff, 0 0 5px rgba(0, 255, 255, 0.5)" }}
          disabled={isLoading}
        >
          {isLoading ? 'Refreshing...' : 'Refresh Submissions'}
        </Button>
      </CardContent>
    </Card>
  )
}
