"use client"

import React, { useState } from "react"
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  closestCenter
} from "@dnd-kit/core"
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable"
import { QueueCardItem } from "./queue-card"
import { DraggableSubmissionCard } from "./draggable-submission-card"
import { SortableQueueList } from "./sortable-queue-list"

interface SubmissionsDndContextProps {
  submissions: QueueCardItem[]
  queue: QueueCardItem[]
  onAddToQueue: (submission: QueueCardItem) => void
  onQueueReorder: (items: QueueCardItem[]) => void
  expandedItems: Record<string | number, boolean>
  onExpand: (id: number) => void
  isPlaylistLocked: boolean
  neonTurquoiseStyle: React.CSSProperties
  children?: React.ReactNode
}

export function SubmissionsDndContext({
  submissions,
  queue,
  onAddToQueue,
  onQueueReorder,
  expandedItems,
  onExpand,
  isPlaylistLocked,
  neonTurquoiseStyle,
  children
}: SubmissionsDndContextProps) {
  const [activeId, setActiveId] = useState<string | null>(null)
  const [activeItem, setActiveItem] = useState<QueueCardItem | null>(null)

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    setActiveId(active.id.toString())

    // If it's a submission being dragged
    if (active.id.toString().startsWith('submission-')) {
      const submissionId = active.id.toString().replace('submission-', '')
      const submission = submissions.find(item => item.id.toString() === submissionId)
      if (submission) {
        setActiveItem(submission)
      }
    }
    // If it's a queue item being dragged
    else {
      const queueItem = queue.find(item => item.id.toString() === active.id.toString())
      if (queueItem) {
        setActiveItem(queueItem)
      }
    }
  }

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over) {
      // If dragging a submission to the queue
      if (active.id.toString().startsWith('submission-') && over.id.toString() === 'queue-droppable') {
        const submissionId = active.id.toString().replace('submission-', '')
        const submission = submissions.find(item => item.id.toString() === submissionId)
        if (submission) {
          onAddToQueue(submission)
        }
      }
    }

    setActiveId(null)
    setActiveItem(null)
  }

  // Log for debugging
  console.log('SubmissionsDndContext rendering with:', {
    submissionsCount: submissions.length,
    queueCount: queue.length,
    isPlaylistLocked
  })

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}

      {/* Drag overlay - shows a preview of the item being dragged */}
      <DragOverlay adjustScale={true}>
        {activeId && activeItem ? (
          <div className="admin-card shadow-lg border-purple-500 opacity-80">
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <p className="font-medium">{activeItem.songTitle}</p>
              </div>
              <Badge variant="default">{activeItem.type}</Badge>
            </div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
