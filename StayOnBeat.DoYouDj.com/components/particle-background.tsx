"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"

interface Particle {
  x: number
  y: number
  size: number
  speedX: number
  speedY: number
  color: string
  alpha: number
}

interface ParticleBackgroundProps {
  particleCount?: number
  particleColor?: string
  backgroundColor?: string
  interactive?: boolean
  maxSpeed?: number
  connectParticles?: boolean
  responsive?: boolean
}

export default function ParticleBackground({
  particleCount = 100,
  particleColor = "#00ffff",
  backgroundColor = "rgba(0, 0, 0, 0.8)",
  interactive = true,
  maxSpeed = 0.5,
  connectParticles = true,
  responsive = true,
}: ParticleBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [particles, setParticles] = useState<Particle[]>([])
  const [mouse, setMouse] = useState({ x: 0, y: 0, radius: 120 })
  const animationRef = useRef<number>()

  // Initialize particles
  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const handleResize = () => {
      if (responsive) {
        canvas.width = window.innerWidth
        canvas.height = window.innerHeight
        setDimensions({ width: canvas.width, height: canvas.height })
      } else {
        canvas.width = canvas.clientWidth
        canvas.height = canvas.clientHeight
        setDimensions({ width: canvas.width, height: canvas.height })
      }
    }

    handleResize()
    window.addEventListener("resize", handleResize)

    // Create particles
    const newParticles: Particle[] = []
    for (let i = 0; i < particleCount; i++) {
      const size = Math.random() * 5 + 1
      const x = Math.random() * canvas.width
      const y = Math.random() * canvas.height
      const speedX = (Math.random() - 0.5) * maxSpeed
      const speedY = (Math.random() - 0.5) * maxSpeed
      const alpha = Math.random() * 0.6 + 0.2

      newParticles.push({
        x,
        y,
        size,
        speedX,
        speedY,
        color: particleColor,
        alpha,
      })
    }
    setParticles(newParticles)

    return () => {
      window.removeEventListener("resize", handleResize)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [particleCount, particleColor, responsive, maxSpeed])

  // Animation loop
  useEffect(() => {
    if (!canvasRef.current || particles.length === 0) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const animate = () => {
      ctx.fillStyle = backgroundColor
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Update and draw particles
      const updatedParticles = [...particles]
      for (let i = 0; i < updatedParticles.length; i++) {
        const p = updatedParticles[i]

        // Move particles
        p.x += p.speedX
        p.y += p.speedY

        // Boundary check
        if (p.x > canvas.width) p.x = 0
        else if (p.x < 0) p.x = canvas.width
        if (p.y > canvas.height) p.y = 0
        else if (p.y < 0) p.y = canvas.height

        // Mouse interaction
        if (interactive) {
          const dx = p.x - mouse.x
          const dy = p.y - mouse.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < mouse.radius) {
            const angle = Math.atan2(dy, dx)
            const pushX = Math.cos(angle) * (mouse.radius / distance) * 0.5
            const pushY = Math.sin(angle) * (mouse.radius / distance) * 0.5

            p.x += pushX
            p.y += pushY
          }
        }

        // Draw particle
        ctx.beginPath()
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2)
        ctx.fillStyle = p.color
        ctx.globalAlpha = p.alpha
        ctx.fill()
        ctx.globalAlpha = 1

        // Connect particles
        if (connectParticles) {
          for (let j = i + 1; j < updatedParticles.length; j++) {
            const p2 = updatedParticles[j]
            const dx = p.x - p2.x
            const dy = p.y - p2.y
            const distance = Math.sqrt(dx * dx + dy * dy)

            if (distance < 100) {
              ctx.beginPath()
              ctx.strokeStyle = p.color
              ctx.globalAlpha = (1 - distance / 100) * 0.5 * p.alpha
              ctx.lineWidth = 0.5
              ctx.moveTo(p.x, p.y)
              ctx.lineTo(p2.x, p2.y)
              ctx.stroke()
              ctx.globalAlpha = 1
            }
          }
        }
      }

      setParticles(updatedParticles)
      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [particles, mouse, backgroundColor, interactive, connectParticles, dimensions])

  // Mouse movement handler
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!interactive || !canvasRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    setMouse({
      ...mouse,
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    })
  }

  return <canvas ref={canvasRef} className="fixed top-0 left-0 w-full h-full -z-10" onMouseMove={handleMouseMove} />
}
