"use client"

import { useState, useEffect, useCallback } from "react"
import { supabase } from "@/lib/supabase"
import { HLSPlayer } from "@/components/video-player/hls-player"
import { getPlaylistItemsFromSupabase, createM3U8FromItems } from "@/lib/m3u8-utils"
import type { PlaylistItem } from "@/lib/types"
import { Skeleton } from "@/components/ui/skeleton"

interface SupabaseM3U8PlayerProps {
  playlistId: string
  autoplay?: boolean
  muted?: boolean
  showControls?: boolean
  fluid?: boolean
  aspectRatio?: string
  width?: number | string
  height?: number | string
  showLogo?: boolean
  logoUrl?: string
}

export function SupabaseM3U8Player({
  playlistId,
  autoplay = false,
  muted = false,
  showControls = true,
  fluid = true,
  aspectRatio = "16:9",
  width,
  height,
  showLogo = false,
  logoUrl = "/images/stayonbeat.png",
}: SupabaseM3U8PlayerProps) {
  const [playlistItems, setPlaylistItems] = useState<PlaylistItem[]>([])
  const [m3u8BlobUrl, setM3U8BlobUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0)

  // Load playlist items and generate M3U8
  const loadPlaylist = useCallback(async () => {
    if (!playlistId) return

    setLoading(true)
    try {
      // Get playlist items from Supabase
      const items = await getPlaylistItemsFromSupabase(playlistId)
      setPlaylistItems(items)

      if (items.length === 0) {
        setError("Playlist is empty")
        setLoading(false)
        return
      }

      // Get playlist details
      const { data: playlistData } = await supabase.from("playlists").select("name").eq("id", playlistId).single()

      // Generate M3U8 content
      const content = createM3U8FromItems(items, playlistData?.name || "Playlist")

      // Create blob URL for the player
      if (m3u8BlobUrl) {
        URL.revokeObjectURL(m3u8BlobUrl)
      }

      const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })
      const url = URL.createObjectURL(blob)
      setM3U8BlobUrl(url)

      setError(null)
    } catch (err) {
      console.error("Error loading playlist:", err)
      setError("Failed to load playlist")
    } finally {
      setLoading(false)
    }
  }, [playlistId, m3u8BlobUrl])

  // Load playlist on mount and when playlistId changes
  useEffect(() => {
    loadPlaylist()

    // Clean up blob URL when component unmounts
    return () => {
      if (m3u8BlobUrl) {
        URL.revokeObjectURL(m3u8BlobUrl)
      }
    }
  }, [playlistId, loadPlaylist])

  // Handle track change
  const handleTrackChange = (index: number) => {
    setCurrentTrackIndex(index)
  }

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>
  }

  if (playlistItems.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
        This playlist is empty.
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <HLSPlayer
        playlist={playlistItems}
        currentIndex={currentTrackIndex}
        showControls={showControls}
        autoplay={autoplay}
        muted={muted}
        fluid={fluid}
        aspectRatio={aspectRatio}
        width={width}
        height={height}
        onTrackChange={handleTrackChange}
        showLogo={showLogo}
        logoUrl={logoUrl}
      />

      <div className="text-sm text-gray-500">
        Playing: {playlistItems[currentTrackIndex]?.songTitle} by {playlistItems[currentTrackIndex]?.artistName}
      </div>
    </div>
  )
}
