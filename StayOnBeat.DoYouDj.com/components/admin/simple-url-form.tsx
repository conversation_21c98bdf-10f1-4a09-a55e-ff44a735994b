"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { supabase } from "@/lib/supabase"
import { toast } from "sonner"

export function SimpleUrlForm() {
  const [url, setUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!url.trim()) {
      toast.error("Please enter a URL")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Insert the URL directly into the playlist table
      const { data, error } = await supabase
        .from("playlist")
        .insert({
          url: url,
          artist_name: "From URL",
          song_title: "New Submission",
          position: 0, // Will be first in playlist
          type: "submission"
        })
      
      if (error) throw error
      
      toast.success("URL added to playlist")
      setUrl("") // Clear the input
    } catch (error) {
      console.error("Error submitting URL:", error)
      toast.error("Failed to add URL to playlist")
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="flex space-x-2">
      <Input
        placeholder="Enter URL"
        value={url}
        onChange={(e) => setUrl(e.target.value)}
        className="flex-1"
      />
      <Button 
        type="submit" 
        disabled={isSubmitting}
      >
        {isSubmitting ? "Adding..." : "Add URL"}
      </Button>
    </form>
  )
}
