"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { fetchAllPlaylists, downloadM3U8FromSupabase } from "@/lib/m3u8-generator"
import { Download, Play, RefreshCw } from "lucide-react"
import Link from "next/link"

export function PlaylistM3U8Tools() {
  const [playlists, setPlaylists] = useState<any[]>([])
  const [selectedPlaylistId, setSelectedPlaylistId] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)

  const loadPlaylists = async () => {
    setIsLoading(true)
    const data = await fetchAllPlaylists()
    setPlaylists(data)
    setIsLoading(false)
  }

  useEffect(() => {
    loadPlaylists()
  }, [])

  const handleDownload = () => {
    if (selectedPlaylistId) {
      downloadM3U8FromSupabase(selectedPlaylistId)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>M3U8 Playlist Tools</CardTitle>
        <CardDescription>Generate and download M3U8 playlists from your Supabase data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Select value={selectedPlaylistId} onValueChange={setSelectedPlaylistId}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a playlist" />
              </SelectTrigger>
              <SelectContent>
                {playlists.map((playlist) => (
                  <SelectItem key={playlist.id} value={playlist.id}>
                    {playlist.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon" onClick={loadPlaylists} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleDownload} disabled={!selectedPlaylistId}>
          <Download className="h-4 w-4 mr-2" />
          Download M3U8
        </Button>

        {selectedPlaylistId && (
          <Link href={`/player/supabase/${selectedPlaylistId}`}>
            <Button>
              <Play className="h-4 w-4 mr-2" />
              Play Playlist
            </Button>
          </Link>
        )}
      </CardFooter>
    </Card>
  )
}
