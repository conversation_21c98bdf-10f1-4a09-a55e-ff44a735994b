"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Music } from "lucide-react"

interface LoginFormProps {
  onLogin: (isAuthenticated: boolean) => void
}

export function LoginForm({ onLogin }: LoginFormProps) {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // For this demo, we'll use a simple hardcoded check
      // In a real app, you would validate against a server
      if (username === "admin" && password === "admin123") {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Set authentication in localStorage
        localStorage.setItem("isAdminAuthenticated", "true")
        
        toast({
          title: "Login Successful",
          description: "Welcome to the admin dashboard",
        })
        
        onLogin(true)
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid username or password",
          variant: "destructive",
        })
        onLogin(false)
      }
    } catch (error) {
      console.error("Login error:", error)
      toast({
        title: "Login Error",
        description: "An error occurred during login",
        variant: "destructive",
      })
      onLogin(false)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-black/80 border border-purple-500/30 rounded-lg shadow-[0_0_15px_rgba(147,51,234,0.2)]">
      <div className="flex items-center justify-center mb-6">
        <Music className="h-8 w-8 text-purple-500 mr-2" />
        <h1 className="text-2xl font-bold text-white">Admin Login</h1>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="username" className="text-white">Username</Label>
          <Input
            id="username"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Enter your username"
            className="bg-black/50 border-purple-500/30 text-white"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="password" className="text-white">Password</Label>
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            className="bg-black/50 border-purple-500/30 text-white"
            required
          />
        </div>
        
        <Button
          type="submit"
          className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white border-none"
          disabled={isLoading}
        >
          {isLoading ? "Logging in..." : "Login"}
        </Button>
        
        <div className="text-center mt-4">
          <p className="text-sm text-gray-400">
            Use username: <span className="text-purple-400">admin</span> and password: <span className="text-purple-400">admin123</span>
          </p>
        </div>
      </form>
    </div>
  )
}
