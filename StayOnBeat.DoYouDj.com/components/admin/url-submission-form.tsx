"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { supabase } from "@/lib/supabase"
import { Loader2 } from "lucide-react"

interface UrlSubmissionFormProps {
  onSuccess?: () => void
  className?: string
}

export function UrlSubmissionForm({ onSuccess, className = "" }: UrlSubmissionFormProps) {
  const [artistName, setArtistName] = useState("")
  const [songTitle, setSongTitle] = useState("")
  const [mediaUrl, setMediaUrl] = useState("")
  const [platform, setPlatform] = useState("other")
  const [submissionType, setSubmissionType] = useState("Free")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!artistName || !songTitle || !mediaUrl) {
      toast.error("Please fill in all required fields")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Insert into Supabase submissions table
      const { data, error } = await supabase
        .from("submissions")
        .insert({
          artist_name: artistName,
          song_title: songTitle,
          submission_type: submissionType,
          status: "confirmed", // Auto-confirm admin submissions
          platform: platform,
          url: mediaUrl,
          media_url: mediaUrl,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
      
      if (error) throw error
      
      toast.success("URL submitted successfully")
      
      // Reset form
      setArtistName("")
      setSongTitle("")
      setMediaUrl("")
      setPlatform("other")
      setSubmissionType("Free")
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error submitting URL:", error)
      toast.error("Failed to submit URL")
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className={`url-submission-form ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="artistName">Artist Name</Label>
          <Input
            id="artistName"
            value={artistName}
            onChange={(e) => setArtistName(e.target.value)}
            placeholder="Enter artist name"
            required
            className="bg-black/40 border-purple-500/30 focus:border-purple-500"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="songTitle">Song Title</Label>
          <Input
            id="songTitle"
            value={songTitle}
            onChange={(e) => setSongTitle(e.target.value)}
            placeholder="Enter song title"
            required
            className="bg-black/40 border-purple-500/30 focus:border-purple-500"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="mediaUrl">Media URL</Label>
          <Input
            id="mediaUrl"
            value={mediaUrl}
            onChange={(e) => setMediaUrl(e.target.value)}
            placeholder="Enter media URL (HLS stream or direct media link)"
            required
            className="bg-black/40 border-purple-500/30 focus:border-purple-500"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Select value={platform} onValueChange={setPlatform}>
              <SelectTrigger id="platform" className="bg-black/40 border-purple-500/30">
                <SelectValue placeholder="Select platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="youtube">YouTube</SelectItem>
                <SelectItem value="soundcloud">SoundCloud</SelectItem>
                <SelectItem value="spotify">Spotify</SelectItem>
                <SelectItem value="bandcamp">Bandcamp</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="submissionType">Submission Type</Label>
            <Select value={submissionType} onValueChange={setSubmissionType}>
              <SelectTrigger id="submissionType" className="bg-black/40 border-purple-500/30">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIP">VIP</SelectItem>
                <SelectItem value="Skip">Skip</SelectItem>
                <SelectItem value="GA">GA</SelectItem>
                <SelectItem value="Free">Free</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            "Submit URL"
          )}
        </Button>
      </form>
    </div>
  )
}
