"use client"

import { useState } from "react"
import { Save, Lock, Globe, Users } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"

interface PlaylistFinalizerIconsProps {
  playedTracks: any[]
  onFinalize: () => void
}

export function PlaylistFinalizerIcons({ playedTracks, onFinalize }: PlaylistFinalizerIconsProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [visibility, setVisibility] = useState<"admin" | "private" | "public">("admin")

  const handleFinalize = async () => {
    if (playedTracks.length === 0) {
      toast({
        title: "No tracks to save",
        description: "There are no played tracks to finalize into a playlist.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      const playlistName = `Show - ${new Date().toLocaleDateString()}`

      // Create a new playlist entry
      const { data: playlist, error: playlistError } = await supabase
        .from("playlists")
        .insert({
          name: playlistName,
          visibility: visibility,
          created_at: new Date().toISOString(),
          track_count: playedTracks.length,
          is_finalized: true,
        })
        .select()
        .single()

      if (playlistError) throw playlistError

      // Add all played tracks to the playlist
      const playlistItems = playedTracks.map((track, index) => ({
        playlist_id: playlist.id,
        track_id: track.id,
        position: index + 1,
        artist_name: track.artistName,
        song_title: track.songTitle,
        url: track.url,
        platform: track.platform,
      }))

      const { error: itemsError } = await supabase.from("playlist_items").insert(playlistItems)

      if (itemsError) throw itemsError

      // Generate and save M3U8 content
      const m3u8Content = generateM3U8FromTracks(playedTracks)

      const { error: m3u8Error } = await supabase.from("playlist_files").insert({
        playlist_id: playlist.id,
        content: m3u8Content,
        format: "m3u8",
        created_at: new Date().toISOString(),
      })

      if (m3u8Error) throw m3u8Error

      toast({
        title: "Playlist saved",
        description: `Playlist has been saved successfully.`,
      })

      onFinalize()
    } catch (error) {
      console.error("Error finalizing playlist:", error)
      toast({
        title: "Error saving playlist",
        description: "There was a problem finalizing the playlist. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Helper function to generate M3U8 content
  const generateM3U8FromTracks = (tracks: any[]) => {
    let content = "#EXTM3U\n"

    tracks.forEach((track, index) => {
      content += `#EXTINF:-1,${track.artistName} - ${track.songTitle}\n`
      content += `${track.url}\n`
    })

    return content
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="flex space-x-2">
        <button
          type="button"
          onClick={() => setVisibility("admin")}
          className={`h-6 w-6 rounded-full flex items-center justify-center transition-colors ${
            visibility === "admin" ? "bg-purple-600 text-white" : "bg-black/20 text-gray-400 hover:text-gray-200"
          }`}
          title="Admin Only"
        >
          <Lock className="h-3 w-3" />
        </button>

        <button
          type="button"
          onClick={() => setVisibility("private")}
          className={`h-6 w-6 rounded-full flex items-center justify-center transition-colors ${
            visibility === "private" ? "bg-purple-600 text-white" : "bg-black/20 text-gray-400 hover:text-gray-200"
          }`}
          title="Private"
        >
          <Users className="h-3 w-3" />
        </button>

        <button
          type="button"
          onClick={() => setVisibility("public")}
          className={`h-6 w-6 rounded-full flex items-center justify-center transition-colors ${
            visibility === "public" ? "bg-purple-600 text-white" : "bg-black/20 text-gray-400 hover:text-gray-200"
          }`}
          title="Public"
        >
          <Globe className="h-3 w-3" />
        </button>
      </div>

      <button
        type="button"
        onClick={handleFinalize}
        disabled={isProcessing || playedTracks.length === 0}
        className={`h-8 w-8 rounded-full flex items-center justify-center transition-colors ${
          isProcessing || playedTracks.length === 0
            ? "bg-gray-700 text-gray-500 cursor-not-allowed"
            : "bg-purple-600 text-white hover:bg-purple-700"
        }`}
        title="Save Playlist"
      >
        <Save className="h-4 w-4" />
      </button>
    </div>
  )
}
