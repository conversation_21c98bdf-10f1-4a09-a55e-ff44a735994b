"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { supabase } from "@/lib/supabase"
import { toast } from "sonner"

export function SimpleSubmissionForm() {
  const [url, setUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!url.trim()) {
      toast.error("Please enter a URL")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Insert the URL into the submissions table
      const { data, error } = await supabase
        .from("submissions")
        .insert({
          url: url,
          media_url: url,
          artist_name: "From URL",
          song_title: "New Submission",
          submission_type: "Free",
          status: "pending",
          created_at: new Date().toISOString()
        })
      
      if (error) throw error
      
      toast.success("URL submitted successfully")
      setUrl("") // Clear the input
    } catch (error) {
      console.error("Error submitting URL:", error)
      toast.error("Failed to submit URL")
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="p-4 bg-black/50 rounded-lg border border-purple-500/30">
      <h3 className="text-lg font-medium mb-2">Quick URL Submission</h3>
      <form onSubmit={handleSubmit} className="flex space-x-2">
        <Input
          placeholder="Enter URL"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          className="flex-1 bg-black/40 border-purple-500/30"
        />
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white"
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </form>
    </div>
  )
}
