"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Save, Lock, Globe, Users } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { supabase } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"

interface PlaylistFinalizerProps {
  playedTracks: any[]
  onFinalize: () => void
}

export function PlaylistFinalizer({ playedTracks, onFinalize }: PlaylistFinalizerProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [visibility, setVisibility] = useState<"admin" | "private" | "public">("admin")
  const [playlistName, setPlaylistName] = useState(`Show - ${new Date().toLocaleDateString()}`)

  const handleFinalize = async () => {
    if (playedTracks.length === 0) {
      toast({
        title: "No tracks to save",
        description: "There are no played tracks to finalize into a playlist.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Create a new playlist entry
      const { data: playlist, error: playlistError } = await supabase
        .from("playlists")
        .insert({
          name: playlistName,
          visibility: visibility,
          created_at: new Date().toISOString(),
          track_count: playedTracks.length,
          is_finalized: true,
        })
        .select()
        .single()

      if (playlistError) throw playlistError

      // Add all played tracks to the playlist
      const playlistItems = playedTracks.map((track, index) => ({
        playlist_id: playlist.id,
        track_id: track.id,
        position: index + 1,
        artist_name: track.artistName,
        song_title: track.songTitle,
        url: track.url,
        platform: track.platform,
      }))

      const { error: itemsError } = await supabase.from("playlist_items").insert(playlistItems)

      if (itemsError) throw itemsError

      // Generate and save M3U8 content
      const m3u8Content = generateM3U8FromTracks(playedTracks)

      const { error: m3u8Error } = await supabase.from("playlist_files").insert({
        playlist_id: playlist.id,
        content: m3u8Content,
        format: "m3u8",
        created_at: new Date().toISOString(),
      })

      if (m3u8Error) throw m3u8Error

      toast({
        title: "Playlist finalized",
        description: `"${playlistName}" has been saved with ${playedTracks.length} tracks.`,
      })

      onFinalize()
    } catch (error) {
      console.error("Error finalizing playlist:", error)
      toast({
        title: "Error saving playlist",
        description: "There was a problem finalizing the playlist. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Helper function to generate M3U8 content
  const generateM3U8FromTracks = (tracks: any[]) => {
    let content = "#EXTM3U\n"

    tracks.forEach((track, index) => {
      content += `#EXTINF:-1,${track.artistName} - ${track.songTitle}\n`
      content += `${track.url}\n`
    })

    return content
  }

  return (
    <div className="bg-black/80 border border-purple-500/30 rounded-lg p-4 mb-6">
      <h3 className="text-lg font-medium mb-4 flex items-center">
        <Save className="mr-2 h-5 w-5 text-purple-500" />
        Finalize Playlist
      </h3>

      <div className="space-y-4">
        <div>
          <Label htmlFor="playlist-name">Playlist Name</Label>
          <input
            id="playlist-name"
            type="text"
            value={playlistName}
            onChange={(e) => setPlaylistName(e.target.value)}
            className="w-full p-2 bg-black border border-purple-500/30 rounded-md mt-1"
          />
        </div>

        <div>
          <Label>Visibility</Label>
          <RadioGroup
            value={visibility}
            onValueChange={(value) => setVisibility(value as any)}
            className="flex space-x-4 mt-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="admin" id="admin" />
              <Label htmlFor="admin" className="flex items-center">
                <Lock className="h-4 w-4 mr-1" /> Admin Only
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="private" id="private" />
              <Label htmlFor="private" className="flex items-center">
                <Users className="h-4 w-4 mr-1" /> Private
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="public" id="public" />
              <Label htmlFor="public" className="flex items-center">
                <Globe className="h-4 w-4 mr-1" /> Public
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-400">{playedTracks.length} tracks will be saved</div>
          <Button
            onClick={handleFinalize}
            disabled={isProcessing || playedTracks.length === 0}
            className="bg-gradient-to-r from-purple-600 to-indigo-600"
          >
            {isProcessing ? (
              <>Processing...</>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Finalized Playlist
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
