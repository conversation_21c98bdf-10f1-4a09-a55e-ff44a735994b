"use client"

import { useState } from "react"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

export function MinimalUrlForm() {
  const [url, setUrl] = useState("")
  const [urls, setUrls] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!url.trim()) return

    setIsSubmitting(true)

    try {
      // Insert the URL into the submissions table
      const { error } = await supabase
        .from("submissions")
        .insert({
          url: url,
          media_url: url,
          artist_name: "From URL",
          song_title: "New Submission",
          submission_type: "Free",
          status: "confirmed",
          created_at: new Date().toISOString()
        })

      if (error) throw error

      setUrl("")
      toast({
        title: "Success",
        description: "URL submitted successfully",
      })
    } catch (error) {
      console.error("Error submitting URL:", error)
      toast({
        title: "Error",
        description: "Failed to submit URL",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleBulkSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!urls.trim()) return

    setIsBulkSubmitting(true)

    try {
      // Split URLs by newlines and filter out empty lines
      const urlList = urls.split('\n').filter(url => url.trim())

      if (urlList.length === 0) {
        toast({
          title: "Error",
          description: "Please enter at least one URL",
          variant: "destructive",
        })
        return
      }

      // Create today's date for playlist name
      const today = new Date()
      const dateStr = today.toISOString().split('T')[0] // YYYY-MM-DD format
      const playlistName = `${dateStr} Episode#Test001`

      // Prepare submissions data
      const submissionsData = urlList.map((url, index) => ({
        url: url.trim(),
        media_url: url.trim(),
        artist_name: `Test Artist ${index + 1}`,
        song_title: `Test Track ${index + 1}`,
        submission_type: "Free",
        status: "confirmed",
        created_at: new Date().toISOString()
      }))

      // Insert all URLs into submissions table
      const { data: submissionResults, error: submissionError } = await supabase
        .from("submissions")
        .insert(submissionsData)
        .select("id, url")

      if (submissionError) throw submissionError

      // Add to playlist table directly (using the existing playlist table structure)
      const playlistData = submissionResults.map((submission, index) => ({
        submission_id: submission.id,
        position: index + 1,
        artist_name: `Test Artist ${index + 1}`,
        song_title: `Test Track ${index + 1}`,
        type: "Free",
        url: submission.url,
        media_url: submission.url,
        created_at: new Date().toISOString()
      }))

      const { error: playlistInsertError } = await supabase
        .from("playlist")
        .insert(playlistData)

      if (playlistInsertError) throw playlistInsertError

      setUrls("")
      toast({
        title: "Success",
        description: `Successfully submitted ${urlList.length} URLs to playlist "${playlistName}"`,
      })

      // Also create a sample file entry to simulate file upload
      const sampleFileData = {
        url: "https://example.com/sample-file.mp3",
        media_url: "https://example.com/sample-file.mp3",
        artist_name: "Sample File Artist",
        song_title: "Sample Uploaded Track",
        submission_type: "Free",
        status: "confirmed",
        created_at: new Date().toISOString()
      }

      const { data: fileSubmission, error: fileError } = await supabase
        .from("submissions")
        .insert(sampleFileData)
        .select("id")
        .single()

      if (!fileError && fileSubmission) {
        // Add sample file to playlist
        await supabase
          .from("playlist")
          .insert({
            submission_id: fileSubmission.id,
            position: urlList.length + 1,
            artist_name: "Sample File Artist",
            song_title: "Sample Uploaded Track",
            type: "Free",
            url: "https://example.com/sample-file.mp3",
            media_url: "https://example.com/sample-file.mp3",
            created_at: new Date().toISOString()
          })
      }

    } catch (error) {
      console.error("Error submitting URLs:", error)
      toast({
        title: "Error",
        description: "Failed to submit URLs",
        variant: "destructive",
      })
    } finally {
      setIsBulkSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Single URL Form */}
      <div>
        <h3 className="text-lg font-semibold text-purple-400 mb-2">Single URL Submission</h3>
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <Input
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="Enter URL (YouTube, SoundCloud, etc.)"
            className="flex-1 bg-black/40 border-purple-500/30 text-white"
          />
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white"
          >
            {isSubmitting ? "Submitting..." : "Submit"}
          </Button>
        </form>
      </div>

      {/* Bulk URL Form */}
      <div>
        <h3 className="text-lg font-semibold text-purple-400 mb-2">Bulk URL Submission</h3>
        <p className="text-sm text-gray-400 mb-2">
          Enter multiple URLs (one per line) to create a playlist for today's date with Episode#Test001
        </p>
        <form onSubmit={handleBulkSubmit} className="space-y-2">
          <Textarea
            value={urls}
            onChange={(e) => setUrls(e.target.value)}
            placeholder="Enter URLs, one per line:
https://on.soundcloud.com/dvpjnwPokiHuvvtG9
https://music.youtube.com/watch?v=7vj1S9qqq7o&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW
https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB?si=14727a84eb3b4185
https://music.youtube.com/watch?v=dbx8Yf9U3qk&list=PLnbt_MKSC8tj1ErQeSVPO6Gh35dnHRJzW"
            className="w-full h-32 bg-black/40 border-purple-500/30 text-white"
            rows={6}
          />
          <Button
            type="submit"
            disabled={isBulkSubmitting}
            className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white"
          >
            {isBulkSubmitting ? "Creating Playlist..." : "Create Test Playlist with URLs"}
          </Button>
        </form>
      </div>
    </div>
  )
}
