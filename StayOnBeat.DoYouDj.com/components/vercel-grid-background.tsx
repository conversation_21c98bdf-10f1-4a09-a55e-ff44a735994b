"use client"

import { useEffect, useRef } from "react"

export default function VercelGridBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const updateCanvasSize = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    updateCanvasSize()
    window.addEventListener("resize", updateCanvasSize)

    // Animation variables
    let time = 0
    let animationFrame: number

    // Draw function
    const draw = () => {
      // Clear canvas
      ctx.fillStyle = "#000000"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Calculate grid dimensions
      const dotSpacing = 25
      const cols = Math.ceil(canvas.width / dotSpacing) + 2
      const rows = Math.ceil(canvas.height / dotSpacing) + 2

      // Offset for animation
      const offsetX = Math.sin(time * 0.2) * 5
      const offsetY = Math.cos(time * 0.3) * 5

      // Draw dots
      ctx.fillStyle = "rgba(0, 255, 255, 0.3)"

      for (let i = -1; i < cols; i++) {
        for (let j = -1; j < rows; j++) {
          const x = i * dotSpacing + offsetX * Math.sin(j * 0.1) * 0.5
          const y = j * dotSpacing + offsetY * Math.cos(i * 0.1) * 0.5

          ctx.beginPath()
          ctx.arc(x, y, 1.2, 0, Math.PI * 2)
          ctx.fill()
        }
      }

      // Update time
      time += 0.3 * 0.01

      // Continue animation
      animationFrame = requestAnimationFrame(draw)
    }

    // Start animation
    draw()

    // Cleanup
    return () => {
      window.removeEventListener("resize", updateCanvasSize)
      cancelAnimationFrame(animationFrame)
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className="fixed top-0 left-0 w-full h-full"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: -1,
        background: "#000000",
      }}
    />
  )
}
