'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

// Define the pages to cycle through
const pages = [
  { path: "/", label: "Home" },
  { path: "/queue", label: "Queue" },
  { path: "/playlists", label: "Playlists" },
  { path: "/player", label: "Player" },
  { path: "/submit", label: "Submit" },
  { path: "/profile", label: "Profile" },
];

export function FixedNavigationDock() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const router = useRouter();

  // Add a check for admin pages in the component:
  const isAdminPage = pathname?.startsWith('/admin');

  // Find current page index
  const getCurrentPageIndex = () => {
    const index = pages.findIndex((page) => page.path === pathname);
    return index >= 0 ? index : 0; // Default to home if not found
  };

  // Navigate to the next page in the cycle
  const navigateNext = () => {
    // Set direction in localStorage for PageTransition component
    localStorage.setItem("navigationDirection", "right");

    const currentIndex = getCurrentPageIndex();
    const nextIndex = (currentIndex + 1) % pages.length;

    // Use router.push for client-side navigation without full page reload
    router.push(pages[nextIndex].path);
  };

  // Navigate to the previous page in the cycle
  const navigatePrev = () => {
    // Set direction in localStorage for PageTransition component
    localStorage.setItem("navigationDirection", "left");

    const currentIndex = getCurrentPageIndex();
    const prevIndex = (currentIndex - 1 + pages.length) % pages.length;

    // Use router.push for client-side navigation without full page reload
    router.push(pages[prevIndex].path);
  };

  if (isAdminPage) {
    return null;
  }

  return (
    <div className="fixed top-[145px] left-0 right-0 z-50 flex justify-center items-center pointer-events-none">
      <div className="pointer-events-auto flex items-center justify-center gap-5 bg-black/90 backdrop-blur-md rounded-full px-6 py-3 border-2 border-red-600 neon-box-red"
      style={{
        boxShadow: "0 0 5px rgba(255, 0, 0, 1), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 15px rgba(255, 0, 0, 0.8), 0 0 20px rgba(255, 0, 0, 0.7)"
      }}>
        {/* Previous button */}
        <button
          className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 transition-all duration-300"
          style={{
            background: "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
            boxShadow: "inset 0 0 15px rgba(255, 0, 0, 0.6), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 20px rgba(255, 0, 0, 0.7)",
            backdropFilter: "blur(4px)",
            transform: "translateZ(0)",
            width: "40px",
            height: "40px",
          }}
          onClick={navigatePrev}
        >
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
              transform: "translateZ(5px)",
            }}
          ></div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white neon-text-red"
          >
            <path d="m11 17-5-5 5-5"></path>
            <path d="m18 17-5-5 5-5"></path>
          </svg>
          <span className="sr-only">Previous</span>
        </button>

        {/* Logo (middle) */}
        <Link
          href="/"
          className="relative"
          style={{ padding: "2px" }}
          onClick={() => localStorage.setItem("navigationDirection", "right")}
        >
          <div className="relative">
            <img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/stayonbeat-RidfPDW2Ft60rFWdaZTQeIWaDD0sod.png"
              alt="STAYONBEAT"
              className="logo-small w-auto"
              style={{ height: "35.2px", width: "auto" }}
            />
          </div>
        </Link>

        {/* Next button */}
        <button
          className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-600 transition-all duration-300"
          style={{
            background: "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
            boxShadow: "inset 0 0 15px rgba(255, 0, 0, 0.6), 0 0 10px rgba(255, 0, 0, 0.9), 0 0 20px rgba(255, 0, 0, 0.7)",
            backdropFilter: "blur(4px)",
            transform: "translateZ(0)",
            width: "40px",
            height: "40px",
          }}
          onClick={navigateNext}
        >
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
              transform: "translateZ(5px)",
            }}
          ></div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white neon-text-red"
            style={{ transform: "rotate(180deg)" }}
          >
            <path d="m11 17-5-5 5-5"></path>
            <path d="m18 17-5-5 5-5"></path>
          </svg>
          <span className="sr-only">Next</span>
        </button>
      </div>
    </div>
  );
}
