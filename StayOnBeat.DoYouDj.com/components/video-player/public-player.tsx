"use client"

import { useEffect } from "react"
import { HLSPlayer } from "./hls-player"
import { useHLSPlayer } from "./use-hls-player"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Play, Pause, Volume2 } from "lucide-react"
import type { PlaylistItem } from "@/lib/types"

interface PublicPlayerProps {
  initialPlaylist?: PlaylistItem[]
  showLogo?: boolean
  onTrackChange?: (track: PlaylistItem) => void
  className?: string
}

export function PublicPlayer({
  initialPlaylist = [],
  showLogo = true,
  onTrackChange,
  className = "",
}: PublicPlayerProps) {
  const {
    playlist,
    currentIndex,
    isPlaying,
    currentTime,
    duration,
    volume,
    formatTime,
    handleTimeUpdate,
    handleTrackChange,
    togglePlay,
    seekTo,
    updateVolume,
    currentTrack,
  } = useHLSPlayer(initialPlaylist)

  // Notify parent component when track changes
  useEffect(() => {
    if (currentTrack && onTrackChange) {
      onTrackChange(currentTrack)
    }
  }, [currentTrack, onTrackChange])

  return (
    <div className={`public-player ${className}`}>
      <div className="player-container">
        <div className="video-container aspect-video bg-black rounded-md overflow-hidden">
          <HLSPlayer
            playlist={playlist}
            currentIndex={currentIndex}
            showControls={false}
            onTimeUpdate={handleTimeUpdate}
            onTrackChange={handleTrackChange}
            showLogo={showLogo}
          />

          {/* Overlay with play button when paused */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-white text-center">
                <div className="mb-4">
                  <button
                    onClick={togglePlay}
                    className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-black border border-[#00ffff]/50 hover:bg-black/60 transition-all duration-300 shadow-[0_0_10px_rgba(0,255,255,0.5)]"
                  >
                    <Play className="h-6 w-6 text-white" />
                  </button>
                </div>
                <h2 className="text-2xl font-bold neon-text-cyan">Now Playing</h2>
                <p className="text-xl mt-2 neon-text-cyan">
                  {currentTrack ? `${currentTrack.songTitle} - ${currentTrack.artistName}` : "No track selected"}
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="custom-controls mt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-white neon-text-cyan">{formatTime(currentTime)}</span>
            <span className="text-sm text-white neon-text-cyan">{formatTime(duration)}</span>
          </div>

          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={1}
            onValueChange={(value) => seekTo(value[0])}
            className="mb-4"
          />

          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2 w-48">
              <Button size="icon" variant="ghost" className="w-9 h-9 hover:bg-black/50" onClick={togglePlay}>
                {isPlaying ? <Pause className="w-6 h-6 fill-white" /> : <Play className="w-6 h-6 fill-white" />}
              </Button>

              <Volume2 className="h-4 w-4 text-[#00ffff]" />
              <Slider value={[volume]} max={100} step={1} onValueChange={(value) => updateVolume(value[0])} />
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .public-player {
          width: 100%;
        }
        .player-container {
          position: relative;
        }
        .video-container {
          position: relative;
          width: 100%;
        }
      `}</style>
    </div>
  )
}
