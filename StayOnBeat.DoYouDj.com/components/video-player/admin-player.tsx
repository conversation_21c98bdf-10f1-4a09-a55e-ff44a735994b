"use client"

import { useEffect, useRef } from "react"
import { HLSPlayer } from "./hls-player"
import { useHLSPlayer } from "./use-hls-player"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Play, Pause, SkipForward, SkipBack, Volume2, RefreshCw } from "lucide-react"
import { downloadM3U8 } from "./playlist-generator"
import type { PlaylistItem } from "@/lib/types"

interface AdminPlayerProps {
  initialPlaylist?: PlaylistItem[]
  showLogo?: boolean
  onTrackChange?: (track: PlaylistItem) => void
  className?: string
}

export function AdminPlayer({
  initialPlaylist = [],
  showLogo = true,
  onTrackChange,
  className = "",
}: AdminPlayerProps) {
  const playerRef = useRef<HTMLDivElement>(null)

  const {
    playlist,
    currentIndex,
    isPlaying,
    currentTime,
    duration,
    volume,
    formatTime,
    handleTimeUpdate,
    handleTrackChange,
    togglePlay,
    playNext,
    playPrevious,
    seekTo,
    updateVolume,
    currentTrack,
    reorderPlaylist,
  } = useHLSPlayer(initialPlaylist)

  // Notify parent component when track changes
  useEffect(() => {
    if (currentTrack && onTrackChange) {
      onTrackChange(currentTrack)
    }
  }, [currentTrack, onTrackChange])

  // Download current playlist as M3U8
  const handleDownloadPlaylist = () => {
    downloadM3U8(playlist, "stayonbeat-playlist.m3u8", "STAYONBEAT Playlist")
  }

  return (
    <div ref={playerRef} className={`admin-player ${className}`}>
      <div className="player-container">
        <div className="video-container aspect-video bg-black rounded-md overflow-hidden">
          <HLSPlayer
            playlist={playlist}
            currentIndex={currentIndex}
            showControls={false}
            onTimeUpdate={handleTimeUpdate}
            onTrackChange={handleTrackChange}
            showLogo={showLogo}
          />
        </div>

        <div className="custom-controls mt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-white">{formatTime(currentTime)}</span>
            <span className="text-sm text-white">{formatTime(duration)}</span>
          </div>

          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={1}
            onValueChange={(value) => seekTo(value[0])}
            className="mb-4"
          />

          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center space-x-4">
              <Button
                onClick={playPrevious}
                className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white border-2 border-purple-600/80 shadow-[0_0_10px_rgba(147,51,234,0.5)] hover:shadow-[0_0_15px_rgba(147,51,234,0.7)] transition-shadow duration-300"
                disabled={currentIndex === 0}
              >
                <SkipBack className="h-5 w-5" />
              </Button>

              <Button
                onClick={togglePlay}
                className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white border-2 border-purple-600/80 shadow-[0_0_10px_rgba(147,51,234,0.5)] hover:shadow-[0_0_15px_rgba(147,51,234,0.7)] transition-shadow duration-300"
              >
                {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
              </Button>

              <Button
                onClick={playNext}
                className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center text-white border-2 border-purple-600/80 shadow-[0_0_10px_rgba(147,51,234,0.5)] hover:shadow-[0_0_15px_rgba(147,51,234,0.7)] transition-shadow duration-300"
                disabled={currentIndex === playlist.length - 1}
              >
                <SkipForward className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2 w-64">
              <Volume2 className="h-4 w-4 text-white" />
              <Slider value={[volume]} max={100} step={1} onValueChange={(value) => updateVolume(value[0])} />
              <span className="text-sm text-white w-8 text-center">{volume}</span>
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPlaylist}
              className="text-purple-600 border-purple-600/50 hover:bg-purple-600/10"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Download M3U8
            </Button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .admin-player {
          width: 100%;
        }
        .player-container {
          position: relative;
        }
        .video-container {
          position: relative;
          width: 100%;
        }
      `}</style>
    </div>
  )
}
