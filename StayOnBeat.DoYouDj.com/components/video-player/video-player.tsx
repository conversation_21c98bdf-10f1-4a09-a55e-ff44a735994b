"use client"

import { useEffect, useRef, useState } from "react"
import videojs from "video.js"
import "video.js/dist/video-js.css"
import type Player from "video.js/dist/types/player"
import type { VideoJsPlayerOptions } from "video.js"

interface VideoPlayerProps {
  options: VideoJsPlayerOptions
  onReady?: (player: Player) => void
  onTimeUpdate?: (currentTime: number, duration: number) => void
  onEnded?: () => void
}

export const VideoPlayer = ({ options, onReady, onTimeUpdate, onEnded }: VideoPlayerProps) => {
  const videoRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<Player | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      if (!videoRef.current) return

      const videoElement = document.createElement("video-js")
      videoElement.classList.add("vjs-big-play-centered")
      videoRef.current.appendChild(videoElement)

      const player = (playerRef.current = videojs(videoElement, options, () => {
        console.log("Player is ready")

        // Add event listeners
        player.on("timeupdate", () => {
          if (onTimeUpdate) {
            onTimeUpdate(player.currentTime(), player.duration())
          }
        })

        player.on("ended", () => {
          if (onEnded) {
            onEnded()
          }
        })

        if (onReady) {
          onReady(player)
        }
      }))
    } else {
      // Update player options if they change
      const player = playerRef.current
      player.autoplay(options.autoplay || false)

      if (options.sources && options.sources.length > 0) {
        player.src(options.sources)
      }
    }
  }, [isClient, options, onReady, onTimeUpdate, onEnded])

  // Dispose the Video.js player when the component unmounts
  useEffect(() => {
    return () => {
      if (playerRef.current) {
        playerRef.current.dispose()
        playerRef.current = null
      }
    }
  }, [])

  return (
    <div data-vjs-player>
      <div ref={videoRef} className="w-full h-full" />
    </div>
  )
}
