"use client"

import { useState, useCallback, useRef } from "react"
import type { PlaylistItem } from "@/lib/types"
import type { PlayerControls } from "./hls-player"

export function useHLSPlayer(initialPlaylist: PlaylistItem[] = []) {
  const [playlist, setPlaylist] = useState<PlaylistItem[]>(initialPlaylist)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(80)
  const playerControlsRef = useRef<PlayerControls | null>(null)

  // Register player controls
  const registerPlayerControls = useCallback((controls: PlayerControls) => {
    playerControlsRef.current = controls
    setIsPlaying(controls.isPlaying)
  }, [])

  // Handle time update
  const handleTimeUpdate = useCallback((time: number, totalDuration: number) => {
    setCurrentTime(time)
    setDuration(totalDuration)
  }, [])

  // Handle track change
  const handleTrackChange = useCallback((index: number) => {
    setCurrentIndex(index)
  }, [])

  // Player control methods
  const playPrevious = useCallback(() => {
    if (playerControlsRef.current) {
      playerControlsRef.current.playPrevious()
    }
  }, [])

  const playNext = useCallback(() => {
    if (playerControlsRef.current) {
      playerControlsRef.current.playNext()
    }
  }, [])

  const togglePlay = useCallback(() => {
    if (playerControlsRef.current) {
      playerControlsRef.current.togglePlay()
      setIsPlaying(!isPlaying)
    }
  }, [isPlaying])

  const seekTo = useCallback((time: number) => {
    if (playerControlsRef.current) {
      playerControlsRef.current.seekTo(time)
    }
  }, [])

  const updateVolume = useCallback((level: number) => {
    if (playerControlsRef.current) {
      playerControlsRef.current.setVolume(level)
    }
    setVolume(level)
  }, [])

  // Playlist management
  const addToPlaylist = useCallback((item: PlaylistItem) => {
    setPlaylist((prev) => [...prev, item])
  }, [])

  const removeFromPlaylist = useCallback(
    (id: number) => {
      setPlaylist((prev) => {
        const newPlaylist = prev.filter((item) => item.id !== id)

        // If we removed the current track, adjust the current index
        if (currentIndex >= newPlaylist.length) {
          setCurrentIndex(Math.max(0, newPlaylist.length - 1))
        }

        return newPlaylist
      })
    },
    [currentIndex],
  )

  const reorderPlaylist = useCallback(
    (newPlaylist: PlaylistItem[]) => {
      // Find the current track in the new playlist
      const currentTrack = playlist[currentIndex]
      const newIndex = newPlaylist.findIndex((item) => item.id === currentTrack.id)

      setPlaylist(newPlaylist)
      if (newIndex !== -1) {
        setCurrentIndex(newIndex)
      }
    },
    [playlist, currentIndex],
  )

  const clearPlaylist = useCallback(() => {
    setPlaylist([])
    setCurrentIndex(0)
    setIsPlaying(false)
  }, [])

  // Format time helper
  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }, [])

  return {
    playlist,
    currentIndex,
    isPlaying,
    currentTime,
    duration,
    volume,
    formatTime,
    registerPlayerControls,
    handleTimeUpdate,
    handleTrackChange,
    playPrevious,
    playNext,
    togglePlay,
    seekTo,
    updateVolume,
    addToPlaylist,
    removeFromPlaylist,
    reorderPlaylist,
    clearPlaylist,
    currentTrack: playlist[currentIndex],
  }
}
