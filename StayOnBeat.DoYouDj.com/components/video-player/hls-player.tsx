"use client"

import { useState, useEffect, useCallback } from "react"
import { VideoPlayer } from "./video-player"
import type Player from "video.js/dist/types/player"
import type { VideoJsPlayerOptions } from "video.js"
import type { PlaylistItem } from "@/lib/types"

interface HLSPlayerProps {
  playlist: PlaylistItem[]
  currentIndex?: number
  showControls?: boolean
  autoplay?: boolean
  muted?: boolean
  fluid?: boolean
  aspectRatio?: string
  width?: number | string
  height?: number | string
  onTimeUpdate?: (currentTime: number, duration: number) => void
  onTrackChange?: (index: number) => void
  showLogo?: boolean
  logoUrl?: string
}

export const HLSPlayer = ({
  playlist,
  currentIndex = 0,
  showControls = true,
  autoplay = false,
  muted = false,
  fluid = true,
  aspectRatio = "16:9",
  width,
  height,
  onTimeUpdate,
  onTrackChange,
  showLogo = false,
  logoUrl = "/images/stayonbeat.png",
}: HLSPlayerProps) => {
  const [player, setPlayer] = useState<Player | null>(null)
  const [activeIndex, setActiveIndex] = useState(currentIndex)
  const [isPlaying, setIsPlaying] = useState(autoplay)

  // Generate VideoJS options
  const videoJsOptions: VideoJsPlayerOptions = {
    autoplay,
    controls: showControls,
    responsive: true,
    fluid,
    aspectRatio,
    muted,
    width,
    height,
    sources:
      playlist.length > 0
        ? [
            {
              src: playlist[activeIndex].mediaUrl || "",
              type: playlist[activeIndex].mediaUrl?.endsWith(".m3u8") ? "application/x-mpegURL" : "video/mp4",
            },
          ]
        : [],
  }

  // Handle player ready
  const handlePlayerReady = useCallback(
    (player: Player) => {
      setPlayer(player)

      // Add logo if needed
      if (showLogo && logoUrl) {
        player.addClass("vjs-has-logo")

        const logoDiv = document.createElement("div")
        logoDiv.classList.add("vjs-logo")
        logoDiv.style.position = "absolute"
        logoDiv.style.top = "10px"
        logoDiv.style.left = "10px"
        logoDiv.style.zIndex = "1"

        const logoImg = document.createElement("img")
        logoImg.src = logoUrl
        logoImg.style.height = "40px"
        logoImg.style.width = "auto"
        logoImg.style.objectFit = "contain"

        logoDiv.appendChild(logoImg)
        player.el().appendChild(logoDiv)
      }
    },
    [showLogo, logoUrl],
  )

  // Handle track ended
  const handleTrackEnded = useCallback(() => {
    if (activeIndex < playlist.length - 1) {
      const nextIndex = activeIndex + 1
      setActiveIndex(nextIndex)
      if (onTrackChange) {
        onTrackChange(nextIndex)
      }
    } else {
      // End of playlist
      setIsPlaying(false)
    }
  }, [activeIndex, playlist, onTrackChange])

  // Update player source when active index changes
  useEffect(() => {
    if (player && playlist.length > 0 && playlist[activeIndex]?.mediaUrl) {
      player.src({
        src: playlist[activeIndex].mediaUrl || "",
        type: playlist[activeIndex].mediaUrl?.endsWith(".m3u8") ? "application/x-mpegURL" : "video/mp4",
      })

      if (isPlaying) {
        player.play().catch((error) => {
          console.error("Playback error:", error)
        })
      }
    }
  }, [player, playlist, activeIndex, isPlaying])

  // Update active index when currentIndex prop changes
  useEffect(() => {
    if (currentIndex !== activeIndex) {
      setActiveIndex(currentIndex)
    }
  }, [currentIndex, activeIndex])

  // Public methods to control the player
  const playPrevious = () => {
    if (activeIndex > 0) {
      const prevIndex = activeIndex - 1
      setActiveIndex(prevIndex)
      if (onTrackChange) {
        onTrackChange(prevIndex)
      }
    }
  }

  const playNext = () => {
    if (activeIndex < playlist.length - 1) {
      const nextIndex = activeIndex + 1
      setActiveIndex(nextIndex)
      if (onTrackChange) {
        onTrackChange(nextIndex)
      }
    }
  }

  const togglePlay = () => {
    if (player) {
      if (player.paused()) {
        player.play()
        setIsPlaying(true)
      } else {
        player.pause()
        setIsPlaying(false)
      }
    }
  }

  const seekTo = (time: number) => {
    if (player) {
      player.currentTime(time)
    }
  }

  const setVolume = (level: number) => {
    if (player) {
      player.volume(level / 100)
    }
  }

  // Expose player controls
  const playerControls = {
    playPrevious,
    playNext,
    togglePlay,
    seekTo,
    setVolume,
    isPlaying,
  }

  return (
    <div className="hls-player-container">
      {/* Slate color angle gradient when no video is playing */}
      {(!playlist.length || !playlist[activeIndex]?.mediaUrl) && (
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-slate-800 to-slate-900 rounded-md"></div>
      )}

      <VideoPlayer
        options={videoJsOptions}
        onReady={handlePlayerReady}
        onTimeUpdate={onTimeUpdate}
        onEnded={handleTrackEnded}
      />

      {/* Progress bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/50 z-10">
        <div
          className="h-full bg-purple-500"
          style={{
            width: player ? `${(player.currentTime() / (player.duration() || 1)) * 100}%` : '0%',
            transition: 'width 0.1s linear'
          }}
        ></div>
      </div>

      {/* Time indicator */}
      <div className="absolute bottom-2 right-2 text-xs text-white bg-black/50 px-2 py-1 rounded z-10">
        {player ? `${Math.floor(player.currentTime() / 60)}:${String(Math.floor(player.currentTime() % 60)).padStart(2, '0')} / ${Math.floor(player.duration() / 60)}:${String(Math.floor(player.duration() % 60)).padStart(2, '0')}` : '0:00 / 0:00'}
      </div>

      <style jsx>{`
        .hls-player-container {
          position: relative;
          width: 100%;
          height: 100%;
        }
        :global(.vjs-has-logo .vjs-control-bar) {
          bottom: 0;
        }
      `}</style>
    </div>
  )
}

// Export player controls interface for TypeScript users
export interface PlayerControls {
  playPrevious: () => void
  playNext: () => void
  togglePlay: () => void
  seekTo: (time: number) => void
  setVolume: (level: number) => void
  isPlaying: boolean
}
