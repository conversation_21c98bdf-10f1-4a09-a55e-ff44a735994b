import type { PlaylistItem } from "@/lib/types"

/**
 * Generates an M3U8 playlist file content from an array of tracks
 *
 * @param tracks Array of playlist items
 * @param title Optional playlist title
 * @returns String content of the M3U8 file
 */
export function generateM3U8(tracks: PlaylistItem[], title?: string): string {
  // Start with the M3U8 header
  let content = "#EXTM3U\n"

  // Add playlist info if title is provided
  if (title) {
    content += `#PLAYLIST:${title}\n`
  }

  // Add each track
  tracks.forEach((track) => {
    // Add track info with duration if available
    const durationStr = track.duration ? `,${track.duration}` : ""
    content += `#EXTINF:${durationStr},${track.artistName} - ${track.songTitle}\n`

    // Add track URL
    content += `${track.mediaUrl || ""}\n`
  })

  return content
}

/**
 * Creates and downloads an M3U8 file
 *
 * @param tracks Array of playlist items
 * @param filename Name of the file to download
 * @param title Optional playlist title
 */
export function downloadM3U8(tracks: PlaylistItem[], filename = "playlist.m3u8", title?: string): void {
  const content = generateM3U8(tracks, title)

  // Create a blob from the content
  const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })

  // Create a download link
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename

  // Trigger the download
  document.body.appendChild(a)
  a.click()

  // Clean up
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/**
 * Parses an M3U8 file content into an array of tracks
 *
 * @param content M3U8 file content
 * @returns Array of playlist items
 */
export function parseM3U8(content: string): Partial<PlaylistItem>[] {
  const lines = content.split("\n")
  const tracks: Partial<PlaylistItem>[] = []

  let currentTrack: Partial<PlaylistItem> | null = null

  lines.forEach((line) => {
    line = line.trim()

    // Skip empty lines and header
    if (!line || line === "#EXTM3U" || line.startsWith("#PLAYLIST:")) {
      return
    }

    // Track info line
    if (line.startsWith("#EXTINF:")) {
      currentTrack = {}

      // Parse duration if available
      const durationMatch = line.match(/#EXTINF:,(\d+)/)
      if (durationMatch && durationMatch[1]) {
        currentTrack.duration = Number.parseInt(durationMatch[1], 10)
      }

      // Parse artist and title
      const infoMatch = line.match(/#EXTINF:.*,(.+)/)
      if (infoMatch && infoMatch[1]) {
        const trackInfo = infoMatch[1]
        const parts = trackInfo.split(" - ")

        if (parts.length > 1) {
          currentTrack.artistName = parts[0]
          currentTrack.songTitle = parts.slice(1).join(" - ")
        } else {
          currentTrack.songTitle = trackInfo
        }
      }
    }
    // URL line (comes after EXTINF)
    else if (currentTrack && !line.startsWith("#")) {
      currentTrack.mediaUrl = line
      tracks.push(currentTrack)
      currentTrack = null
    }
  })

  return tracks
}
