"use client"

import { useState, useEffect } from "react"
import { useMusicPlayer } from "@/contexts/music-player-context"
import { supabase } from "@/lib/supabase"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Music, Play, Pause, Heart, SkipForward, Volume2 } from "lucide-react"
import { Slider } from "@/components/ui/slider"
import Image from "next/image"

interface SpotifyTrack {
  id: string
  title: string
  artist: string
  audio_url: string
  artwork_url: string
  spotify_id: string
  isrc: string
  duration: number
}

export function SpotifyTrackPlayer() {
  const [tracks, setTracks] = useState<SpotifyTrack[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [volume, setVolume] = useState(80)
  const { playerState, play, pause, togglePlay, next, setVolume: setPlayerVolume } = useMusicPlayer()

  useEffect(() => {
    async function fetchTracks() {
      try {
        setIsLoading(true)
        const { data, error } = await supabase.from("tracks").select("*").order("created_at", { ascending: false })

        if (error) throw error

        setTracks(data || [])
      } catch (err) {
        console.error("Error fetching tracks:", err)
        setError("Failed to load tracks. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchTracks()
  }, [])

  useEffect(() => {
    // Update player volume when local volume changes
    setPlayerVolume(volume / 100)
  }, [volume, setPlayerVolume])

  const handlePlayTrack = (track: SpotifyTrack) => {
    // Convert to the format expected by the music player
    const playerTrack = {
      id: track.id,
      title: track.title,
      artist: track.artist,
      audio_url: track.audio_url,
      artwork_url: track.artwork_url,
      duration: track.duration,
    }

    play(playerTrack as any)
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Music className="h-5 w-5 mr-2 text-purple-600" /> Spotify Indie Artist Tracks
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Tracks</TabsTrigger>
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
            <TabsTrigger value="recent">Recently Played</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {tracks.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No tracks found</div>
            ) : (
              tracks.map((track) => (
                <div
                  key={track.id}
                  className="flex items-center justify-between p-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center">
                    <div className="relative h-12 w-12 mr-3 rounded overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                      {track.artwork_url ? (
                        <Image
                          src={track.artwork_url || "/placeholder.svg"}
                          alt={`${track.title} artwork`}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full w-full">
                          <Music className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{track.title}</h3>
                      <p className="text-sm text-gray-500">{track.artist}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 mr-2">{formatDuration(track.duration)}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-gray-500 hover:text-purple-600"
                      onClick={() => {}}
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-gray-500 hover:text-purple-600"
                      onClick={() => handlePlayTrack(track)}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </TabsContent>

          <TabsContent value="favorites">
            <div className="text-center py-8 text-gray-500">Your favorite tracks will appear here</div>
          </TabsContent>

          <TabsContent value="recent">
            <div className="text-center py-8 text-gray-500">Your recently played tracks will appear here</div>
          </TabsContent>
        </Tabs>

        {/* Mini Player */}
        {playerState.currentTrack && (
          <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="relative h-10 w-10 mr-3 rounded overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                  {playerState.currentTrack.artwork_url ? (
                    <Image
                      src={playerState.currentTrack.artwork_url || "/placeholder.svg"}
                      alt={`${playerState.currentTrack.title} artwork`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full w-full">
                      <Music className="h-5 w-5 text-gray-400" />
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-sm">{playerState.currentTrack.title}</h3>
                  <p className="text-xs text-gray-500">{playerState.currentTrack.artist}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-500 hover:text-purple-600"
                  onClick={togglePlay}
                >
                  {playerState.isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button variant="ghost" size="icon" className="text-gray-500 hover:text-purple-600" onClick={next}>
                  <SkipForward className="h-4 w-4" />
                </Button>
                <div className="flex items-center w-24 ml-2">
                  <Volume2 className="h-3 w-3 text-gray-500 mr-2" />
                  <Slider value={[volume]} max={100} step={1} onValueChange={(value) => setVolume(value[0])} />
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
