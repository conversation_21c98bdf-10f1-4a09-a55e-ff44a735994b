"use client"

import type React from "react"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, Music } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

// Define types for our card data
export interface QueueCardItem {
  id: number | string
  position?: number
  artistName: string
  artistId?: string
  songTitle: string
  type: string
  submissionTime?: string
  platform?: string
  url?: string
  isPlaying?: boolean
}

interface QueueCardProps {
  item: QueueCardItem
  className?: string
  isDraggable?: boolean
  dragHandleProps?: any
  isHighlighted?: boolean
  onExpand?: (id: number) => void
  isExpanded?: boolean
  children?: React.ReactNode
  isUnlockMode?: boolean
}

// Helper function to get platform icon
export const getPlatformIcon = (platform?: string) => {
  if (!platform) return <Music className="h-4 w-4" />

  switch (platform.toLowerCase()) {
    case "soundcloud":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M1.175 12.225c-.051 0-.094.046-.101.1l-.233 2.154.233 2.105c.007.058.05.098.101.098.05 0 .09-.04.099-.098l.255-2.105-.27-2.154c-.009-.055-.05-.1-.1-.1m-.899.828c-.05 0-.091.04-.1.099L0 14.479l.176 1.299c.009.06.05.1.101.1.05 0 .09-.04.099-.1l.2-1.3-.2-1.327c-.01-.06-.05-.099-.1-.099m1.8-1.548c-.061 0-.11.05-.12.11l-.216 2.762.216 2.681c.01.06.06.11.12.11.06 0 .11-.05.12-.11l.244-2.681-.244-2.762c-.01-.06-.061-.11-.12-.11m.901-.495c-.07 0-.13.06-.14.13l-.2 3.237.2 3.142c.01.07.07.13.14.13.07 0 .13-.06.14-.13l.22-3.142-.22-3.237c-.01-.07-.07-.13-.14-.13m1.001-.473c-.08 0-.14.07-.15.15l-.181 3.56.181 3.443c.01.08.07.15.15.15.08 0 .14-.07.15-.15l.205-3.443-.205-3.56c-.01-.08-.07-.15-.15-.15m1.002-.328c-.09 0-.16.08-.17.17l-.167 3.867.167 3.74c.01.09.08.17.17.17.09 0 .16-.08.17-.17l.185-3.74-.185-3.867c-.01-.09-.08-.17-.17-.17m1.17-.33c-.1 0-.18.09-.19.19l-.15 4.177.15 4.043c.01.1.09.19.19.19.1 0 .18-.09.19-.19l.17-4.043-.17-4.177c-.01-.1-.09-.19-.19-.19m1.198-.137c-.11 0-.2.1-.21.21l-.137 4.294.137 4.163c.01.11.1.2.21.2.11 0 .18-.09.19-.2l.153-4.163-.153-4.294c-.01-.11-.1-.21-.21-.21m1.3-.112c-.121 0-.221.11-.231.22l-.126 4.396.126 4.267c.01.12.11.22.231.22.12 0 .22-.1.23-.22l.142-4.267-.142-4.396c-.01-.11-.11-.22-.23-.22m1.401-.048c-.131 0-.241.11-.251.24l-.114 4.424.114 4.306c.01.13.12.24.251.24.13 0 .24-.11.25-.24l.127-4.306-.127-4.424c-.01-.13-.12-.24-.25-.24m1.502-.057c-.06 0-.12.01-.17.03-.05.02-.09.05-.13.09-.07.08-.11.17-.11.27l-.001.16-.11 4.191.11 4.414h.001c.01.11.05.2.11.27.04.04.08.07.13.09.05.02.11.03.17.03.06 0 .12-.01.17-.03.05-.02.09-.05.13-.09.07-.07.11-.16.11-.27l.001-.13.119-4.284-.121-4.35c-.01-.11-.04-.2-.11-.27-.04-.04-.08-.07-.13-.09-.05-.02-.11-.03-.17-.03m7.954 9.972c-.377 0-7.255 0-7.29-.003a.425.425 0 0 1-.299-.128.433.433 0 0 1-.126-.307l-.004-8.089a.433.433 0 0 1 .126-.32c.075-.08.177-.126.284-.131.018-.002 4.901-.002 7.162-.002a.39.39 0 0 1 .389.39v.002c.01 2.562.018 7.729.018 8.172 0 .239-.189.431-.43.431z" />
        </svg>
      )
    case "spotify":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z" />
        </svg>
      )
    case "youtube":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
        </svg>
      )
    case "bandcamp":
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.6 0 12 0zm0 18.3c-3.5 0-6.3-2.8-6.3-6.3S8.5 5.7 12 5.7s6.3 2.8 6.3 6.3-2.8 6.3-6.3 6.3zm3.7-6.9l-5.9 3.5-3.7-6.3 5.9-3.5 3.7 6.3z" />
        </svg>
      )
    default:
      return <Music className="h-4 w-4" />
  }
}

// Neon turquoise style for artist name
const neonTurquoiseStyle = {
  color: "#00ffff",
  textShadow: "0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff",
  transition: "all 0.3s ease",
}

const neonTurquoiseHoverStyle = {
  textShadow: "0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff, 0 0 20px #00ffff",
  filter: "brightness(1.2)",
}

export function QueueCard({
  item,
  className,
  isDraggable = false,
  dragHandleProps,
  isHighlighted = false,
  onExpand,
  isExpanded = false,
  children,
  isUnlockMode = false,
}: QueueCardProps) {
  // Local state for expansion if no external control is provided
  const [localExpanded, setLocalExpanded] = useState(false)

  // Use either controlled or uncontrolled expansion state
  const expanded = onExpand ? isExpanded : localExpanded

  // Handle expand toggle
  const handleExpandToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onExpand) {
      onExpand(item.id)
    } else {
      setLocalExpanded(!localExpanded)
    }
  }

  return (
    <div
      className={cn(
        "admin-card relative transition-all duration-300 hover:shadow-[0_0_8px_rgba(147,51,234,0.4)] hover:border-purple-400 hover:scale-[1.02] transform-gpu",
        isHighlighted ? "bg-purple-50/10" : "",
        className,
      )}
    >
      {isDraggable && dragHandleProps && (
        <div
          {...dragHandleProps}
          className="absolute left-0 top-0 bottom-0 w-8 flex items-center justify-center cursor-grab active:cursor-grabbing"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-400"
          >
            <circle cx="9" cy="12" r="1" />
            <circle cx="9" cy="5" r="1" />
            <circle cx="9" cy="19" r="1" />
            <circle cx="15" cy="12" r="1" />
            <circle cx="15" cy="5" r="1" />
            <circle cx="15" cy="19" r="1" />
          </svg>
        </div>
      )}

      {isUnlockMode && dragHandleProps && (
        <div
          {...dragHandleProps}
          className="absolute right-0 top-0 bottom-0 w-8 flex items-center justify-center cursor-grab active:cursor-grabbing"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-400"
          >
            <circle cx="9" cy="12" r="1" />
            <circle cx="9" cy="5" r="1" />
            <circle cx="9" cy="19" r="1" />
            <circle cx="15" cy="12" r="1" />
            <circle cx="15" cy="5" r="1" />
            <circle cx="15" cy="19" r="1" />
          </svg>
        </div>
      )}

      <div className={cn("flex justify-between items-start", isDraggable ? "pl-6" : "")}>
        <div className="flex items-center">
          {item.position && (
            <span className="font-mono text-sm w-6 text-gray-500">
              {item.isPlaying && "▶️ "}
              {item.position.toString().padStart(2, "0")}
            </span>
          )}
          <div className={item.position ? "ml-2" : ""}>
            <div className="flex items-center">
              <div className="mr-2 text-gray-400">
                {item.platform ? getPlatformIcon(item.platform) : <Music className="h-4 w-4" />}
              </div>
              {item.url ? (
                <a
                  href={item.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium line-clamp-1 hover:text-purple-600 transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  {item.songTitle}
                </a>
              ) : (
                <p className="font-medium line-clamp-1">{item.songTitle}</p>
              )}
            </div>
            {item.artistId ? (
              <Link
                href={`/artist/${item.artistId}`}
                className="text-sm"
                style={neonTurquoiseStyle}
                onMouseEnter={(e) => {
                  Object.assign(e.currentTarget.style, {
                    textShadow: neonTurquoiseHoverStyle.textShadow,
                    filter: neonTurquoiseHoverStyle.filter,
                  })
                }}
                onMouseLeave={(e) => {
                  Object.assign(e.currentTarget.style, {
                    textShadow: neonTurquoiseStyle.textShadow,
                    filter: "",
                  })
                }}
                onClick={(e) => e.stopPropagation()}
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.artistName}
              </Link>
            ) : (
              <span className="text-sm" style={neonTurquoiseStyle}>
                {item.artistName}
              </span>
            )}
          </div>
        </div>
        <div className="flex flex-col items-end">
          <Badge variant="default">{item.type}</Badge>
          {item.submissionTime && (
            <div className="flex items-center mt-1">
              <span className="text-xs text-gray-500 mr-1">{item.submissionTime}</span>
              <Button
                size="icon"
                variant="ghost"
                onClick={handleExpandToggle}
                className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 h-5 w-5 p-0"
              >
                {expanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
              </Button>
            </div>
          )}
        </div>
      </div>

      {expanded && <div className="mt-3 pt-3 border-t border-gray-700/30 pl-6">{children}</div>}
    </div>
  )
}
