"use client"

import React from "react"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { QueueCard, type QueueCardItem } from "./queue-card"

interface DraggableQueueCardProps {
  item: QueueCardItem
  isHighlighted?: boolean
  isExpanded?: boolean
  onExpand?: (id: number) => void
  children?: React.ReactNode
  isUnlockMode?: boolean
}

export function DraggableQueueCard({
  item,
  isHighlighted = false,
  isExpanded = false,
  onExpand,
  children,
  isUnlockMode = false,
}: DraggableQueueCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id.toString() })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1,
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <QueueCard
        item={item}
        isDraggable={true}
        dragHandleProps={listeners}
        isHighlighted={isHighlighted}
        isExpanded={isExpanded}
        onExpand={onExpand}
        isUnlockMode={isUnlockMode}
        className={isDragging ? "border-purple-500 shadow-lg" : ""}
      >
        {children}
      </QueueCard>
    </div>
  )
}
