"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { downloadM3U8File, getPlaylistItemsFromSupabase, createM3U8FromItems } from "@/lib/m3u8-utils"
import { HLSPlayer } from "@/components/video-player/hls-player"
import type { PlaylistItem } from "@/lib/types"
import { Download, RefreshCw } from "lucide-react"

export function M3U8PlaylistManager() {
  const [playlists, setPlaylists] = useState<any[]>([])
  const [selectedPlaylistId, setSelectedPlaylistId] = useState<string | null>(null)
  const [playlistItems, setPlaylistItems] = useState<PlaylistItem[]>([])
  const [m3u8Content, setM3U8Content] = useState<string>("")
  const [m3u8BlobUrl, setM3U8BlobUrl] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch playlists from Supabase
  useEffect(() => {
    async function fetchPlaylists() {
      try {
        const { data, error } = await supabase.from("playlists").select("*").order("created_at", { ascending: false })

        if (error) throw error

        setPlaylists(data || [])

        // Select the first playlist by default
        if (data && data.length > 0 && !selectedPlaylistId) {
          setSelectedPlaylistId(data[0].id)
        }
      } catch (err) {
        console.error("Error fetching playlists:", err)
        setError("Failed to load playlists")
      }
    }

    fetchPlaylists()
  }, [selectedPlaylistId])

  // Load playlist items when a playlist is selected
  useEffect(() => {
    if (!selectedPlaylistId) return

    async function loadPlaylistItems() {
      setLoading(true)
      try {
        const items = await getPlaylistItemsFromSupabase(selectedPlaylistId)
        setPlaylistItems(items)

        // Generate M3U8 content
        const playlist = playlists.find((p) => p.id === selectedPlaylistId)
        const content = createM3U8FromItems(items, playlist?.name || "Playlist")
        setM3U8Content(content)

        // Create blob URL for the player
        const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })
        const url = URL.createObjectURL(blob)
        setM3U8BlobUrl(url)

        setError(null)
      } catch (err) {
        console.error("Error loading playlist items:", err)
        setError("Failed to load playlist items")
      } finally {
        setLoading(false)
      }
    }

    loadPlaylistItems()

    // Clean up blob URL when component unmounts or playlist changes
    return () => {
      if (m3u8BlobUrl) {
        URL.revokeObjectURL(m3u8BlobUrl)
      }
    }
  }, [selectedPlaylistId, playlists])

  const handleDownloadM3U8 = () => {
    if (!selectedPlaylistId || !m3u8Content) return

    const playlist = playlists.find((p) => p.id === selectedPlaylistId)
    const filename = `${playlist?.name || "playlist"}.m3u8`

    downloadM3U8File(m3u8Content, filename)
  }

  const handleRefreshPlaylist = async () => {
    if (!selectedPlaylistId) return

    setLoading(true)
    try {
      const items = await getPlaylistItemsFromSupabase(selectedPlaylistId)
      setPlaylistItems(items)

      // Generate M3U8 content
      const playlist = playlists.find((p) => p.id === selectedPlaylistId)
      const content = createM3U8FromItems(items, playlist?.name || "Playlist")
      setM3U8Content(content)

      // Update blob URL
      if (m3u8BlobUrl) {
        URL.revokeObjectURL(m3u8BlobUrl)
      }
      const blob = new Blob([content], { type: "application/vnd.apple.mpegurl" })
      const url = URL.createObjectURL(blob)
      setM3U8BlobUrl(url)

      setError(null)
    } catch (err) {
      console.error("Error refreshing playlist:", err)
      setError("Failed to refresh playlist")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <h2 className="text-2xl font-bold">M3U8 Playlist Manager</h2>

        {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

        <div className="flex flex-col space-y-2">
          <label htmlFor="playlist-select" className="font-medium">
            Select Playlist
          </label>
          <select
            id="playlist-select"
            className="p-2 border rounded-md"
            value={selectedPlaylistId || ""}
            onChange={(e) => setSelectedPlaylistId(e.target.value)}
            disabled={loading}
          >
            <option value="">Select a playlist</option>
            {playlists.map((playlist) => (
              <option key={playlist.id} value={playlist.id}>
                {playlist.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex space-x-2">
          <Button
            onClick={handleRefreshPlaylist}
            disabled={!selectedPlaylistId || loading}
            className="flex items-center space-x-1"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </Button>

          <Button
            onClick={handleDownloadM3U8}
            disabled={!selectedPlaylistId || !m3u8Content || loading}
            className="flex items-center space-x-1"
          >
            <Download className="h-4 w-4" />
            <span>Download M3U8</span>
          </Button>
        </div>
      </div>

      {selectedPlaylistId && playlistItems.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Playlist Preview</h3>

          <div className="border rounded-lg overflow-hidden">
            {m3u8BlobUrl ? (
              <HLSPlayer playlist={playlistItems} showControls={true} fluid={true} aspectRatio="16:9" />
            ) : (
              <div className="flex items-center justify-center h-64 bg-gray-100">
                <p className="text-gray-500">Loading player...</p>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Playlist Items ({playlistItems.length})</h4>
            <div className="border rounded-md overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Title
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Artist
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {playlistItems.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {item.songTitle}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.artistName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.duration ? formatDuration(item.duration) : "N/A"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">M3U8 Content</h4>
            <div className="border rounded-md p-4 bg-gray-50">
              <pre className="text-xs overflow-auto max-h-64">{m3u8Content}</pre>
            </div>
          </div>
        </div>
      )}

      {selectedPlaylistId && playlistItems.length === 0 && !loading && (
        <div className="bg-yellow-50 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          This playlist is empty. Add songs to the playlist to generate an M3U8 file.
        </div>
      )}
    </div>
  )
}

// Helper function to format duration in seconds to MM:SS
function formatDuration(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, "0")}`
}
