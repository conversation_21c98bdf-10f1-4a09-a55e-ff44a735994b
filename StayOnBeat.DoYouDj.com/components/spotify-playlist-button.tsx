"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { generateSpotifyPlaylist } from "@/lib/generate-spotify-playlist"
import { Download } from "lucide-react"
import { useState } from "react"

export function SpotifyPlaylistButton() {
  const [isGenerating, setIsGenerating] = useState(false)

  const handleGenerate = async () => {
    setIsGenerating(true)
    try {
      await generateSpotifyPlaylist()
    } catch (error) {
      console.error("Error:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Button onClick={handleGenerate} disabled={isGenerating} className="flex items-center gap-2">
      <Download className="h-4 w-4" />
      {isGenerating ? "Generating..." : "Generate Spotify Playlist"}
    </Button>
  )
}
