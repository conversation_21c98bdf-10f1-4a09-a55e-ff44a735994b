"use client"

import { useState } from "react"
import { UploadButton } from "@uploadthing/react"
import { toast } from "sonner"
import { But<PERSON> } from "@/components/ui/button"
import { Trash2, Upload, FileText } from "lucide-react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"

interface M3U8FileUploaderProps {
  onUploadComplete?: (url: string, fileName: string) => void
  onDelete?: () => void
  existingFileUrl?: string
  existingFileName?: string
  className?: string
}

export function M3U8FileUploader({
  onUploadComplete,
  onDelete,
  existingFileUrl,
  existingFileName,
  className = "",
}: M3U8FileUploaderProps) {
  const [fileUrl, setFileUrl] = useState<string | null>(existingFileUrl || null)
  const [fileName, setFileName] = useState<string | null>(existingFileName || null)
  const [isUploading, setIsUploading] = useState(false)

  const handleDelete = () => {
    setFileUrl(null)
    setFileName(null)
    if (onDelete) onDelete()
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {fileUrl ? (
        <div className="rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-purple-500" />
              <div>
                <p className="font-medium">{fileName || "M3U8 Playlist"}</p>
                <p className="text-xs text-muted-foreground">
                  File uploaded successfully
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              asChild
            >
              <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                View File
              </a>
            </Button>
          </div>
        </div>
      ) : (
        <div className="rounded-lg border border-dashed p-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <Upload className="h-10 w-10 text-muted-foreground" />
            <div className="text-center">
              <p className="text-sm font-medium">Upload M3U8 Playlist File</p>
              <p className="text-xs text-muted-foreground mt-1">
                Drag and drop or click to upload your .m3u8 playlist file
              </p>
            </div>
            <UploadButton<OurFileRouter>
              endpoint="m3u8Uploader"
              onUploadBegin={() => {
                setIsUploading(true)
              }}
              onClientUploadComplete={(res) => {
                setIsUploading(false)
                if (res && res[0]) {
                  const url = res[0].url
                  const name = res[0].fileName || res[0].name || "playlist.m3u8"
                  setFileUrl(url)
                  setFileName(name)

                  if (onUploadComplete) onUploadComplete(url, name)

                  toast.success("M3U8 file uploaded successfully")
                }
              }}
              onUploadError={(error: Error) => {
                setIsUploading(false)
                toast.error(`Error uploading file: ${error.message}`)
              }}
              className="ut-button:bg-purple-600 ut-button:ut-readying:bg-purple-500/50 ut-button:hover:bg-purple-700"
            />
          </div>
        </div>
      )}
    </div>
  )
}
