"use client"

import React from "react"
import { useDraggable } from "@dnd-kit/core"
import { CSS } from "@dnd-kit/utilities"
import { Badge } from "@/components/ui/badge"
import { Music } from "lucide-react"
import { QueueCardItem, getPlatformIcon } from "./queue-card"

interface DraggableSubmissionCardProps {
  item: QueueCardItem
  className?: string
  neonTurquoiseStyle: React.CSSProperties
}

export function DraggableSubmissionCard({
  item,
  className,
  neonTurquoiseStyle
}: DraggableSubmissionCardProps) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `submission-${item.id}`,
    data: {
      type: 'submission',
      item
    }
  })

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`admin-card relative ${className || ''} ${isDragging ? 'opacity-50 border-purple-500' : 'hover:border-purple-400/50'}`}
    >
      <div className="flex justify-between items-start">
        <div className="flex items-center">
          <div className="mr-2 text-gray-400">
            {item.platform ? getPlatformIcon(item.platform) : <Music className="h-4 w-4" />}
          </div>
          <div>
            <p className="font-medium line-clamp-1">{item.songTitle}</p>
            <span className="text-sm" style={neonTurquoiseStyle}>
              {item.artistName}
            </span>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <Badge variant="default">{item.type}</Badge>
          <div className="flex items-center mt-1">
            <span className="text-xs text-gray-500 mr-1">{item.submissionTime}</span>
            <div
              {...attributes}
              {...listeners}
              className="ml-2 w-8 h-8 flex items-center justify-center cursor-grab active:cursor-grabbing text-purple-400 hover:text-purple-300 bg-purple-900/20 border border-purple-500/30 rounded-md transition-all duration-300 hover:shadow-[0_0_8px_rgba(147,51,234,0.4)] hover:border-purple-400"
              title="Drag to add to queue"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="9" cy="12" r="1" />
                <circle cx="9" cy="5" r="1" />
                <circle cx="9" cy="19" r="1" />
                <circle cx="15" cy="12" r="1" />
                <circle cx="15" cy="5" r="1" />
                <circle cx="15" cy="19" r="1" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
