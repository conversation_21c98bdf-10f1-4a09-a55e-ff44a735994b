'use client';

import { usePathname } from "next/navigation";
import { NavigationDock } from "@/components/navigation-dock.tsx";
import { AdminsBigDock } from "@/components/admin/admins-big-dock";
import { FooterWrapper } from "@/components/FooterWrapper";
import Link from "next/link";
import { PageTransition } from "@/components/page-transition";

export function NavigationWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith('/admin');

  return (
    <PageTransition>
      {!isAdminRoute && <NavigationDock />}
      <main className="flex-1">
        {children}
      </main>
      <FooterWrapper />
      {!isAdminRoute && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-[100]">
          <Link href="/admin">
            <button
              className="admin-login-button w-6 h-6 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border border-red-600 transition-all duration-300 shadow-lg hover:scale-110"
              style={{
                background:
                  "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                boxShadow:
                  "inset 0 0 8px rgba(255, 0, 0, 0.5), 0 0 5px rgba(255, 0, 0, 0.7), 0 0 10px rgba(255, 0, 0, 0.3)",
                backdropFilter: "blur(4px)",
                transform: "translateZ(0)",
              }}
            >
              <img
                src="/images/sob-logo-square.png"
                alt="Admin"
                className="h-3 w-3 object-contain"
              />
              <span className="sr-only">Admin</span>
            </button>
          </Link>
        </div>
      )}
    </PageTransition>
  );
}