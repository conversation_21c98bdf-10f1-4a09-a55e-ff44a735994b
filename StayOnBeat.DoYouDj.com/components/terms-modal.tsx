"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { X, Printer } from "lucide-react"

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  content: React.ReactNode
}

export function TermsModal({ isOpen, onClose, title, content }: ModalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    if (isOpen) {
      document.body.style.overflow = "hidden"
    }

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  if (!mounted || !isOpen) return null

  const handlePrint = () => {
    const printWindow = window.open("", "_blank")
    if (!printWindow) return

    const content = document.getElementById("modal-content")?.innerHTML

    printWindow.document.write(`
      <html>
        <head>
          <title>${title}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; padding: 20px; }
            h1 { color: #000; }
            h2 { color: #333; margin-top: 20px; }
            p { margin-bottom: 10px; }
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          ${content}
        </body>
      </html>
    `)

    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm">
      <div className="relative w-full max-w-3xl max-h-[90vh] overflow-auto bg-black/90 border border-[#00ffff]/30 rounded-lg shadow-lg">
        <div className="sticky top-0 flex items-center justify-between p-4 border-b border-[#00ffff]/20 bg-black/90 z-10">
          <h2 className="text-xl font-semibold text-[#00ffff]">{title}</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={handlePrint}
              className="p-2 text-[#00ffff]/70 hover:text-[#00ffff] transition-colors"
              aria-label="Print"
            >
              <Printer size={20} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-[#00ffff]/70 hover:text-[#00ffff] transition-colors"
              aria-label="Close"
            >
              <X size={20} />
            </button>
          </div>
        </div>
        <div className="p-6 text-[#00ffff]/80" id="modal-content">
          {content}
        </div>
      </div>
    </div>
  )
}
