"use client"

import { useState } from "react"
import { UploadButton } from "@uploadthing/react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"
import { toast } from "@/components/ui/use-toast"

interface BackgroundImageUploaderProps {
  onUploadComplete?: (url: string) => void
}

export function BackgroundImageUploader({ onUploadComplete }: BackgroundImageUploaderProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)

  return (
    <div className="space-y-4">
      <UploadButton<OurFileRouter, "backgroundImage">
        endpoint="backgroundImage"
        onUploadBegin={() => {
          setIsUploading(true)
        }}
        onClientUploadComplete={(res) => {
          setIsUploading(false)
          if (res && res[0]) {
            const url = res[0].url
            setImageUrl(url)
            if (onUploadComplete) onUploadComplete(url)
            toast({
              title: "Upload complete",
              description: "Your background image has been uploaded successfully.",
            })
          }
        }}
        onUploadError={(error: Error) => {
          setIsUploading(false)
          toast({
            title: "Upload failed",
            description: error.message,
            variant: "destructive",
          })
        }}
      />

      {imageUrl && (
        <div className="mt-4">
          <p className="text-sm text-gray-500 mb-2">Preview:</p>
          <div className="relative h-40 w-full overflow-hidden rounded-md border">
            <img
              src={imageUrl || "/placeholder.svg"}
              alt="Uploaded background"
              className="h-full w-full object-cover"
            />
          </div>
        </div>
      )}
    </div>
  )
}
