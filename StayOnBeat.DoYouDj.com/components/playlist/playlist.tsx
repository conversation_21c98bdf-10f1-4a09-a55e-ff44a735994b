"use client"

import { usePlayer } from "@/contexts/music-player-context"
import { formatDuration } from "@/lib/utils"
import { Play, Pause } from "lucide-react"
import Image from "next/image"

export function Playlist() {
  const { state, play, pause, setCurrentIndex } = usePlayer()

  const handleTrackClick = (index: number) => {
    setCurrentIndex(index)
    if (!state.isPlaying) {
      play()
    }
  }

  if (!state.playlist.length) {
    return (
      <div className="flex h-full items-center justify-center text-muted-foreground">
        No tracks in playlist
      </div>
    )
  }

  return (
    <div className="flex h-full flex-col space-y-2 overflow-y-auto p-4">
      {state.playlist.map((track, index) => (
        <button
          key={track.id}
          onClick={() => handleTrackClick(index)}
          className={`flex w-full items-center space-x-4 rounded-lg p-2 transition-colors hover:bg-accent ${
            state.currentTrack?.id === track.id ? "bg-accent" : ""
          }`}
        >
          <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-md">
            <Image
              src={track.artwork_url}
              alt={`${track.title} artwork`}
              fill
              className="object-cover"
            />
          </div>
          <div className="flex flex-1 flex-col items-start space-y-1">
            <span className="line-clamp-1 text-sm font-medium">{track.title}</span>
            <span className="line-clamp-1 text-xs text-muted-foreground">{track.artist}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-muted-foreground">
              {formatDuration(track.duration)}
            </span>
            {state.currentTrack?.id === track.id && (
              <div className="flex h-6 w-6 items-center justify-center">
                {state.isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </div>
            )}
          </div>
        </button>
      ))}
    </div>
  )
} 