 "use client"

import { useState } from "react"
import Link from "next/link"
import { TermsModal } from "@/components/terms-modal"
import { TermsContent } from "@/components/terms-content"
import { PrivacyContent } from "@/components/privacy-content"

export function SobFooter() {
  const [showTerms, setShowTerms] = useState(false)
  const [showPrivacy, setShowPrivacy] = useState(false)

  return (
    <>
      {/* SobFooter */}
      <footer className="w-full text-white mt-auto bg-transparent" style={{ borderTop: 'none' }}>
        <div className="container mx-auto px-4 max-w-[81%] md:max-w-[725px]">
          <div className="flex items-center justify-between w-full h-4">
            {/* Copyright on the left */}
            <p className="text-[5px] text-[#00ffff]/70 mr-auto whitespace-nowrap neon-text-small">
              © {new Date().getFullYear()}{" "}
              <a href="https://www.stayonbeat.com" className="hover:text-[#00ffff] transition-colors neon-text-small">
                StayOnBeat
              </a>{" "}
              All rights reserved
            </p>

            {/* Links on the right */}
            <div className="flex space-x-4 items-center">
              <button
                onClick={() => setShowTerms(true)}
                className="text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small"
              >
                Terms
              </button>
              <button
                onClick={() => setShowPrivacy(true)}
                className="text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small"
              >
                Privacy
              </button>
              <a
                href="mailto:<EMAIL>"
                className="text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small"
              >
                Contact
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* Terms Modal */}
      <TermsModal
        isOpen={showTerms}
        onClose={() => setShowTerms(false)}
        title="Terms of Service"
        content={<TermsContent />}
      />

      {/* Privacy Modal */}
      <TermsModal
        isOpen={showPrivacy}
        onClose={() => setShowPrivacy(false)}
        title="Privacy Policy"
        content={<PrivacyContent />}
      />
    </>
  )
}
