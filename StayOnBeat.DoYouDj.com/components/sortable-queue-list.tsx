"use client"

import React, { useState } from "react"
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  DragCancelEvent
} from "@dnd-kit/core"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from "@dnd-kit/sortable"
import { DraggableQueueCard } from "./draggable-queue-card"
import { QueueCard, type QueueCardItem } from "./queue-card"

interface SortableQueueListProps {
  items: QueueCardItem[]
  onReorder?: (items: QueueCardItem[]) => void
  expandedItems?: Record<string | number, boolean>
  onExpand?: (id: number) => void
  isUnlockMode?: boolean
  emptyMessage?: string
}

export function SortableQueueList({
  items,
  onReorder,
  expandedItems = {},
  onExpand,
  isUnlockMode = false,
  emptyMessage = "Queue is empty"
}: SortableQueueListProps) {
  const [activeId, setActiveId] = useState<string | null>(null)
  
  // Find the active item
  const activeItem = activeId ? items.find(item => item.id.toString() === activeId) : null
  
  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id.toString())
  }

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex(item => item.id.toString() === active.id)
      const newIndex = items.findIndex(item => item.id.toString() === over.id)
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newItems = arrayMove(items, oldIndex, newIndex)
        
        // Update positions
        const reorderedItems = newItems.map((item, index) => ({
          ...item,
          position: index + 1
        }))
        
        // Call the onReorder callback if provided
        if (onReorder) {
          onReorder(reorderedItems)
        }
      }
    }
    
    setActiveId(null)
  }

  // Handle drag cancel
  const handleDragCancel = (event: DragCancelEvent) => {
    setActiveId(null)
  }

  // If there are no items, show the empty message
  if (items.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-purple-400">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <SortableContext 
        items={items.map(item => item.id.toString())}
        strategy={verticalListSortingStrategy}
      >
        <div className="space-y-2">
          {items.map(item => (
            <DraggableQueueCard
              key={item.id}
              item={item}
              isHighlighted={item.isPlaying}
              isExpanded={expandedItems[item.id] || false}
              onExpand={onExpand}
              isUnlockMode={isUnlockMode}
            >
              {expandedItems[item.id] && (
                <div className="text-sm">
                  <p>Submitted: {item.submissionTime}</p>
                  {item.url && (
                    <p className="mt-1">
                      <a 
                        href={item.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300"
                      >
                        View original
                      </a>
                    </p>
                  )}
                </div>
              )}
            </DraggableQueueCard>
          ))}
        </div>
      </SortableContext>
      
      {/* Drag overlay - shows a preview of the item being dragged */}
      <DragOverlay adjustScale={true}>
        {activeId && activeItem ? (
          <QueueCard
            item={activeItem}
            isDraggable={true}
            isHighlighted={activeItem.isPlaying}
            className="shadow-lg border-purple-500 opacity-80"
          />
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
