"use client"

import React from 'react';

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"

// Define the pages to cycle through
const pages = [
  { path: "/", label: "Home" },
  { path: "/queue", label: "Queue" },
  { path: "/playlists", label: "Playlists" },
  { path: "/player", label: "Player" },
  { path: "/submit", label: "Submit" },
  { path: "/profile", label: "Profile" },
]

export function NavigationDock() {
  const pathname = usePathname()
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const router = useRouter()
  const [isAtBottom, setIsAtBottom] = useState(false)

  // Add a check for admin pages in the component:
  const isAdminPage = pathname.startsWith("/admin")

  // Find current page index
  const getCurrentPageIndex = () => {
    const index = pages.findIndex((page) => page.path === pathname)
    return index >= 0 ? index : 0 // Default to home if not found
  }

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight

      // Check if scrolled to bottom (with a small threshold)
      const isBottom = currentScrollY + windowHeight >= documentHeight - 20
      setIsAtBottom(isBottom)

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
        setIsCollapsed(true)
      } else {
        setIsVisible(true)
        setIsCollapsed(false)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener("scroll", handleScroll)

    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [lastScrollY])

  // Navigate to the next page in the cycle
  const navigateNext = () => {
    // Set direction in localStorage for PageTransition component
    localStorage.setItem("navigationDirection", "right")

    const currentIndex = getCurrentPageIndex()
    const nextIndex = (currentIndex + 1) % pages.length

    // Use router.push for client-side navigation without full page reload
    router.push(pages[nextIndex].path)
  }

  // Navigate to the previous page in the cycle
  const navigatePrev = () => {
    // Set direction in localStorage for PageTransition component
    localStorage.setItem("navigationDirection", "left")

    const currentIndex = getCurrentPageIndex()
    const prevIndex = (currentIndex - 1 + pages.length) % pages.length

    // Use router.push for client-side navigation without full page reload
    router.push(pages[prevIndex].path)
  }

  return (
    <React.Fragment>
      {/* Floating corner docks - only visible when collapsed */}
      {isCollapsed && (
        <>
          {/* Left corner dock - Previous button */}
          <div className="fixed bottom-4 left-4 z-50">
            <div
              className="flex h-12 w-12 items-center justify-center rounded-full bg-black/80 backdrop-blur-md py-1 border border-red-500/30"
              style={{ padding: "4px" }}
            >
              <button
                className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-[#00ffff] transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(0, 255, 255, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                  width: "40px",
                  height: "40px",
                }}
                onClick={navigatePrev}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white neon-text-cyan"
                >
                  <path d="m11 17-5-5 5-5"></path>
                  <path d="m18 17-5-5 5-5"></path>
                </svg>
                <span className="sr-only">Previous</span>
              </button>
            </div>
          </div>

          {/* Right corner dock - Forward button */}
          <div className="fixed bottom-4 right-4 z-50">
            <div
              className="flex h-12 w-12 items-center justify-center rounded-full bg-black/80 backdrop-blur-md py-1 border border-red-500/30"
              style={{ padding: "4px" }}
            >
              <button
                className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-[#00ffff] transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(0, 255, 255, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                  width: "40px",
                  height: "40px",
                }}
                onClick={navigateNext}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white neon-text-cyan"
                  style={{ transform: "rotate(180deg)" }}
                >
                  <path d="m11 17-5-5 5-5"></path>
                  <path d="m18 17-5-5 5-5"></path>
                </svg>
                <span className="sr-only">Next</span>
              </button>
            </div>
          </div>
        </>
      )}

      {/* LOCKED NAVIGATION BAR DESIGN - DO NOT MODIFY SHAPES OR DIMENSIONS */}
      <div
        className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-opacity duration-300 ${
          isCollapsed || (isAdminPage && !isAtBottom) ? "opacity-0 pointer-events-none" : "opacity-100"
        }`}
        style={{
          position: 'fixed',
          top: '16px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: 'auto',
          margin: '0 auto',
          display: 'block'
        }}
      >
        <div
          className={`flex items-center rounded-full bg-black/80 backdrop-blur-md px-5 py-2 border border-red-500/30 neon-box-red transition-all duration-300 ${
            isCollapsed ? "w-12 justify-center" : "gap-5"
          }`}
          style={{
            width: isCollapsed ? "52px" : "fit-content",
            padding: isCollapsed ? "6px" : "4px 6px",
            zIndex: 50,
            transformOrigin: "center bottom",
            height: "44px",
            transform: "scale(1)",
            maxWidth: "90vw",
            position: "relative",
            display: "flex",
            alignItems: "center",
            justifyContent: "center"
          }}
        >
          {/* Login button - only visible when not collapsed */}
          {!isCollapsed && (
            <div style={{ padding: "2px" }}>
              <button
                className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-[#00ffff] transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(0, 255, 255, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                  width: "40px",
                  height: "40px",
                }}
                onClick={navigatePrev}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white neon-text-cyan"
                >
                  <path d="m11 17-5-5 5-5"></path>
                  <path d="m18 17-5-5 5-5"></path>
                </svg>
                <span className="sr-only">Previous</span>
              </button>
            </div>
          )}

          {/* Logo (middle) with admin login trigger - always visible */}
          <Link
            href="/"
            className="relative"
            style={{ padding: "2px" }}
            onClick={() => localStorage.setItem("navigationDirection", "right")}
          >
            {isCollapsed ? (
              <button
                className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-red-500 transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(255, 0, 0, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                  width: "40px",
                  height: "40px",
                }}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white neon-text-red"
                >
                  <path d="M18 8c0 4.5-6 9-6 9s-6-4.5-6-9a6 6 0 0 1 12 0Z" />
                  <circle cx="12" cy="8" r="2" />
                </svg>
                <span className="sr-only">Home</span>
              </button>
            ) : (
              <div className="relative">
                <img
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/stayonbeat-RidfPDW2Ft60rFWdaZTQeIWaDD0sod.png"
                  alt="STAYONBEAT"
                  className="logo-small w-auto"
                  style={{ height: "35.2px", width: "auto" }}
                />
              </div>
            )}
          </Link>

          {/* Login button (right side) - only visible when not collapsed */}
          {!isCollapsed && (
            <div style={{ padding: "2px" }}>
              <button
                className="h-10 w-10 rounded-full flex items-center justify-center relative overflow-hidden backdrop-blur-sm border-2 border-[#00ffff] transition-all duration-300 shadow-lg"
                style={{
                  background:
                    "radial-gradient(circle at 30% 30%, rgba(0, 255, 255, 0.2), rgba(0, 0, 0, 0.8) 60%, transparent 70%)",
                  boxShadow:
                    "inset 0 0 15px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.3)",
                  backdropFilter: "blur(4px)",
                  transform: "translateZ(0)",
                  width: "40px",
                  height: "40px",
                }}
                onClick={navigateNext}
              >
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: "radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 25%)",
                    transform: "translateZ(5px)",
                  }}
                ></div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white neon-text-cyan"
                  style={{ transform: "rotate(180deg)" }}
                >
                  <path d="m11 17-5-5 5-5"></path>
                  <path d="m18 17-5-5 5-5"></path>
                </svg>
                <span className="sr-only">FF</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </React.Fragment>
  )
}
