'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { PageTransition } from '@/components/page-transition';
import { FixedNavigationDock } from '@/components/FixedNavigationDock';
import { FixedAdminButton } from '@/components/FixedAdminButton';
import { AdminsBigDock } from '@/components/admin/admins-big-dock';
import { FooterWrapper } from '@/components/FooterWrapper';
import { Header } from '@/components/header';

interface ClientLayoutWrapperProps {
  children: React.ReactNode;
}

export function ClientLayoutWrapper({ children }: ClientLayoutWrapperProps) {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith('/admin');

  return (
    <PageTransition>
      <Header />
      {!isAdminRoute && <FixedNavigationDock />}
      <main className="flex-1 bg-black min-h-screen">
        {children}
      </main>
      <FooterWrapper />
      {!isAdminRoute && <FixedAdminButton />}
    </PageTransition>
  );
}