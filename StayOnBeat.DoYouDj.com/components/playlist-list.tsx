"use client"

import { useState, useEffect } from "react"
import { fetchAllPlaylists, fetchPlaylistsByVisibility } from "@/lib/m3u8-generator"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Music, Play, Download } from "lucide-react"
import Link from "next/link"
import { downloadM3U8FromSupabase } from "@/lib/m3u8-generator"

interface PlaylistListProps {
  visibilityFilter?: "public" | "private" | "admin" | "all"
}

export function PlaylistList({ visibilityFilter = "public" }: PlaylistListProps) {
  const [playlists, setPlaylists] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadPlaylists() {
      setIsLoading(true)
      let data

      if (visibilityFilter === "all") {
        data = await fetchAllPlaylists()
      } else {
        data = await fetchPlaylistsByVisibility(visibilityFilter)
      }

      setPlaylists(data)
      setIsLoading(false)
    }

    loadPlaylists()
  }, [visibilityFilter])

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-1/2 mb-1" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[120px] w-full rounded-md" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }

  if (playlists.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <Music className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No Playlists Found</h3>
            <p className="text-sm text-gray-500 mt-2">There are no playlists available at the moment.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {playlists.map((playlist) => (
        <Card key={playlist.id} className="overflow-hidden transition-all duration-300 hover:shadow-md">
          <CardHeader>
            <CardTitle>{playlist.name}</CardTitle>
            <CardDescription>{playlist.description || "No description"}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-black/50 rounded-md flex items-center justify-center mb-4">
              {playlist.cover_image ? (
                <img
                  src={playlist.cover_image || "/placeholder.svg"}
                  alt={playlist.name}
                  className="w-full h-full object-cover rounded-md"
                />
              ) : (
                <Music className="h-12 w-12 text-gray-500" />
              )}
            </div>
            <div className="text-sm text-gray-500">
              <p>Created: {new Date(playlist.created_at).toLocaleDateString()}</p>
              {playlist.updated_at && <p>Updated: {new Date(playlist.updated_at).toLocaleDateString()}</p>}
              {playlist.visibility && <p>Visibility: {playlist.visibility}</p>}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => downloadM3U8FromSupabase(playlist.id)}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Link href={`/player/supabase/${playlist.id}`} className="flex-1">
              <Button className="w-full">
                <Play className="h-4 w-4 mr-2" />
                Play
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
