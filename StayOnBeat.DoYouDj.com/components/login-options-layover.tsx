"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { SocialLoginCard } from "@/components/auth/social-login-card"

interface LoginOptionsLayoverProps {
  onOptionSelect: (option: "guest" | "login" | "connect" | "register") => void
}

export function LoginOptionsLayover({ onOptionSelect }: LoginOptionsLayoverProps) {
  const router = useRouter()
  const [isExiting, setIsExiting] = useState(false)
  const [activeTab, setActiveTab] = useState<"connect" | "login" | "register">("connect")

  const handleOptionSelect = (option: "guest" | "login" | "connect" | "register") => {
    setIsExiting(true)
    setActiveTab(option === "register" ? "login" : option as any)
    setTimeout(() => {
      onOptionSelect(option)
    }, 300)
  }

  const handleAuthSuccess = (userData: any) => {
    // Handle successful authentication
    router.push("/profile")
  }

  const handleAuthError = (error: Error) => {
    console.error("Authentication error:", error)
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-md rounded-lg bg-card p-8 shadow-lg"
      >
        <h2 className="mb-6 text-center text-2xl font-bold">Welcome to StayOnBeat</h2>
        <div className="space-y-4">
          <Button
            onClick={() => handleOptionSelect("connect")}
            className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Connect
          </Button>
          <Button
            onClick={() => handleOptionSelect("login")}
            className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Login
          </Button>
          <Button
            onClick={() => handleOptionSelect("register")}
            className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Register
          </Button>
          <div className="mt-4">
            <SocialLoginCard
              initialTab={activeTab}
              onSuccess={handleAuthSuccess}
              onError={handleAuthError}
            />
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
