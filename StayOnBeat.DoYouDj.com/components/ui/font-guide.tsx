'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Orbitron, 
  Tilt_Neon, 
  <PERSON><PERSON><PERSON>_Glitch, 
  <PERSON>ubi<PERSON>_Burned,
  Audiowide,
  Syncopate,
  Chakra_Petch,
  Turret_Road,
  Michroma,
  Rajdhani,
  Exo_2,
  Bai_Jamjuree,
  Syne_Mono,
  Monoton,
  Poiret_One,
  Major_Mono_Display
} from 'next/font/google'

// Current fonts
const orbitron = Orbitron({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-orbitron',
})

const tiltNeon = Tilt_Neon({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-tilt-neon',
})

const rubikGlitch = Rubik_Glitch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-glitch',
  weight: ['400'],
})

const rubikBurned = Rubik_Burned({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik-burned',
  weight: ['400'],
})

// Suggested fonts
const audiowide = Audiowide({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-audiowide',
  weight: ['400'],
})

const syncopate = Syncopate({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syncopate',
  weight: ['400', '700'],
})

const chakraPetch = Chakra_Petch({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-chakra-petch',
  weight: ['400', '700'],
})

const turretRoad = Turret_Road({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-turret-road',
  weight: ['400', '700'],
})

const michroma = Michroma({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-michroma',
  weight: ['400'],
})

const rajdhani = Rajdhani({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rajdhani',
  weight: ['400', '700'],
})

const exo2 = Exo_2({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-exo2',
  weight: ['400', '700'],
})

const baiJamjuree = Bai_Jamjuree({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-bai-jamjuree',
  weight: ['400', '700'],
})

const syneMono = Syne_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-syne-mono',
  weight: ['400'],
})

const monoton = Monoton({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-monoton',
  weight: ['400'],
})

const poiretOne = Poiret_One({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poiret-one',
  weight: ['400'],
})

const majorMonoDisplay = Major_Mono_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-major-mono-display',
  weight: ['400'],
})

// Font categories
const currentFonts = [
  { name: 'Orbitron', font: orbitron, variable: '--font-orbitron', weights: ['400', '500', '600', '700', '800', '900'] },
  { name: 'Tilt Neon', font: tiltNeon, variable: '--font-tilt-neon', weights: ['400'] },
  { name: 'Rubik Glitch', font: rubikGlitch, variable: '--font-rubik-glitch', weights: ['400'] },
  { name: 'Rubik Burned', font: rubikBurned, variable: '--font-rubik-burned', weights: ['400'] },
]

const suggestedFonts = [
  { name: 'Audiowide', font: audiowide, variable: '--font-audiowide', weights: ['400'] },
  { name: 'Syncopate', font: syncopate, variable: '--font-syncopate', weights: ['400', '700'] },
  { name: 'Chakra Petch', font: chakraPetch, variable: '--font-chakra-petch', weights: ['400', '700'] },
  { name: 'Turret Road', font: turretRoad, variable: '--font-turret-road', weights: ['400', '700'] },
  { name: 'Michroma', font: michroma, variable: '--font-michroma', weights: ['400'] },
  { name: 'Rajdhani', font: rajdhani, variable: '--font-rajdhani', weights: ['400', '700'] },
  { name: 'Exo 2', font: exo2, variable: '--font-exo2', weights: ['400', '700'] },
  { name: 'Bai Jamjuree', font: baiJamjuree, variable: '--font-bai-jamjuree', weights: ['400', '700'] },
  { name: 'Syne Mono', font: syneMono, variable: '--font-syne-mono', weights: ['400'] },
  { name: 'Monoton', font: monoton, variable: '--font-monoton', weights: ['400'] },
  { name: 'Poiret One', font: poiretOne, variable: '--font-poiret-one', weights: ['400'] },
  { name: 'Major Mono Display', font: majorMonoDisplay, variable: '--font-major-mono-display', weights: ['400'] },
]

export function FontGuide() {
  const [glowEffect, setGlowEffect] = useState(true)
  
  return (
    <div className={`
      ${orbitron.variable} 
      ${tiltNeon.variable} 
      ${rubikGlitch.variable} 
      ${rubikBurned.variable}
      ${audiowide.variable}
      ${syncopate.variable}
      ${chakraPetch.variable}
      ${turretRoad.variable}
      ${michroma.variable}
      ${rajdhani.variable}
      ${exo2.variable}
      ${baiJamjuree.variable}
      ${syneMono.variable}
      ${monoton.variable}
      ${poiretOne.variable}
      ${majorMonoDisplay.variable}
    `}>
      <Card className="bg-black/80 border border-[#00ffff]/30 shadow-[0_0_10px_#00ffff]">
        <CardHeader>
          <CardTitle className="text-[#00ffff]">StayOnBeat Font Guide</CardTitle>
          <CardDescription>Google Fonts for neon/cyberpunk aesthetic</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="current">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="current">Current Fonts</TabsTrigger>
              <TabsTrigger value="suggested">Suggested Fonts</TabsTrigger>
            </TabsList>
            
            <TabsContent value="current">
              <div className="space-y-6">
                {currentFonts.map((fontInfo) => (
                  <div key={fontInfo.name} className="space-y-2">
                    <h3 className="text-lg font-bold text-[#00ffff]">{fontInfo.name}</h3>
                    <p className="text-sm text-gray-400">CSS Variable: {fontInfo.variable}</p>
                    <div className="space-y-2">
                      {fontInfo.weights.map((weight) => (
                        <div key={`${fontInfo.name}-${weight}`} className="p-2 border border-gray-800 rounded">
                          <p 
                            style={{ fontFamily: `var(${fontInfo.variable})`, fontWeight: weight }}
                            className={`text-xl ${glowEffect ? 'text-[#00ffff] drop-shadow-[0_0_5px_#00ffff]' : 'text-white'}`}
                          >
                            {fontInfo.name} {weight} - The quick brown fox jumps over the lazy dog
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="suggested">
              <div className="space-y-6">
                {suggestedFonts.map((fontInfo) => (
                  <div key={fontInfo.name} className="space-y-2">
                    <h3 className="text-lg font-bold text-[#00ffff]">{fontInfo.name}</h3>
                    <p className="text-sm text-gray-400">CSS Variable: {fontInfo.variable}</p>
                    <div className="space-y-2">
                      {fontInfo.weights.map((weight) => (
                        <div key={`${fontInfo.name}-${weight}`} className="p-2 border border-gray-800 rounded">
                          <p 
                            style={{ fontFamily: `var(${fontInfo.variable})`, fontWeight: weight }}
                            className={`text-xl ${glowEffect ? 'text-[#00ffff] drop-shadow-[0_0_5px_#00ffff]' : 'text-white'}`}
                          >
                            {fontInfo.name} {weight} - The quick brown fox jumps over the lazy dog
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
