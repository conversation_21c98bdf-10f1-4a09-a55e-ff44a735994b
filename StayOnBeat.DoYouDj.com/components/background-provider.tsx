"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { getDefaultImage } from "@/lib/image-utils"

interface BackgroundProviderProps {
  children: React.ReactNode
}

export function BackgroundProvider({ children }: BackgroundProviderProps) {
  const [backgroundUrl, setBackgroundUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let isMounted = true

    // First check localStorage for immediate display
    try {
      const cachedUrl = localStorage.getItem("backgroundImage")
      if (cachedUrl && isMounted) {
        setBackgroundUrl(cachedUrl)
        setIsLoading(false)
      }
    } catch (e) {
      console.error("Error accessing localStorage:", e)
    }

    // Then fetch from database for the latest
    async function fetchBackground() {
      try {
        // Safely get the background URL
        let url: string | null = null

        try {
          url = await getDefaultImage("background")
        } catch (fetchError) {
          console.error("Error in getDefaultImage:", fetchError)
        }

        // Only update state if component is still mounted
        if (!isMounted) return

        if (url) {
          setBackgroundUrl(url)
          try {
            localStorage.setItem("backgroundImage", url)
          } catch (e) {
            console.error("Error setting localStorage:", e)
          }
        } else {
          // Clear localStorage if no background is set in the database
          try {
            localStorage.removeItem("backgroundImage")
          } catch (e) {
            console.error("Error removing from localStorage:", e)
          }
        }
      } catch (error) {
        console.error("Error in fetchBackground:", error)
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchBackground()

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false
    }
  }, [])

  return (
    <div className="min-h-screen relative">
      {/* Show loading state */}
      {isLoading && <div className="absolute inset-0 z-[-1] bg-gradient-to-b from-black via-purple-900/30 to-black" />}

      {/* Show background image if available */}
      {!isLoading && backgroundUrl && (
        <div
          className="absolute inset-0 z-[-1] bg-cover bg-center bg-no-repeat opacity-70"
          style={{ backgroundImage: `url('${backgroundUrl}')` }}
        />
      )}

      {/* Fallback gradient if no background */}
      {!isLoading && !backgroundUrl && (
        <div className="absolute inset-0 z-[-1] bg-gradient-to-b from-black via-purple-900/30 to-black" />
      )}

      {/* Add grid pattern overlay */}
      <div className="absolute inset-0 z-[-1] bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDIiPjwvcmVjdD4KPC9zdmc+')] opacity-20"></div>

      {/* Content */}
      <div className="relative z-0">{children}</div>
    </div>
  )
}
