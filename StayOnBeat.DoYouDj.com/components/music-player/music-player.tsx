"use client"

import { usePlayer } from "@/contexts/music-player-context"
import { formatDuration } from "@/lib/utils"
import { Play, Pause, SkipB<PERSON>, SkipForward, Volume2, VolumeX } from "lucide-react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"

export function MusicPlayer() {
  const { state, play, pause, next, previous, setVolume, setProgress } = usePlayer()

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0])
  }

  const handleProgressChange = (value: number[]) => {
    setProgress(value[0])
  }

  if (!state.currentTrack) {
    return (
      <div className="flex h-24 items-center justify-center rounded-lg border bg-background p-4">
        <p className="text-muted-foreground">No track selected</p>
      </div>
    )
  }

  return (
    <div className="flex h-24 items-center justify-between rounded-lg border bg-background p-4">
      <div className="flex items-center space-x-4">
        <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
          <Image
            src={state.currentTrack.artwork_url}
            alt={`${state.currentTrack.title} artwork`}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col">
          <span className="font-medium">{state.currentTrack.title}</span>
          <span className="text-sm text-muted-foreground">{state.currentTrack.artist}</span>
        </div>
      </div>

      <div className="flex flex-1 flex-col items-center space-y-2 px-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={previous}>
            <SkipBack className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={state.isPlaying ? pause : play}>
            {state.isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          <Button variant="ghost" size="icon" onClick={next}>
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex w-full items-center space-x-2">
          <span className="text-xs text-muted-foreground">
            {formatDuration(state.progress)}
          </span>
          <Slider
            value={[state.progress]}
            max={state.duration}
            step={1}
            onValueChange={handleProgressChange}
            className="flex-1"
          />
          <span className="text-xs text-muted-foreground">
            {formatDuration(state.duration)}
          </span>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setVolume(state.volume === 0 ? 1 : 0)}
        >
          {state.volume === 0 ? (
            <VolumeX className="h-4 w-4" />
          ) : (
            <Volume2 className="h-4 w-4" />
          )}
        </Button>
        <Slider
          value={[state.volume]}
          max={1}
          step={0.1}
          onValueChange={handleVolumeChange}
          className="w-24"
        />
      </div>
    </div>
  )
}
