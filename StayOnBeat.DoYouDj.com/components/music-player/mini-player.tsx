"use client"
import { useMusicPlayer } from "@/contexts/music-player-context"
import { But<PERSON> } from "@/components/ui/button"
import { Play, Pause, SkipForward, Music } from "lucide-react"
import Image from "next/image"

export function MiniPlayer() {
  const { playerState, togglePlay, next } = useMusicPlayer()
  const { currentTrack, isPlaying, progress, duration } = playerState

  if (!currentTrack) return null

  const progressPercentage = duration > 0 ? (progress / duration) * 100 : 0

  return (
    <div className="flex items-center gap-3 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <div className="relative h-10 w-10 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700">
        {currentTrack.artwork_url ? (
          <Image
            src={currentTrack.artwork_url || "/placeholder.svg"}
            alt={currentTrack.title}
            fill
            className="object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full w-full">
            <Music className="h-5 w-5 text-gray-400" />
          </div>
        )}
      </div>

      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{currentTrack.title}</div>
        <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{currentTrack.artist}</div>
        <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-1 overflow-hidden">
          <div className="h-full bg-green-500 rounded-full" style={{ width: `${progressPercentage}%` }}></div>
        </div>
      </div>

      <div className="flex items-center gap-1">
        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={togglePlay}>
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </Button>
        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={next}>
          <SkipForward className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
