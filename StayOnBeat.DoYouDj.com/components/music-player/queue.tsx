"use client"
import { useMusicPlayer } from "@/contexts/music-player-context"
import { Button } from "@/components/ui/button"
import { X, Music, Clock } from "lucide-react"
import Image from "next/image"
import { formatTime } from "./music-player"

export function Queue() {
  const { playerState, play, removeFromQueue, clearQueue } = useMusicPlayer()
  const { currentTrack, playlist } = playerState

  if (!playlist.length) {
    return (
      <div className="p-8 text-center">
        <p className="text-gray-500">Queue is empty</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Queue</h3>
        <Button variant="outline" size="sm" onClick={clearQueue}>
          Clear Queue
        </Button>
      </div>

      <div className="grid grid-cols-12 gap-4 px-4 py-2 border-b border-gray-200 dark:border-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400">
        <div className="col-span-1">#</div>
        <div className="col-span-5">Title</div>
        <div className="col-span-3">Artist</div>
        <div className="col-span-2 text-right">
          <Clock className="inline-block h-4 w-4" />
        </div>
        <div className="col-span-1"></div>
      </div>

      {playlist.map((track, index) => {
        const isCurrentTrack = currentTrack?.id === track.id

        return (
          <div
            key={track.id}
            className={`grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors ${
              isCurrentTrack ? "bg-gray-100 dark:bg-gray-800" : ""
            }`}
          >
            <div className="col-span-1 flex items-center">
              <span className="text-gray-500">{index + 1}</span>
            </div>

            <div className="col-span-5 flex items-center gap-3">
              <div className="relative h-10 w-10 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                {track.artwork_url ? (
                  <Image
                    src={track.artwork_url || "/placeholder.svg"}
                    alt={track.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full w-full">
                    <Music className="h-5 w-5 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="truncate">
                <div className="font-medium truncate">{track.title}</div>
              </div>
            </div>

            <div className="col-span-3 flex items-center">
              <span className="truncate">{track.artist}</span>
            </div>

            <div className="col-span-2 flex items-center justify-end">
              <span className="text-gray-500">{track.duration ? formatTime(track.duration) : "--:--"}</span>
            </div>

            <div className="col-span-1 flex items-center justify-end">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => removeFromQueue(track.id)}
                className="h-8 w-8 text-gray-500 hover:text-red-500"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )
      })}
    </div>
  )
}
