"use client"
import { useMusicPlayer, type Track } from "@/contexts/music-player-context"
import { Button } from "@/components/ui/button"
import { Plus, Music, Clock, MoreHorizontal } from "lucide-react"
import Image from "next/image"
import { formatTime } from "./music-player"

interface TrackListProps {
  tracks?: Track[]
  showAddToQueue?: boolean
}

export function TrackList({ tracks, showAddToQueue = true }: TrackListProps) {
  const { playerState, play, addToQueue } = useMusicPlayer()
  const { currentTrack, isPlaying, playlist } = playerState

  // Use provided tracks or fall back to the playlist from context
  const displayTracks = tracks || playlist

  if (!displayTracks.length) {
    return (
      <div className="p-8 text-center">
        <p className="text-gray-500">No tracks available</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-12 gap-4 px-4 py-2 border-b border-gray-200 dark:border-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400">
        <div className="col-span-1">#</div>
        <div className="col-span-5">Title</div>
        <div className="col-span-3">Artist</div>
        <div className="col-span-2 text-right">
          <Clock className="inline-block h-4 w-4" />
        </div>
        <div className="col-span-1"></div>
      </div>

      {displayTracks.map((track, index) => {
        const isCurrentTrack = currentTrack?.id === track.id

        return (
          <div
            key={track.id}
            className={`grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors ${
              isCurrentTrack ? "bg-gray-100 dark:bg-gray-800" : ""
            }`}
          >
            <div className="col-span-1 flex items-center">
              {isCurrentTrack && isPlaying ? (
                <div className="w-5 h-5 flex items-center justify-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                </div>
              ) : (
                <span className="text-gray-500">{index + 1}</span>
              )}
            </div>

            <div className="col-span-5 flex items-center gap-3">
              <div className="relative h-10 w-10 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                {track.artwork_url ? (
                  <Image
                    src={track.artwork_url || "/placeholder.svg"}
                    alt={track.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full w-full">
                    <Music className="h-5 w-5 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="truncate">
                <div className="font-medium truncate">{track.title}</div>
              </div>
            </div>

            <div className="col-span-3 flex items-center">
              <span className="truncate">{track.artist}</span>
            </div>

            <div className="col-span-2 flex items-center justify-end">
              <span className="text-gray-500">{track.duration ? formatTime(track.duration) : "--:--"}</span>
            </div>

            <div className="col-span-1 flex items-center justify-end">
              {showAddToQueue ? (
                <Button variant="ghost" size="icon" onClick={() => addToQueue(track)} className="h-8 w-8">
                  <Plus className="h-4 w-4" />
                </Button>
              ) : (
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
