"use client"

import { useState } from "react"
import { UploadButton } from "@uploadthing/react"
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Trash2 } from "lucide-react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"
import { saveImageReference, type ImageCategory } from "@/lib/image-utils"

interface ImageUploaderProps {
  category: ImageCategory
  onUploadComplete?: (url: string) => void
  onDelete?: () => void
  existingImageUrl?: string
  imageName?: string
}

export function ImageUploader({
  category,
  onUploadComplete,
  onDelete,
  existingImageUrl,
  imageName = "image",
}: ImageUploaderProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(existingImageUrl || null)
  const [isUploading, setIsUploading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Map category to endpoint
  const getEndpoint = (): keyof OurFileRouter => {
    switch (category) {
      case "background":
        return "backgroundImage"
      case "profile":
        return "profileImage"
      case "logo":
        return "logoImage"
      default:
        return "imageUploader"
    }
  }

  const handleDelete = async () => {
    if (!onDelete) return

    setIsDeleting(true)
    try {
      await onDelete()
      setImageUrl(null)
      toast({
        title: "Image deleted",
        description: `The ${imageName} has been deleted successfully.`,
      })
    } catch (error) {
      toast({
        title: "Delete failed",
        description: `Failed to delete the ${imageName}.`,
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-4">
      <UploadButton<OurFileRouter>
        endpoint={getEndpoint()}
        onUploadBegin={() => {
          setIsUploading(true)
        }}
        onClientUploadComplete={async (res) => {
          setIsUploading(false)
          if (res && res[0]) {
            const url = res[0].url
            setImageUrl(url)

            // Save reference to database
            await saveImageReference(url, category, imageName)

            if (onUploadComplete) onUploadComplete(url)

            toast({
              title: "Upload complete",
              description: `Your ${imageName} has been uploaded successfully.`,
            })
          }
        }}
        onUploadError={(error: Error) => {
          setIsUploading(false)
          toast({
            title: "Upload failed",
            description: error.message,
            variant: "destructive",
          })
        }}
      />

      {imageUrl && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm text-gray-500">Preview:</p>
            {onDelete && (
              <Button variant="destructive" size="sm" onClick={handleDelete} disabled={isDeleting}>
                <Trash2 className="h-4 w-4 mr-1" />
                {isDeleting ? "Deleting..." : "Delete"}
              </Button>
            )}
          </div>
          <div className="relative h-40 w-full overflow-hidden rounded-md border">
            <img
              src={imageUrl || "/placeholder.svg"}
              alt={`Uploaded ${imageName}`}
              className="h-full w-full object-cover"
            />
          </div>
        </div>
      )}
    </div>
  )
}
