"use client"

import { motion, AnimatePresence } from "framer-motion"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import type { ReactNode } from "react"

interface PageTransitionProps {
  children: ReactNode
}

export function PageTransition({ children }: PageTransitionProps) {
  const pathname = usePathname()
  const [direction, setDirection] = useState("right")
  const [isInitialRender, setIsInitialRender] = useState(true)
  const [underscores, setUnderscores] = useState<
    Array<{
      id: number
      x: number
      y: number
      delay: number
      periods: string
      hasPeriods: boolean
      opacity: number
      fontSize: number
    }>
  >([])

  // Get transition direction from localStorage (set by NavigationDock)
  useEffect(() => {
    // Check if we have a stored navigation direction
    const storedDirection = localStorage.getItem("navigationDirection")
    if (storedDirection) {
      setDirection(storedDirection)
    }

    // Generate random periods for each underscore
    const generatePeriods = () => {
      const count = Math.floor(Math.random() * 5) + 1 // 1-5 periods
      return Array(count).fill(".").join(" ")
    }

    // Generate random underscores - 60 total
    const newUnderscores = Array.from({ length: 60 }, (_, i) => {
      // First 30 have periods, next 30 don't
      const hasPeriods = i < 30
      return {
        id: i,
        x: Math.random() * 100, // random x position (percentage)
        y: Math.random() * 100, // random y position (percentage)
        delay: Math.random() * 0.5, // random delay for staggered appearance
        periods: hasPeriods ? generatePeriods() : "", // random periods for first 30
        hasPeriods: hasPeriods,
        opacity: Math.random() * 0.5 + 0.3, // random opacity between 0.3 and 0.8
        fontSize: Math.floor(Math.random() * 9) + 8, // random font size between 4px and 14px
      }
    })

    setUnderscores(newUnderscores)
    setIsInitialRender(false)
  }, [pathname])

  return (
    <div className="relative flex flex-col min-h-screen bg-black">
      <AnimatePresence mode="wait">
        <motion.div
          key={pathname}
          initial={isInitialRender ? false : {
            opacity: 0,
            x: direction === "right" ? "100%" : "-100%",
          }}
          animate={{
            opacity: 1,
            x: 0,
          }}
          exit={{
            opacity: 0,
            x: direction === "right" ? "-100%" : "100%",
          }}
          transition={{
            duration: 0.5,
            ease: "easeInOut",
          }}
          className="w-full flex-1"
        >
          {children}

          {/* Underscore overlay */}
          <div className="fixed inset-0 pointer-events-none z-40 overflow-hidden">
            {underscores.map((underscore) => (
              <motion.div
                key={underscore.id}
                className="absolute font-mono"
                style={{
                  left: `${underscore.x}%`,
                  top: `${underscore.y}%`,
                  fontSize: `${underscore.fontSize}px`,
                }}
                initial={{ opacity: 0, x: direction === "right" ? -20 : 20 }}
                animate={{
                  opacity: [0, underscore.opacity, 0],
                  x: direction === "right" ? [-20, 0, 20] : [20, 0, -20],
                }}
                transition={{
                  duration: 1.7,
                  delay: underscore.delay,
                  times: [0, 0.5, 1],
                }}
              >
                {/* Underscore with neon turquoise glow */}
                <span
                  className="text-[#00ffff]"
                  style={{
                    opacity: underscore.opacity,
                    textShadow: "0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff",
                    animation: "shimmer 1.95s infinite alternate",
                  }}
                >
                  ___
                </span>

                {/* Periods with white transparent glow - only for first 30 */}
                {underscore.hasPeriods && (
                  <span
                    className="text-white/70 ml-1"
                    style={{
                      textShadow: "0 0 3px rgba(255, 255, 255, 0.5)",
                    }}
                  >
                    {underscore.periods}
                  </span>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
