'use client'

import { usePathname } from 'next/navigation'
import { AuthButtons } from './auth/auth-buttons'
import Link from 'next/link'

export function Header() {
  const pathname = usePathname()
  const isAdminPage = pathname?.startsWith('/admin')

  if (isAdminPage) {
    return null
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 flex justify-between items-center p-4 bg-black/50 backdrop-blur-md">
      <Link href="/" className="flex items-center">
        <img
          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/stayonbeat-RidfPDW2Ft60rFWdaZTQeIWaDD0sod.png"
          alt="STAYONBEAT"
          className="h-8 w-auto"
        />
        <span className="ml-2 text-[#00ffff] font-bold text-lg" style={{ fontFamily: "'Tilt Neon', cursive" }}>
          StayOnBeat
        </span>
      </Link>
      <AuthButtons />
    </header>
  )
}
