"use client"

import { useMotionValue, motion, useMotionTemplate } from "motion/react"
import type React from "react"
import { type MouseEvent as ReactMouseEvent, useState, useRef, useEffect } from "react"
import { CanvasRevealEffect } from "@/components/redcardspot/canvas-reveal-effect"
import { cn } from "@/lib/utils"

export const CardSpotlight = ({
  children,
  color = "#262626",
  className,
  ...props
}: {
  color?: string
  children?: React.ReactNode
} & React.HTMLAttributes<HTMLDivElement>) => {
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  const cardRef = useRef<HTMLDivElement>(null)
  const [radius, setRadius] = useState(350)
  const [isHovering, setIsHovering] = useState(false)

  // Update radius based on card size
  useEffect(() => {
    if (cardRef.current) {
      const { width, height } = cardRef.current.getBoundingClientRect()
      const newRadius = Math.max(width, height) * 0.6 // Adjust radius based on card size
      setRadius(newRadius)
    }
  }, [])

  function handleMouseMove({ currentTarget, clientX, clientY }: ReactMouseEvent<HTMLDivElement>) {
    const { left, top } = currentTarget.getBoundingClientRect()
    mouseX.set(clientX - left)
    mouseY.set(clientY - top)
  }

  const handleMouseEnter = () => setIsHovering(true)
  const handleMouseLeave = () => setIsHovering(false)

  return (
    <div
      ref={cardRef}
      className={cn(
        "group/spotlight p-10 rounded-md relative border border-neutral-800 bg-black w-full h-full",
        "transform perspective-1000 rotate-x-1",
        className,
      )}
      style={{
        boxShadow: "0 20px 40px -20px rgba(0,0,0,0.8), inset 0 -1px 0 0 rgba(255,255,255,0.1)",
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      <motion.div
        className="pointer-events-none absolute z-10 -inset-px rounded-md opacity-0 transition duration-300 group-hover/spotlight:opacity-100"
        style={{
          backgroundColor: color,
          maskImage: useMotionTemplate`
            radial-gradient(
              ${radius}px circle at ${mouseX}px ${mouseY}px,
              white,
              transparent 80%
            )
          `,
        }}
      >
        {isHovering && (
          <CanvasRevealEffect
            animationSpeed={5}
            containerClassName="bg-transparent absolute inset-0 pointer-events-none"
            colors={[
              [220, 38, 38], // Red (similar to Tailwind's red-600)
              [185, 28, 28], // Darker red (similar to Tailwind's red-700)
            ]}
            dotSize={3}
          />
        )}
        {isHovering && (
          <div className="absolute inset-0 bg-red-500/20 mix-blend-overlay rounded-md pointer-events-none" />
        )}
      </motion.div>
      <div className="relative z-20">{children}</div>
    </div>
  )
}
