"use client"

import Card<PERSON><PERSON>lightDemo from "./card-spotlight-demo"
import { Card<PERSON>potlight } from "./card-spotlight"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "./card"

export default function RedCardSpotDemo() {
  return (
    <div className="min-h-screen bg-black p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">Red Card Spotlight Demo</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Empty demo */}
          <div className="flex flex-col space-y-4">
            <h2 className="text-xl font-semibold text-white">Empty Spotlight Card</h2>
            <CardSpotlightDemo />
          </div>

          {/* Demo with content */}
          <div className="flex flex-col space-y-4">
            <h2 className="text-xl font-semibold text-white">Spotlight Card with Content</h2>
            <CardSpotlight className="w-full h-full max-w-[600px] max-h-[500px] min-h-[500px]">
              <Card className="w-full h-full bg-transparent border-none shadow-none">
                <CardHeader>
                  <CardTitle className="text-white">Featured Artist</CardTitle>
                  <CardDescription className="text-gray-400">Discover new music</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center h-64">
                    <div className="w-32 h-32 rounded-full bg-red-500/30 flex items-center justify-center mb-4">
                      <span className="text-white text-5xl">🎵</span>
                    </div>
                    <h3 className="text-xl font-bold text-white">Artist Name</h3>
                    <p className="text-gray-400 mt-2">Genre: Electronic</p>
                  </div>
                </CardContent>
                <CardFooter className="justify-between">
                  <button className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Play Now
                  </button>
                  <button className="px-4 py-2 bg-transparent border border-red-600 text-white rounded-md hover:bg-red-900/20 transition-colors">
                    Add to Queue
                  </button>
                </CardFooter>
              </Card>
            </CardSpotlight>
          </div>
        </div>
      </div>
    </div>
  )
}
