"use client"

import React from "react"
import { useDroppable } from "@dnd-kit/core"
import { SortableQueueList } from "./sortable-queue-list"
import { QueueCardItem } from "./queue-card"

interface QueueDroppableProps {
  items: QueueCardItem[]
  onReorder: (items: QueueCardItem[]) => void
  expandedItems: Record<string | number, boolean>
  onExpand: (id: number) => void
  isUnlockMode: boolean
  emptyMessage?: string
  className?: string
}

export function QueueDroppable({
  items,
  onReorder,
  expandedItems,
  onExpand,
  isUnlockMode,
  emptyMessage = "Queue is empty",
  className
}: QueueDroppableProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: 'queue-droppable',
  })

  // Log for debugging
  console.log('QueueDroppable rendering with:', {
    itemsCount: items.length,
    isOver,
    isUnlockMode
  })

  return (
    <div
      ref={setNodeRef}
      className={`${className || ''} transition-all duration-300 ${
        isOver
          ? 'bg-purple-900/20 border-2 border-dashed border-purple-500/50 rounded-lg p-2 shadow-[0_0_15px_rgba(147,51,234,0.4)]'
          : ''
      }`}
    >
      {isOver && items.length === 0 && (
        <div className="text-center py-8 text-purple-400">
          <p>Drop here to add to queue</p>
        </div>
      )}

      <SortableQueueList
        items={items}
        onReorder={onReorder}
        expandedItems={expandedItems}
        onExpand={onExpand}
        isUnlockMode={isUnlockMode}
        emptyMessage={emptyMessage}
      />
    </div>
  )
}
