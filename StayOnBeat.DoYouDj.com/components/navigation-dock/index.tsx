"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

export function NavigationDock() {
  const pathname = usePathname()

  return (
    <nav className="fixed top-0 left-0 right-0 z-[9999] bg-black/80 backdrop-blur-sm border-b border-[#00ffff]/20">
      <div className="container mx-auto px-4 max-w-[81%] md:max-w-[725px] relative">
        <div className="flex items-center justify-between h-8">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img
              src="/images/sob-logo-square.png"
              alt="StayOnBeat"
              className="h-4 w-4 object-contain"
            />
            <span className="ml-2 text-white text-[5px] font-neon neon-text-small">StayOnBeat</span>
          </Link>

          {/* Navigation Links */}
          <div className="flex space-x-6">
            <Link
              href="/"
              className={`text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small ${
                pathname === "/" ? "text-[#00ffff]" : ""
              }`}
            >
              Home
            </Link>
            <Link
              href="/submit"
              className={`text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small ${
                pathname === "/submit" ? "text-[#00ffff]" : ""
              }`}
            >
              Submit
            </Link>
            <Link
              href="/queue"
              className={`text-[5px] text-[#00ffff]/70 hover:text-[#00ffff] transition-colors neon-text-small ${
                pathname === "/queue" ? "text-[#00ffff]" : ""
              }`}
            >
              Queue
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}