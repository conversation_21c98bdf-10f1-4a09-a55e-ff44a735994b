"use client"

import { useState } from "react"
import { useSupabasePlayer } from "@/hooks/use-supabase-player"
import { usePlayer } from "@/contexts/music-player-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { UploadButton } from "@uploadthing/react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"

export function TrackUpload() {
  const [title, setTitle] = useState("")
  const [artist, setArtist] = useState("")
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [artworkUrl, setArtworkUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [audioDuration, setAudioDuration] = useState<number | null>(null)
  const [isAudioUploading, setIsAudioUploading] = useState(false)
  const [isArtworkUploading, setIsArtworkUploading] = useState(false)

  const { uploadTrack } = useSupabasePlayer()
  const { addToPlaylist } = usePlayer()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!audioUrl) {
      toast.error("Please upload an audio file")
      return
    }

    if (!artworkUrl) {
      toast.error("Please upload artwork")
      return
    }

    if (!audioDuration) {
      toast.error("Audio file metadata could not be loaded")
      return
    }

    try {
      setIsUploading(true)

      // Upload track to Supabase
      const track = await uploadTrack({
        title,
        artist,
        audio_url: audioUrl,
        artwork_url: artworkUrl,
        duration: audioDuration,
      })

      if (track) {
        addToPlaylist(track)
        toast.success("Track uploaded successfully")
        setTitle("")
        setArtist("")
        setAudioUrl(null)
        setArtworkUrl(null)
        setAudioDuration(null)
      }
    } catch (error) {
      console.error("Error uploading track:", error)
      toast.error("Failed to upload track")
    } finally {
      setIsUploading(false)
    }
  }

  // Helper function to load audio duration
  const loadAudioDuration = async (url: string) => {
    try {
      const audio = new Audio(url)
      return new Promise<number>((resolve, reject) => {
        audio.addEventListener("loadedmetadata", () => {
          resolve(audio.duration)
        })
        audio.addEventListener("error", (err) => {
          reject(err)
        })
      })
    } catch (error) {
      console.error("Error loading audio duration:", error)
      throw error
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter track title"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="artist">Artist</Label>
        <Input
          id="artist"
          value={artist}
          onChange={(e) => setArtist(e.target.value)}
          placeholder="Enter artist name"
          required
        />
      </div>

      <div className="space-y-2">
        <Label>Audio File</Label>
        <div className="border rounded-md p-4 bg-black/20">
          {audioUrl ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm text-green-500">✓ Audio uploaded successfully</div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setAudioUrl(null)
                    setAudioDuration(null)
                  }}
                >
                  Change
                </Button>
              </div>
              <audio controls className="w-full">
                <source src={audioUrl} />
                Your browser does not support the audio element.
              </audio>
            </div>
          ) : (
            <UploadButton<OurFileRouter>
              endpoint="audioUploader"
              onUploadBegin={() => {
                setIsAudioUploading(true)
              }}
              onClientUploadComplete={async (res) => {
                setIsAudioUploading(false)
                if (res && res[0]) {
                  const url = res[0].url
                  setAudioUrl(url)

                  try {
                    const duration = await loadAudioDuration(url)
                    setAudioDuration(duration)
                  } catch (error) {
                    console.error("Error loading audio duration:", error)
                    toast.error("Could not load audio metadata")
                  }

                  toast.success("Audio uploaded successfully")
                }
              }}
              onUploadError={(error: Error) => {
                setIsAudioUploading(false)
                toast.error(`Error uploading audio: ${error.message}`)
              }}
              className="ut-button:bg-purple-600 ut-button:ut-readying:bg-purple-500/50"
            />
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label>Artwork</Label>
        <div className="border rounded-md p-4 bg-black/20">
          {artworkUrl ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm text-green-500">✓ Artwork uploaded successfully</div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setArtworkUrl(null)}
                >
                  Change
                </Button>
              </div>
              <div className="relative h-40 w-full overflow-hidden rounded-md border">
                <img
                  src={artworkUrl}
                  alt="Track artwork"
                  className="h-full w-full object-cover"
                />
              </div>
            </div>
          ) : (
            <UploadButton<OurFileRouter>
              endpoint="artworkUploader"
              onUploadBegin={() => {
                setIsArtworkUploading(true)
              }}
              onClientUploadComplete={(res) => {
                setIsArtworkUploading(false)
                if (res && res[0]) {
                  setArtworkUrl(res[0].url)
                  toast.success("Artwork uploaded successfully")
                }
              }}
              onUploadError={(error: Error) => {
                setIsArtworkUploading(false)
                toast.error(`Error uploading artwork: ${error.message}`)
              }}
              className="ut-button:bg-purple-600 ut-button:ut-readying:bg-purple-500/50"
            />
          )}
        </div>
      </div>

      <Button
        type="submit"
        disabled={isUploading || isAudioUploading || isArtworkUploading || !audioUrl || !artworkUrl}
        className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
      >
        {isUploading ? "Saving..." : "Save Track"}
      </Button>
    </form>
  )
}