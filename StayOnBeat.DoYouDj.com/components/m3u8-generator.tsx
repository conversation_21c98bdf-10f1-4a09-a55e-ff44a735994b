"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { generateM3U8Playlist, downloadM3U8Playlist } from "@/lib/m3u8-utils"
import { Download, RefreshCw } from "lucide-react"
import { supabase } from "@/lib/supabase"

export function M3U8Generator() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [playlistCount, setPlaylistCount] = useState(0)

  // Fetch playlist count
  const fetchPlaylistCount = async () => {
    try {
      const { count, error } = await supabase.from("playlist").select("*", { count: "exact", head: true })

      if (error) throw error

      setPlaylistCount(count || 0)
    } catch (error) {
      console.error("Error fetching playlist count:", error)
    }
  }

  useEffect(() => {
    fetchPlaylistCount()
  }, [])

  // Generate and download M3U8 playlist
  const handleGenerateAndDownload = async () => {
    setIsGenerating(true)

    try {
      const m3u8Content = await generateM3U8Playlist()

      if (m3u8Content) {
        downloadM3U8Playlist(m3u8Content, "current_playlist.m3u8")
      } else {
        alert("No playlist items found")
      }
    } catch (error) {
      console.error("Error generating M3U8 playlist:", error)
      alert("Error generating playlist")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>M3U8 Playlist Generator</CardTitle>
        <CardDescription>Generate and download M3U8 playlists from your current queue</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm">
          <p>Current playlist has {playlistCount} items</p>
          <p className="text-gray-500 mt-2">The generated M3U8 file can be used with any HLS-compatible player.</p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={fetchPlaylistCount}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
        <Button onClick={handleGenerateAndDownload} disabled={isGenerating || playlistCount === 0}>
          <Download className="h-4 w-4 mr-2" />
          {isGenerating ? "Generating..." : "Generate & Download"}
        </Button>
      </CardFooter>
    </Card>
  )
}
