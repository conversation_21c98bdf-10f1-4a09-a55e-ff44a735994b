"use client"

import { useState, useEffect } from "react"
import { client as database } from "@/lib/database"
import type { Track } from "@/contexts/music-player-context"
import type { PlaylistItem } from "@/lib/types"

// Hook for loading a specific playlist by ID
export function useDatabasePlayer(playlistId?: string) {
  const [playlist, setPlaylist] = useState<PlaylistItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentTrack, setCurrentTrack] = useState<PlaylistItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load specific playlist
  const loadPlaylist = async (id: string) => {
    try {
      setIsLoading(true)
      setError(null)

      // ALWAYS use real Supabase - NO MOCKS

      // Get playlist items from Supabase
      const { data: playlistItems, error: itemsError } = await supabase
        .from("playlist_items")
        .select(`
          id,
          position,
          songs (
            id,
            title,
            artist_name,
            media_url,
            duration,
            album_name,
            artwork_url
          )
        `)
        .eq("playlist_id", id)
        .order("position", { ascending: true })

      if (itemsError) throw itemsError

      // Convert to PlaylistItem format
      const items: PlaylistItem[] = playlistItems.map((item) => ({
        id: item.id,
        songTitle: item.songs.title,
        artistName: item.songs.artist_name,
        type: 'GA', // Default type, should be stored in the database
        mediaUrl: item.songs.media_url,
        duration: item.songs.duration || 0,
        artworkUrl: item.songs.artwork_url || "/placeholder.svg",
        position: item.position,
        platform: 'unknown', // Should be stored in the database
        url: item.songs.media_url,
      }))

      setPlaylist(items)
      setCurrentTrack(items[0] || null)
    } catch (err) {
      console.error("Error loading playlist:", err)
      setError(err instanceof Error ? err.message : "Failed to load playlist")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle track change
  const handleTrackChange = (index: number) => {
    if (index >= 0 && index < playlist.length) {
      setCurrentIndex(index)
      setCurrentTrack(playlist[index])
    }
  }

  // Refresh playlist
  const refreshPlaylist = () => {
    if (playlistId) {
      loadPlaylist(playlistId)
    }
  }

  // Load playlist when playlistId changes
  useEffect(() => {
    if (playlistId) {
      loadPlaylist(playlistId)
    }
  }, [playlistId])

  // If no playlistId provided, return the general hook functionality
  if (!playlistId) {
    return useSupabasePlayerGeneral()
  }

  return {
    playlist,
    currentIndex,
    currentTrack,
    isLoading,
    error,
    handleTrackChange,
    refreshPlaylist,
  }
}

// General hook for managing tracks and playlists (original functionality)
export function useSupabasePlayerGeneral() {
  const [tracks, setTracks] = useState<Track[]>([])
  const [playlists, setPlaylists] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch tracks
  const fetchTracks = async () => {
    try {
      setLoading(true)

      // Use unified database client

      const { data, error } = await database
        .from("tracks")
        .select("*")
        .order("created_at", { ascending: false })

      if (error) throw error

      const formattedTracks = data.map((track) => ({
        id: track.id,
        title: track.title,
        artist: track.artist,
        artwork_url: track.artwork_url,
        audio_url: track.audio_url,
        duration: track.duration,
        created_at: track.created_at,
        spotify_id: track.spotify_id,
        isrc: track.isrc,
      }))

      setTracks(formattedTracks)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Fetch playlists
  const fetchPlaylists = async () => {
    try {
      setLoading(true)

      // ALWAYS use real Supabase - NO MOCKS

      const { data, error } = await supabase
        .from("playlists")
        .select(`
          *,
          playlist_tracks (
            track_id,
            position,
            tracks (*)
          )
        `)
        .order("created_at", { ascending: false })

      if (error) throw error

      setPlaylists(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Add track to playlist
  const addTrackToPlaylist = async (playlistId: string, trackId: string, position: number) => {
    try {
      const { error } = await supabase.from("playlist_tracks").insert({
        playlist_id: playlistId,
        track_id: trackId,
        position,
      })

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Remove track from playlist
  const removeTrackFromPlaylist = async (playlistId: string, trackId: string) => {
    try {
      const { error } = await supabase
        .from("playlist_tracks")
        .delete()
        .match({ playlist_id: playlistId, track_id: trackId })

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Create new playlist
  const createPlaylist = async (name: string, description?: string, isPublic: boolean = false) => {
    try {
      const { data, error } = await supabase
        .from("playlists")
        .insert({
          name,
          description,
          is_public: isPublic,
        })
        .select()
        .single()

      if (error) throw error

      // Refresh playlists
      await fetchPlaylists()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      return null
    }
  }

  // Upload track
  const uploadTrack = async (track: Omit<Track, "id" | "created_at">) => {
    try {
      const { data, error } = await database.from("tracks").insert(track).select().single()

      if (error) throw error

      // Refresh tracks
      await fetchTracks()
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      return null
    }
  }

  // Delete track
  const deleteTrack = async (trackId: string) => {
    try {
      const { error } = await database.from("tracks").delete().eq("id", trackId)

      if (error) throw error

      // Refresh tracks
      await fetchTracks()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  // Initialize data
  useEffect(() => {
    fetchTracks()
    fetchPlaylists()
  }, [])

  return {
    tracks,
    playlists,
    loading,
    error,
    fetchTracks,
    fetchPlaylists,
    addTrackToPlaylist,
    removeTrackFromPlaylist,
    createPlaylist,
    uploadTrack,
    deleteTrack,
  }
}

// Keep the legacy export for backward compatibility
export { useDatabasePlayer as useSupabasePlayer }
