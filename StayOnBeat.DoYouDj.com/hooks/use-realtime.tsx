"use client"

import { useEffect, useState, useCallback } from "react"
import type { RealtimeChannel, RealtimePostgresChangesPayload } from "@supabase/supabase-js"
import { client as database, hasRealtimeSupport } from "@/lib/database"

type SubscriptionCallback<T> = (payload: RealtimePostgresChangesPayload<T>) => void

export function useRealtimeSubscription<T = any>(
  table: string,
  callback: SubscriptionCallback<T>,
  options?: {
    event?: "INSERT" | "UPDATE" | "DELETE" | "*"
    filter?: string
  },
) {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  // Use a stable reference to the callback to prevent infinite re-renders
  const stableCallback = useCallback(
    (payload: RealtimePostgresChangesPayload<T>) => {
      callback(payload)
    },
    [callback],
  )

  useEffect(() => {
    // Check if realtime is supported
    if (!hasRealtimeSupport()) {
      console.warn("Realtime subscriptions are not available with current database")
      setError(new Error("Realtime subscriptions are not available"))
      return
    }

    // Check if database.channel is available
    if (typeof database.channel !== "function") {
      console.warn("Realtime subscriptions are not available - database.channel is not a function")
      setError(new Error("Realtime subscriptions are not available"))
      return
    }

    try {
      // Create a new channel
      const channelName = `realtime:${table}:${options?.event || "*"}:${options?.filter || "*"}`
      const newChannel = database.channel(channelName)

      // Set up the subscription
      newChannel
        .on(
          "postgres_changes",
          {
            event: options?.event || "*",
            schema: "public",
            table: table,
            filter: options?.filter || undefined,
          },
          (payload) => {
            stableCallback(payload as RealtimePostgresChangesPayload<T>)
          },
        )
        .subscribe((status) => {
          if (status === "SUBSCRIBED") {
            setIsConnected(true)
          } else {
            setIsConnected(false)
          }
        })

      setChannel(newChannel)

      // Clean up the subscription when the component unmounts
      return () => {
        newChannel.unsubscribe()
      }
    } catch (err) {
      console.error("Error setting up realtime subscription:", err)
      setError(err instanceof Error ? err : new Error(String(err)))
    }
  }, [table, options?.event, options?.filter, stableCallback])

  return { isConnected, error }
}

// Similarly update the useRealtimeBroadcast function
export function useRealtimeBroadcast(channelName: string) {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    // Check if realtime is supported
    if (!hasRealtimeSupport()) {
      console.warn("Realtime broadcasting is not available with current database")
      setError(new Error("Realtime broadcasting is not available"))
      return
    }

    // Check if database.channel is available
    if (typeof database.channel !== "function") {
      console.warn("Realtime broadcasting is not available - database.channel is not a function")
      setError(new Error("Realtime broadcasting is not available"))
      return
    }

    try {
      const newChannel = database.channel(channelName)

      newChannel.subscribe((status) => {
        if (status === "SUBSCRIBED") {
          setIsConnected(true)
        } else {
          setIsConnected(false)
        }
      })

      setChannel(newChannel)

      return () => {
        newChannel.unsubscribe()
      }
    } catch (err) {
      console.error("Error setting up realtime broadcast:", err)
      setError(err instanceof Error ? err : new Error(String(err)))
    }
  }, [channelName])

  const broadcast = async (event: string, payload: any) => {
    if (!channel) return false

    try {
      return channel.send({
        type: "broadcast",
        event,
        payload,
      })
    } catch (err) {
      console.error("Error broadcasting message:", err)
      return false
    }
  }

  return { broadcast, isConnected, error }
}

// And update the useRealtimePresence function
export function useRealtimePresence(channelName: string, userId: string) {
  const [onlineUsers, setOnlineUsers] = useState<Record<string, any>>({})
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    // Check if realtime is supported
    if (!hasRealtimeSupport()) {
      console.warn("Realtime presence is not available with current database")
      setError(new Error("Realtime presence is not available"))
      return
    }

    // Check if database.channel is available
    if (typeof database.channel !== "function") {
      console.warn("Realtime presence is not available - database.channel is not a function")
      setError(new Error("Realtime presence is not available"))
      return
    }

    try {
      const newChannel = database.channel(channelName, {
        config: {
          presence: {
            key: userId,
          },
        },
      })

      newChannel
        .on("presence", { event: "sync" }, () => {
          const state = newChannel.presenceState()
          setOnlineUsers(state)
        })
        .subscribe(async (status) => {
          if (status === "SUBSCRIBED") {
            await newChannel.track({ online_at: new Date().toISOString() })
          }
        })

      setChannel(newChannel)

      return () => {
        newChannel.unsubscribe()
      }
    } catch (err) {
      console.error("Error setting up realtime presence:", err)
      setError(err instanceof Error ? err : new Error(String(err)))
    }
  }, [channelName, userId])

  return { onlineUsers, error }
}
