import { useEffect } from 'react'
import { client as database, hasRealtimeSupport } from '@/lib/database'
import { usePlayer } from '@/contexts/music-player-context'

export function useRealtimePlayer() {
  const { state } = usePlayer()

  useEffect(() => {
    // Only set up realtime if supported (Supabase fallback)
    if (!hasRealtimeSupport()) {
      console.log('⚠️ Realtime not supported with current database')
      return
    }

    // Subscribe to player status changes
    const channel = database
      .channel('player_status')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'playlist',
          filter: 'position=1'
        },
        (payload) => {
          // Handle player status updates
          console.log('Player status update:', payload)
        }
      )
      .subscribe()

    return () => {
      database.removeChannel(channel)
    }
  }, [])

  // Function to broadcast player status
  const broadcastPlayerStatus = async (status: {
    isPlaying: boolean
    currentTrack: any
    currentTime: number
  }) => {
    try {
      // Only broadcast if realtime is supported
      if (!hasRealtimeSupport()) {
        console.log('⚠️ Player status broadcast not supported with current database')
        return
      }

      const { error } = await database
        .from('player_status')
        .upsert({
          id: 1,
          is_playing: status.isPlaying,
          current_track_id: status.currentTrack?.id,
          current_time: status.currentTime,
          updated_at: new Date().toISOString()
        })

      if (error) throw error
    } catch (error) {
      console.error('Error broadcasting player status:', error)
    }
  }

  return { broadcastPlayerStatus }
}