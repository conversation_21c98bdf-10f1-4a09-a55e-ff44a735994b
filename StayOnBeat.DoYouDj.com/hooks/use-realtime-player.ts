import { useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { usePlayer } from '@/contexts/music-player-context'

export function useRealtimePlayer() {
  const { state } = usePlayer()

  useEffect(() => {
    // Subscribe to player status changes
    const channel = supabase
      .channel('player_status')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'playlist',
          filter: 'position=1'
        },
        (payload) => {
          // Handle player status updates
          console.log('Player status update:', payload)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  // Function to broadcast player status
  const broadcastPlayerStatus = async (status: {
    isPlaying: boolean
    currentTrack: any
    currentTime: number
  }) => {
    try {
      const { error } = await supabase
        .from('player_status')
        .upsert({
          id: 1,
          is_playing: status.isPlaying,
          current_track_id: status.currentTrack?.id,
          current_time: status.currentTime,
          updated_at: new Date().toISOString()
        })

      if (error) throw error
    } catch (error) {
      console.error('Error broadcasting player status:', error)
    }
  }

  return { broadcastPlayerStatus }
} 