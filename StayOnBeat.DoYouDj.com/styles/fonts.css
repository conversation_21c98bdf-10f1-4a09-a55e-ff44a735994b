.font-tiltNeon {
  font-family: var(--font-tilt-neon), var(--font-orbitron), system-ui, sans-serif;
}

.font-orbitron {
  font-family: var(--font-orbitron), system-ui, sans-serif;
}

.font-neonderthaw {
  font-family: var(--font-neonderthaw), cursive;
}

/* Tilt Neon specific class */
.tilt-neon {
  font-family: "Tilt Neon", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "XROT" 0, "YROT" 0;
}

/* Neonderthaw specific class */
.neonderthaw {
  font-family: "Neonderthaw", cursive;
  font-weight: 400;
  font-style: normal;
}

/* Custom neon text effect */
.neon-text {
  text-shadow: 0 0 4px rgba(255, 0, 255, 0.64), 0 0 8px rgba(255, 0, 255, 0.48), 0 0 12px rgba(255, 0, 255, 0.32), 0 0
    16px rgba(255, 0, 255, 0.16);
  letter-spacing: 1px;
}

.neon-text-cyan {
  text-shadow: 0 0 4px rgba(0, 255, 255, 0.64), 0 0 8px rgba(0, 255, 255, 0.48), 0 0 12px rgba(0, 255, 255, 0.32), 0 0
    16px rgba(0, 255, 255, 0.16);
  letter-spacing: 1px;
}

.neon-text-red {
  text-shadow: 0 0 4px rgba(255, 0, 0, 0.64), 0 0 8px rgba(255, 0, 0, 0.48), 0 0 12px rgba(255, 0, 0, 0.32), 0 0 16px
    rgba(255, 0, 0, 0.16);
  letter-spacing: 1px;
}

/* Enhanced glow text effects - reduced by 20% */
.glow-text-cyan {
  color: #fff;
  text-shadow: 0 0 5.6px #00ffff, 0 0 8px #00ffff, 0 0 16.8px #00ffff, 0 0 33.6px #00ffff, 0 0 65.6px #00ffff, 0 0
    73.6px #00ffff, 0 0 81.6px #00ffff, 0 0 120.8px #00ffff;
  letter-spacing: 1px;
}

.glow-text-red {
  color: #fff;
  text-shadow: 0 0 5.6px #ff0000, 0 0 8px #ff0000, 0 0 16.8px #ff0000, 0 0 33.6px #ff0000, 0 0 65.6px #ff0000, 0 0
    73.6px #ff0000, 0 0 81.6px #ff0000, 0 0 120.8px #ff0000;
  letter-spacing: 1px;
}

/* Neonderthaw specific glow effect */
.neonderthaw-glow {
  color: #fff;
  text-shadow: 0 0 5px #ff00ff, 0 0 10px #ff00ff, 0 0 15px #ff00ff, 0 0 20px #ff00ff, 0 0 25px #ff00ff;
  letter-spacing: 2px;
}
