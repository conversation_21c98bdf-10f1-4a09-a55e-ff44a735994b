{"name": "clerk-nextjs-quickstart-app-router", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.12", "@supabase/supabase-js": "^2.49.4", "next": "^15.2.4", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "latest", "@types/react": "latest", "@types/react-dom": "latest", "autoprefixer": "latest", "eslint": "latest", "eslint-config-next": "14.2.8", "postcss": "latest", "tailwindcss": "latest", "typescript": "latest"}}