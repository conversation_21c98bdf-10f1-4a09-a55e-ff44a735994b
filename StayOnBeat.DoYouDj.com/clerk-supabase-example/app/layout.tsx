import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>In<PERSON><PERSON><PERSON>,
  SignedIn,
  SignedO<PERSON>,
  <PERSON>r<PERSON>utt<PERSON>,
} from "@clerk/nextjs";
import "./globals.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <header>
            <SignedOut>
              <SignInButton />
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </header>
          <main>{children}</main>
        </body>
      </html>
    </ClerkProvider>
  );
}
