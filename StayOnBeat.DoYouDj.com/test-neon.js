#!/usr/bin/env node

/**
 * Neon Database Integration Test for StayOnBeat
 * Tests the complete Neon PostgreSQL setup
 */

console.log('🚀 Neon Database Integration Test for StayOnBeat')
console.log('===============================================')

// Test environment variables
console.log('\n📋 Environment Check:')
console.log('DATABASE_URL:', process.env.DATABASE_URL ? '✅ Set' : '❌ Not set')
console.log('NEXT_PUBLIC_USE_LOCAL_DB:', process.env.NEXT_PUBLIC_USE_LOCAL_DB || 'not set')

// Test if PostgreSQL packages are installed
console.log('\n📦 Package Check:')
try {
  require('pg')
  console.log('pg: ✅ Installed')
} catch (error) {
  console.log('pg: ❌ Not installed')
  console.log('Run: npm install pg @types/pg')
  process.exit(1)
}

// Test database client selection
console.log('\n🔍 Database Client Selection:')
try {
  // Import the URL processor to test database selection
  const { processUrl } = require('./lib/url-processor.js')
  console.log('URL processor: ✅ Loaded')
  
  // Test which database client would be selected
  if (process.env.DATABASE_URL) {
    console.log('Selected database: 🚀 Neon PostgreSQL')
  } else if (process.env.NEXT_PUBLIC_USE_LOCAL_DB === 'true') {
    console.log('Selected database: 📁 Local Database')
  } else {
    console.log('Selected database: ☁️ Supabase (fallback)')
  }
} catch (error) {
  console.log('URL processor: ❌ Error loading:', error.message)
}

// Test Neon connection (if configured)
async function testNeonConnection() {
  if (!process.env.DATABASE_URL) {
    console.log('\n⏭️ Skipping Neon connection test (no DATABASE_URL set)')
    console.log('To test Neon:')
    console.log('1. Set DATABASE_URL in .env.local')
    console.log('2. Run this test again')
    return
  }

  console.log('\n🔍 Testing Neon Connection:')
  try {
    const { testNeonConnection } = require('./lib/neon.js')
    const connected = await testNeonConnection()
    
    if (connected) {
      console.log('Neon connection: ✅ Success')
    } else {
      console.log('Neon connection: ❌ Failed')
    }
  } catch (error) {
    console.log('Neon connection: ❌ Error:', error.message)
    console.log('Make sure your DATABASE_URL is correct in .env.local')
  }
}

// Test URL processing with current database
async function testUrlProcessing() {
  console.log('\n🎵 Testing URL Processing:')
  try {
    const { processUrl } = require('./lib/url-processor.js')
    
    const testUrl = 'https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB'
    console.log(`Processing: ${testUrl}`)
    
    const result = await processUrl({
      url: testUrl,
      submissionType: 'Free',
      userId: 'test-user',
      userEmail: '<EMAIL>'
    })
    
    if (result.success) {
      console.log('URL processing: ✅ Success')
      console.log(`Submission ID: ${result.submissionId}`)
      console.log(`Platform: ${result.platformDetection?.platform}`)
      console.log(`Track: ${result.trackCard?.title} by ${result.trackCard?.artist}`)
    } else {
      console.log('URL processing: ❌ Failed')
      console.log(`Error: ${result.error}`)
    }
  } catch (error) {
    console.log('URL processing: ❌ Error:', error.message)
  }
}

// Main test function
async function runTests() {
  await testNeonConnection()
  await testUrlProcessing()
  
  console.log('\n🎉 Test Summary:')
  console.log('================')
  console.log('✅ Dependencies installed')
  console.log('✅ Database client selection working')
  
  if (process.env.DATABASE_URL) {
    console.log('🚀 Neon configuration detected')
    console.log('📋 Next steps:')
    console.log('   1. Create database schema in Neon Console')
    console.log('   2. Copy/paste scripts/neon-schema.sql')
    console.log('   3. Test your app: http://localhost:3003')
  } else {
    console.log('📁 Using local database (no Neon configured)')
    console.log('📋 To set up Neon:')
    console.log('   1. Create account at https://neon.tech')
    console.log('   2. Create database and get connection string')
    console.log('   3. Add DATABASE_URL to .env.local')
    console.log('   4. Run this test again')
  }
  
  console.log('\n📖 For detailed setup instructions, see:')
  console.log('   - NEON_SETUP.md')
  console.log('   - STAYONBEAT_WORKFLOW_DOCUMENTATION.md')
}

// Run the tests
runTests().catch(console.error)
