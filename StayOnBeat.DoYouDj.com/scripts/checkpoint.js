#!/usr/bin/env node

/**
 * Checkpoint and Rollback System
 * 
 * This script provides functionality to create checkpoints of the application state
 * and roll back to previous checkpoints when needed.
 * 
 * Usage:
 *   - Create checkpoint: node scripts/checkpoint.js create [name]
 *   - List checkpoints: node scripts/checkpoint.js list
 *   - Rollback to checkpoint: node scripts/checkpoint.js rollback [number]
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CHECKPOINTS_DIR = path.join(__dirname, '../.checkpoints');
const MAX_CHECKPOINTS = 10;

// Ensure checkpoints directory exists
if (!fs.existsSync(CHECKPOINTS_DIR)) {
  fs.mkdirSync(CHECKPOINTS_DIR, { recursive: true });
}

// Helper functions
function getCheckpoints() {
  if (!fs.existsSync(CHECKPOINTS_DIR)) return [];
  
  return fs.readdirSync(CHECKPOINTS_DIR)
    .filter(file => file.endsWith('.json'))
    .map(file => {
      const content = JSON.parse(fs.readFileSync(path.join(CHECKPOINTS_DIR, file), 'utf8'));
      return {
        number: parseInt(file.split('-')[0]),
        name: content.name,
        timestamp: content.timestamp,
        gitCommit: content.gitCommit,
        files: content.files
      };
    })
    .sort((a, b) => b.number - a.number);
}

function getNextCheckpointNumber() {
  const checkpoints = getCheckpoints();
  return checkpoints.length > 0 ? checkpoints[0].number + 1 : 1;
}

function createCheckpoint(name) {
  const checkpointNumber = getNextCheckpointNumber();
  const timestamp = new Date().toISOString();
  let gitCommit = null;
  
  // Try to get current git commit hash
  try {
    gitCommit = execSync('git rev-parse HEAD').toString().trim();
  } catch (error) {
    console.warn('Warning: Could not get git commit hash. Git may not be initialized.');
  }
  
  // Get list of important files to save
  const filesToSave = [
    'app/login/page.tsx',
    'components/current-user-avatar.tsx',
    'app/auth/callback/page.tsx',
    'lib/jwt.ts',
    'middleware.ts',
    'app/api/auth/token/route.ts',
    'app/api/auth/verify/route.ts',
    'app/api/auth/logout/route.ts',
    'hooks/useAuth.ts',
    'components/protected-route.tsx',
    'lib/api.ts'
  ];
  
  // Create file snapshots
  const fileSnapshots = {};
  filesToSave.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      fileSnapshots[filePath] = fs.readFileSync(fullPath, 'utf8');
    }
  });
  
  // Create checkpoint data
  const checkpointData = {
    name: name || `Checkpoint ${checkpointNumber}`,
    timestamp,
    gitCommit,
    files: fileSnapshots
  };
  
  // Save checkpoint
  const checkpointFile = path.join(CHECKPOINTS_DIR, `${checkpointNumber.toString().padStart(2, '0')}-checkpoint.json`);
  fs.writeFileSync(checkpointFile, JSON.stringify(checkpointData, null, 2));
  
  // Limit number of checkpoints
  const checkpoints = getCheckpoints();
  if (checkpoints.length > MAX_CHECKPOINTS) {
    const oldestCheckpoint = checkpoints[checkpoints.length - 1];
    fs.unlinkSync(path.join(CHECKPOINTS_DIR, `${oldestCheckpoint.number.toString().padStart(2, '0')}-checkpoint.json`));
  }
  
  console.log(`✅ Created checkpoint ${checkpointNumber}: ${checkpointData.name}`);
  return checkpointNumber;
}

function listCheckpoints() {
  const checkpoints = getCheckpoints();
  
  if (checkpoints.length === 0) {
    console.log('No checkpoints found.');
    return;
  }
  
  console.log('Available checkpoints:');
  checkpoints.forEach(checkpoint => {
    const date = new Date(checkpoint.timestamp).toLocaleString();
    console.log(`${checkpoint.number}. ${checkpoint.name} (${date})`);
  });
}

function rollbackToCheckpoint(checkpointNumber) {
  const checkpoints = getCheckpoints();
  const checkpoint = checkpoints.find(cp => cp.number === parseInt(checkpointNumber));
  
  if (!checkpoint) {
    console.error(`❌ Checkpoint ${checkpointNumber} not found.`);
    return false;
  }
  
  // Create a backup before rollback
  createCheckpoint('Auto-backup before rollback to checkpoint ' + checkpointNumber);
  
  // Restore files from checkpoint
  Object.entries(checkpoint.files).forEach(([filePath, content]) => {
    const fullPath = path.join(process.cwd(), filePath);
    const dir = path.dirname(fullPath);
    
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(fullPath, content);
  });
  
  console.log(`✅ Successfully rolled back to checkpoint ${checkpointNumber}: ${checkpoint.name}`);
  return true;
}

// Main
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      createCheckpoint(args[1]);
      break;
    case 'list':
      listCheckpoints();
      break;
    case 'rollback':
      if (!args[1]) {
        console.error('❌ Please specify a checkpoint number to roll back to.');
        process.exit(1);
      }
      rollbackToCheckpoint(args[1]);
      break;
    default:
      console.log(`
Checkpoint and Rollback System

Usage:
  - Create checkpoint: node scripts/checkpoint.js create [name]
  - List checkpoints: node scripts/checkpoint.js list
  - Rollback to checkpoint: node scripts/checkpoint.js rollback [number]
      `);
  }
}

main();
