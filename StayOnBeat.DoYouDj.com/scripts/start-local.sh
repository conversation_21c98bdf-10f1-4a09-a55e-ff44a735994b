#!/bin/bash

# StayOnBeat Local Development Startup Script
# This script sets up and starts the complete local development environment

echo "🚀 Starting StayOnBeat Local Development Environment"
echo "=================================================="

# Check if Docker is running (required for Supabase)
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "📦 Installing Supabase CLI..."
    npm install -g supabase
fi

# Initialize Supabase if not already done
if [ ! -f "supabase/config.toml" ]; then
    echo "🔧 Initializing Supabase..."
    supabase init
fi

# Start Supabase services
echo "🗄️ Starting Supabase services..."
supabase start

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Apply migrations
echo "📊 Applying database migrations..."
supabase db reset

# Seed the database with sample data
echo "🌱 Seeding database with sample data..."
supabase db seed

# Create local environment file if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating local environment file..."
    cp .env.local.example .env.local
    echo "✅ Created .env.local - please update with your API keys"
fi

# Start the Next.js development server
echo "🌐 Starting Next.js development server..."
npm run dev &

echo ""
echo "✅ StayOnBeat Local Environment Started!"
echo "=================================================="
echo "🌐 App: http://localhost:3002"
echo "🗄️ Supabase Studio: http://localhost:54323"
echo "📧 Inbucket (Email): http://localhost:54324"
echo "🔧 API: http://localhost:54321"
echo ""
echo "📋 Next Steps:"
echo "1. Visit http://localhost:3002 to see your app"
echo "2. Visit http://localhost:54323 to manage your database"
echo "3. Update .env.local with your API keys for full functionality"
echo "4. Sign up for an admin account and update is_admin = true in profiles table"
echo ""
echo "🛑 To stop all services: npm run stop-local"
