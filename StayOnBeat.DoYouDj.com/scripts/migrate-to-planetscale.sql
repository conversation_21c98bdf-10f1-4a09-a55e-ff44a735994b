-- StayOnBeat Database Schema for PlanetScale (MySQL)
-- Converted from PostgreSQL Supabase schema to MySQL

-- Create submissions table
CREATE TABLE IF NOT EXISTS submissions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(36),
  artist_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  song_title VARCHAR(255) NOT NULL,
  submission_type ENUM('VIP', 'Skip', 'GA', 'Free') NOT NULL,
  status ENUM('pending', 'confirmed', 'rejected', 'played') NOT NULL DEFAULT 'pending',
  platform VARCHAR(50),
  platform_id VARCHAR(255),
  content_type ENUM('track', 'playlist', 'album', 'artist'),
  processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  metadata_extracted BOOLEAN DEFAULT FALSE,
  url TEXT,
  media_url TEXT,
  notes TEXT,
  position INT,
  images JSON,
  previous_submissions JSON,
  track_card_data JSON,
  search_text TEXT,
  normalized_artist VARCHAR(255),
  normalized_title VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_submissions_status (status),
  INDEX idx_submissions_platform (platform),
  INDEX idx_submissions_type (submission_type),
  INDEX idx_submissions_user (user_id),
  INDEX idx_submissions_search (search_text(100)),
  INDEX idx_submissions_created (created_at)
);

-- Create playlist table (live queue)
CREATE TABLE IF NOT EXISTS playlist (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  position INT NOT NULL,
  artist_name VARCHAR(255) NOT NULL,
  song_title VARCHAR(255) NOT NULL,
  type ENUM('VIP', 'Skip', 'GA', 'Free') NOT NULL,
  user_id VARCHAR(36),
  submission_id BIGINT,
  platform VARCHAR(50),
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_playlist_position (position),
  INDEX idx_playlist_type (type),
  INDEX idx_playlist_submission (submission_id),
  FOREIGN KEY (submission_id) REFERENCES submissions(id) ON DELETE SET NULL
);

-- Create tracks table for normalized track metadata
CREATE TABLE IF NOT EXISTS tracks (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  title VARCHAR(255) NOT NULL,
  artist VARCHAR(255) NOT NULL,
  album VARCHAR(255),
  duration INT,
  artwork_url TEXT,
  audio_url TEXT,
  stream_url TEXT,
  platform VARCHAR(50) NOT NULL,
  platform_id VARCHAR(255) NOT NULL,
  genre VARCHAR(100),
  release_date DATE,
  is_explicit BOOLEAN DEFAULT FALSE,
  isrc VARCHAR(20),
  spotify_id VARCHAR(100),
  youtube_id VARCHAR(100),
  soundcloud_id VARCHAR(100),
  bandcamp_id VARCHAR(100),
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_platform_track (platform, platform_id),
  INDEX idx_tracks_artist (artist),
  INDEX idx_tracks_platform (platform),
  INDEX idx_tracks_spotify (spotify_id),
  INDEX idx_tracks_youtube (youtube_id)
);

-- Create played_tracks table
CREATE TABLE IF NOT EXISTS played_tracks (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  artist_name VARCHAR(255) NOT NULL,
  song_title VARCHAR(255) NOT NULL,
  played_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  feedback TEXT,
  feedback_sent BOOLEAN DEFAULT FALSE,
  user_id VARCHAR(36),
  submission_id BIGINT,
  platform VARCHAR(50),
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_played_tracks_date (played_at),
  INDEX idx_played_tracks_user (user_id),
  INDEX idx_played_tracks_submission (submission_id),
  FOREIGN KEY (submission_id) REFERENCES submissions(id) ON DELETE SET NULL
);

-- Create artists table
CREATE TABLE IF NOT EXISTS artists (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSON,
  social_links JSON,
  user_id VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_artists_name (name),
  INDEX idx_artists_user (user_id)
);

-- Create artist_library table for artist management
CREATE TABLE IF NOT EXISTS artist_library (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  normalized_name VARCHAR(255) NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSON,
  social_links JSON,
  platforms JSON,
  submission_count INT DEFAULT 0,
  play_count INT DEFAULT 0,
  last_submission TIMESTAMP NULL,
  last_played TIMESTAMP NULL,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_normalized_name (normalized_name),
  INDEX idx_artist_library_name (name),
  INDEX idx_artist_library_submissions (submission_count),
  INDEX idx_artist_library_plays (play_count)
);

-- Create playlists table for saved playlists
CREATE TABLE IF NOT EXISTS playlists (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  visibility ENUM('admin', 'private', 'public') NOT NULL DEFAULT 'admin',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  track_count INT DEFAULT 0,
  is_finalized BOOLEAN DEFAULT FALSE,
  
  INDEX idx_playlists_visibility (visibility),
  INDEX idx_playlists_finalized (is_finalized)
);

-- Create playlist_items table
CREATE TABLE IF NOT EXISTS playlist_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  playlist_id INT NOT NULL,
  track_id INT NOT NULL,
  position INT NOT NULL,
  artist_name VARCHAR(255),
  song_title VARCHAR(255),
  url TEXT,
  platform VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_playlist_items_playlist (playlist_id),
  INDEX idx_playlist_items_position (position),
  FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE
);

-- Create playlist_files table to store M3U8 content
CREATE TABLE IF NOT EXISTS playlist_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  playlist_id INT NOT NULL,
  content LONGTEXT NOT NULL,
  format VARCHAR(10) DEFAULT 'm3u8',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_playlist_files_playlist (playlist_id),
  FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE
);

-- Create images table
CREATE TABLE IF NOT EXISTS images (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  url TEXT NOT NULL,
  category VARCHAR(100) NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_images_category (category)
);

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
  id VARCHAR(100) PRIMARY KEY,
  value JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO settings (id, value) VALUES 
  ('default_background_image', '{"url": null}'),
  ('default_logo_image', '{"url": null}'),
  ('default_profile_image', '{"url": null}')
ON DUPLICATE KEY UPDATE value = VALUES(value);

-- Create profiles table (simplified for MySQL)
CREATE TABLE IF NOT EXISTS profiles (
  id VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255),
  full_name VARCHAR(255),
  avatar_url TEXT,
  provider VARCHAR(50),
  provider_id VARCHAR(255),
  is_complete BOOLEAN DEFAULT FALSE,
  spotify_profile JSON,
  youtube_profile JSON,
  soundcloud_profile JSON,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_profiles_email (email),
  INDEX idx_profiles_provider (provider, provider_id),
  INDEX idx_profiles_admin (is_admin)
);
