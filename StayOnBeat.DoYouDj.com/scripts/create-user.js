// <PERSON>ript to create a test user in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase admin client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUser() {
  try {
    // Create user
    const { data: user, error: createError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'test',
      email_confirm: true // Auto-confirm the email
    });

    if (createError) {
      throw createError;
    }

    console.log('User created successfully:', user);
    
    // Set user as admin (optional)
    // This depends on your database schema
    if (user && user.user && user.user.id) {
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert({
          id: user.user.id,
          email: '<EMAIL>',
          role: 'admin',
          updated_at: new Date().toISOString()
        });

      if (updateError) {
        console.error('Error setting user as admin:', updateError);
      } else {
        console.log('User set as admin successfully');
      }
    }

  } catch (error) {
    console.error('Error creating user:', error);
  }
}

createUser();
