#!/usr/bin/env node

/**
 * Data Migration Script: Local Database → PlanetScale
 * Migrates existing StayOnBeat data from local JSON files to PlanetScale MySQL
 */

const fs = require('fs')
const path = require('path')

// Import PlanetScale client (will need to be adjusted after npm install)
async function importPlanetScale() {
  try {
    const { planetscale } = await import('../lib/planetscale.js')
    return planetscale
  } catch (error) {
    console.error('❌ Could not import PlanetScale client. Make sure to install dependencies first.')
    console.log('Run: npm install @planetscale/database mysql2')
    process.exit(1)
  }
}

// Load local data
function loadLocalData() {
  const dataPath = path.join(process.cwd(), 'data')
  const submissionsFile = path.join(dataPath, 'submissions.json')
  const playlistFile = path.join(dataPath, 'playlist.json')
  
  let submissions = []
  let playlist = []
  
  try {
    if (fs.existsSync(submissionsFile)) {
      submissions = JSON.parse(fs.readFileSync(submissionsFile, 'utf8'))
      console.log(`📁 Loaded ${submissions.length} submissions from local database`)
    }
    
    if (fs.existsSync(playlistFile)) {
      playlist = JSON.parse(fs.readFileSync(playlistFile, 'utf8'))
      console.log(`📁 Loaded ${playlist.length} playlist items from local database`)
    }
  } catch (error) {
    console.error('❌ Error loading local data:', error)
  }
  
  return { submissions, playlist }
}

// Convert data for MySQL compatibility
function convertDataForMySQL(data, table) {
  return data.map(item => {
    const converted = { ...item }
    
    // Convert timestamps to MySQL format
    if (converted.created_at) {
      converted.created_at = new Date(converted.created_at)
    }
    if (converted.updated_at) {
      converted.updated_at = new Date(converted.updated_at)
    }
    
    // Convert JSON fields
    if (table === 'submissions') {
      if (converted.images && typeof converted.images === 'object') {
        converted.images = JSON.stringify(converted.images)
      }
      if (converted.previous_submissions && typeof converted.previous_submissions === 'object') {
        converted.previous_submissions = JSON.stringify(converted.previous_submissions)
      }
      if (converted.track_card_data && typeof converted.track_card_data === 'object') {
        converted.track_card_data = JSON.stringify(converted.track_card_data)
      }
    }
    
    // Remove undefined values
    Object.keys(converted).forEach(key => {
      if (converted[key] === undefined) {
        delete converted[key]
      }
    })
    
    return converted
  })
}

// Migrate submissions
async function migrateSubmissions(planetscale, submissions) {
  console.log('🚀 Migrating submissions...')
  
  const convertedSubmissions = convertDataForMySQL(submissions, 'submissions')
  
  for (const submission of convertedSubmissions) {
    try {
      const columns = Object.keys(submission).join(', ')
      const placeholders = Object.keys(submission).map(() => '?').join(', ')
      const values = Object.values(submission)
      
      const query = `INSERT INTO submissions (${columns}) VALUES (${placeholders})`
      await planetscale.execute(query, values)
      
      console.log(`✅ Migrated submission: ${submission.artist_name} - ${submission.song_title}`)
    } catch (error) {
      console.error(`❌ Error migrating submission ${submission.id}:`, error)
    }
  }
}

// Migrate playlist
async function migratePlaylist(planetscale, playlist) {
  console.log('🚀 Migrating playlist...')
  
  const convertedPlaylist = convertDataForMySQL(playlist, 'playlist')
  
  for (const item of convertedPlaylist) {
    try {
      const columns = Object.keys(item).join(', ')
      const placeholders = Object.keys(item).map(() => '?').join(', ')
      const values = Object.values(item)
      
      const query = `INSERT INTO playlist (${columns}) VALUES (${placeholders})`
      await planetscale.execute(query, values)
      
      console.log(`✅ Migrated playlist item: ${item.artist_name} - ${item.song_title}`)
    } catch (error) {
      console.error(`❌ Error migrating playlist item ${item.id}:`, error)
    }
  }
}

// Test PlanetScale connection
async function testConnection(planetscale) {
  try {
    console.log('🔍 Testing PlanetScale connection...')
    const result = await planetscale.execute('SELECT 1 as test')
    console.log('✅ PlanetScale connection successful!')
    return true
  } catch (error) {
    console.error('❌ PlanetScale connection failed:', error)
    console.log('Make sure your DATABASE_URL environment variable is set correctly.')
    return false
  }
}

// Main migration function
async function main() {
  console.log('🚀 Starting StayOnBeat data migration to PlanetScale...')
  
  // Check environment variables
  if (!process.env.DATABASE_URL && !process.env.PLANETSCALE_DATABASE_URL) {
    console.error('❌ Missing DATABASE_URL or PLANETSCALE_DATABASE_URL environment variable')
    console.log('Please set your PlanetScale connection string in .env.local:')
    console.log('DATABASE_URL="mysql://username:password@host/database?sslaccept=strict"')
    process.exit(1)
  }
  
  // Import PlanetScale client
  const planetscale = await importPlanetScale()
  
  // Test connection
  const connected = await testConnection(planetscale)
  if (!connected) {
    process.exit(1)
  }
  
  // Load local data
  const { submissions, playlist } = loadLocalData()
  
  if (submissions.length === 0 && playlist.length === 0) {
    console.log('📭 No local data found to migrate.')
    console.log('Make sure you have data in:')
    console.log('- data/submissions.json')
    console.log('- data/playlist.json')
    process.exit(0)
  }
  
  try {
    // Migrate submissions
    if (submissions.length > 0) {
      await migrateSubmissions(planetscale, submissions)
    }
    
    // Migrate playlist
    if (playlist.length > 0) {
      await migratePlaylist(planetscale, playlist)
    }
    
    console.log('🎉 Migration completed successfully!')
    console.log(`✅ Migrated ${submissions.length} submissions and ${playlist.length} playlist items`)
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Run migration
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { main, loadLocalData, convertDataForMySQL }
