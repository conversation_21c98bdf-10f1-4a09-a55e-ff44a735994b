-- StayOnBeat Database Schema for Neon (PostgreSQL)
-- Complete schema for URL processing and playlist management

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create submissions table
CREATE TABLE IF NOT EXISTS submissions (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  submission_type TEXT NOT NULL CHECK (submission_type IN ('VIP', 'Skip', 'GA', 'Free')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected', 'played')),
  platform TEXT,
  platform_id TEXT,
  content_type TEXT CHECK (content_type IN ('track', 'playlist', 'album', 'artist')),
  processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  metadata_extracted BOOLEAN DEFAULT FALSE,
  url TEXT,
  media_url TEXT,
  notes TEXT,
  position INTEGER,
  images JSONB,
  previous_submissions JSONB,
  track_card_data JSONB,
  search_text TEXT,
  normalized_artist TEXT,
  normalized_title TEXT,
  duration INTEGER,
  artwork_url TEXT,
  album_name TEXT,
  genre TEXT,
  is_explicit BOOLEAN DEFAULT FALSE,
  stream_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for submissions
CREATE INDEX IF NOT EXISTS idx_submissions_status ON submissions(status);
CREATE INDEX IF NOT EXISTS idx_submissions_platform ON submissions(platform);
CREATE INDEX IF NOT EXISTS idx_submissions_type ON submissions(submission_type);
CREATE INDEX IF NOT EXISTS idx_submissions_user ON submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_submissions_created ON submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_submissions_search ON submissions(search_text);

-- Create playlist table (live queue)
CREATE TABLE IF NOT EXISTS playlist (
  id BIGSERIAL PRIMARY KEY,
  position INTEGER NOT NULL,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('VIP', 'Skip', 'GA', 'Free')),
  user_id UUID,
  submission_id BIGINT REFERENCES submissions(id) ON DELETE SET NULL,
  platform TEXT,
  url TEXT,
  media_url TEXT,
  artwork_url TEXT,
  duration INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for playlist
CREATE INDEX IF NOT EXISTS idx_playlist_position ON playlist(position);
CREATE INDEX IF NOT EXISTS idx_playlist_type ON playlist(type);
CREATE INDEX IF NOT EXISTS idx_playlist_submission ON playlist(submission_id);

-- Create tracks table for normalized track metadata
CREATE TABLE IF NOT EXISTS tracks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  album TEXT,
  duration INTEGER,
  artwork_url TEXT,
  audio_url TEXT,
  stream_url TEXT,
  platform TEXT NOT NULL,
  platform_id TEXT NOT NULL,
  genre TEXT,
  release_date DATE,
  is_explicit BOOLEAN DEFAULT FALSE,
  isrc TEXT,
  spotify_id TEXT,
  youtube_id TEXT,
  soundcloud_id TEXT,
  bandcamp_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(platform, platform_id)
);

-- Create indexes for tracks
CREATE INDEX IF NOT EXISTS idx_tracks_artist ON tracks(artist);
CREATE INDEX IF NOT EXISTS idx_tracks_title ON tracks(title);
CREATE INDEX IF NOT EXISTS idx_tracks_platform ON tracks(platform);
CREATE INDEX IF NOT EXISTS idx_tracks_spotify ON tracks(spotify_id) WHERE spotify_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tracks_youtube ON tracks(youtube_id) WHERE youtube_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tracks_soundcloud ON tracks(soundcloud_id) WHERE soundcloud_id IS NOT NULL;

-- Create played_tracks table
CREATE TABLE IF NOT EXISTS played_tracks (
  id BIGSERIAL PRIMARY KEY,
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  played_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  feedback TEXT,
  feedback_sent BOOLEAN DEFAULT FALSE,
  user_id UUID,
  submission_id BIGINT REFERENCES submissions(id) ON DELETE SET NULL,
  platform TEXT,
  url TEXT,
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for played_tracks
CREATE INDEX IF NOT EXISTS idx_played_tracks_date ON played_tracks(played_at);
CREATE INDEX IF NOT EXISTS idx_played_tracks_user ON played_tracks(user_id);
CREATE INDEX IF NOT EXISTS idx_played_tracks_submission ON played_tracks(submission_id);

-- Create artists table
CREATE TABLE IF NOT EXISTS artists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  user_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for artists
CREATE INDEX IF NOT EXISTS idx_artists_name ON artists(name);
CREATE INDEX IF NOT EXISTS idx_artists_user ON artists(user_id);

-- Create artist_library table for artist management
CREATE TABLE IF NOT EXISTS artist_library (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  normalized_name TEXT NOT NULL UNIQUE,
  bio TEXT,
  profile_image TEXT,
  cover_images JSONB,
  social_links JSONB,
  platforms JSONB,
  submission_count INTEGER DEFAULT 0,
  play_count INTEGER DEFAULT 0,
  last_submission TIMESTAMP WITH TIME ZONE,
  last_played TIMESTAMP WITH TIME ZONE,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for artist_library
CREATE INDEX IF NOT EXISTS idx_artist_library_name ON artist_library(name);
CREATE INDEX IF NOT EXISTS idx_artist_library_normalized_name ON artist_library(normalized_name);
CREATE INDEX IF NOT EXISTS idx_artist_library_submissions ON artist_library(submission_count);
CREATE INDEX IF NOT EXISTS idx_artist_library_plays ON artist_library(play_count);

-- Create playlists table for saved playlists
CREATE TABLE IF NOT EXISTS playlists (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  visibility TEXT NOT NULL DEFAULT 'admin' CHECK (visibility IN ('admin', 'private', 'public')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  track_count INTEGER DEFAULT 0,
  is_finalized BOOLEAN DEFAULT FALSE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for playlists
CREATE INDEX IF NOT EXISTS idx_playlists_visibility ON playlists(visibility);
CREATE INDEX IF NOT EXISTS idx_playlists_finalized ON playlists(is_finalized);

-- Create playlist_items table
CREATE TABLE IF NOT EXISTS playlist_items (
  id SERIAL PRIMARY KEY,
  playlist_id INTEGER NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
  track_id UUID REFERENCES tracks(id) ON DELETE CASCADE,
  position INTEGER NOT NULL,
  artist_name TEXT,
  song_title TEXT,
  url TEXT,
  platform TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for playlist_items
CREATE INDEX IF NOT EXISTS idx_playlist_items_playlist ON playlist_items(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_items_position ON playlist_items(position);
CREATE INDEX IF NOT EXISTS idx_playlist_items_track ON playlist_items(track_id);

-- Create playlist_files table to store M3U8 content
CREATE TABLE IF NOT EXISTS playlist_files (
  id SERIAL PRIMARY KEY,
  playlist_id INTEGER NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  format TEXT DEFAULT 'm3u8',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for playlist_files
CREATE INDEX IF NOT EXISTS idx_playlist_files_playlist ON playlist_files(playlist_id);

-- Create images table
CREATE TABLE IF NOT EXISTS images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  url TEXT NOT NULL,
  category TEXT NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for images
CREATE INDEX IF NOT EXISTS idx_images_category ON images(category);

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
  id TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings
INSERT INTO settings (id, value) VALUES 
  ('default_background_image', '{"url": null}'),
  ('default_logo_image', '{"url": null}'),
  ('default_profile_image', '{"url": null}')
ON CONFLICT (id) DO NOTHING;

-- Create profiles table (simplified for PostgreSQL)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  provider TEXT,
  provider_id TEXT,
  is_complete BOOLEAN DEFAULT FALSE,
  spotify_profile JSONB,
  youtube_profile JSONB,
  soundcloud_profile JSONB,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for profiles
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_provider ON profiles(provider, provider_id);
CREATE INDEX IF NOT EXISTS idx_profiles_admin ON profiles(is_admin);

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_submissions_updated_at BEFORE UPDATE ON submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_playlist_updated_at BEFORE UPDATE ON playlist FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tracks_updated_at BEFORE UPDATE ON tracks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_artists_updated_at BEFORE UPDATE ON artists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_artist_library_updated_at BEFORE UPDATE ON artist_library FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_images_updated_at BEFORE UPDATE ON images FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_playlists_updated_at BEFORE UPDATE ON playlists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
