#!/bin/bash

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    npm install -g supabase
fi

# Initialize Supabase project
echo "Initializing Supabase project..."
supabase init

# Start Supabase locally
echo "Starting Supabase locally..."
supabase start

# Apply database schema
echo "Applying database schema..."
supabase db reset

# Get the local Supabase URL and anon key
SUPABASE_URL=$(supabase status | grep "API URL" | awk '{print $3}')
SUPABASE_ANON_KEY=$(supabase status | grep "anon key" | awk '{print $4}')

# Create .env.local file
echo "Creating .env.local file..."
cat > .env.local << EOL
NEXT_PUBLIC_SUPABASE_URL=$SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY
NEXT_PUBLIC_DEV_AUTO_LOGIN=true
EOL

echo "Setup complete! You can now start the development server with 'npm run dev'" 