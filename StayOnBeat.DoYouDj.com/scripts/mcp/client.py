from typing import Dict, Any, List, Optional, TypedDict

class ToolInputSchema(TypedDict):
    properties: Dict[str, Any]
    required: List[str]

class Tool(TypedDict):
    name: str
    description: str
    inputSchema: ToolInputSchema

class ConvertedTool(TypedDict):
    type: str
    function: Dict[str, Any]

def convert_tool_format(tool: Tool) -> ConvertedTool:
    """
    Convert a tool from internal format to OpenAI function calling format.
    
    Args:
        tool: A dictionary containing tool information with name, description, and inputSchema
        
    Returns:
        A dictionary in OpenAI function calling format
        
    Raises:
        ValueError: If required fields are missing or invalid
        TypeError: If input types are incorrect
    """
    try:
        # Validate required fields
        if not isinstance(tool, dict):
            raise TypeError("Tool must be a dictionary")
            
        required_fields = ["name", "description", "inputSchema"]
        for field in required_fields:
            if field not in tool:
                raise ValueError(f"Missing required field: {field}")
                
        if not isinstance(tool["inputSchema"], dict):
            raise TypeError("inputSchema must be a dictionary")
            
        if "properties" not in tool["inputSchema"] or "required" not in tool["inputSchema"]:
            raise ValueError("inputSchema must contain 'properties' and 'required' fields")
            
        # Convert to OpenAI function calling format
        converted_tool: ConvertedTool = {
            "type": "function",
            "function": {
                "name": tool["name"],
                "description": tool["description"],
                "parameters": {
                    "type": "object",
                    "properties": tool["inputSchema"]["properties"],
                    "required": tool["inputSchema"]["required"]
                }
            }
        }
        
        return converted_tool
        
    except Exception as e:
        raise ValueError(f"Error converting tool format: {str(e)}")

# Example usage
if __name__ == "__main__":
    # Example tool
    example_tool: Tool = {
        "name": "search_files",
        "description": "Search for files in a directory",
        "inputSchema": {
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query"
                },
                "directory": {
                    "type": "string",
                    "description": "Directory to search in"
                }
            },
            "required": ["query"]
        }
    }
    
    try:
        converted = convert_tool_format(example_tool)
        print(json.dumps(converted, indent=2))
    except Exception as e:
        print(f"Error: {str(e)}") 