-- StayOnBeat Database Schema for Neon with <PERSON><PERSON> and <PERSON> Integration
-- This schema includes Row Level Security policies for secure multi-user access

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom function to get user ID from <PERSON>WT (Clerk integration)
-- This function extracts the user ID from the JWT token set by Clerk
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS text AS $$
  SELECT current_setting('request.jwt.claims', true)::json->>'sub';
$$ LANGUAGE sql STABLE;

-- Create profiles table first (needed for admin checks)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  is_complete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS for profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- RLS policies for profiles
CREATE POLICY "Users can view their own profile" ON public.profiles FOR
  SELECT USING (auth.user_id() = user_id);

CREATE POLICY "Users can insert their own profile" ON public.profiles FOR
  INSERT WITH CHECK (auth.user_id() = user_id);

CREATE POLICY "Users can update their own profile" ON public.profiles FOR
  UPDATE USING (auth.user_id() = user_id);

CREATE POLICY "Admins can view all profiles" ON public.profiles FOR
  SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

-- Create submissions table with RLS
CREATE TABLE IF NOT EXISTS public.submissions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL DEFAULT auth.user_id(),
  artist_name TEXT NOT NULL,
  song_title TEXT NOT NULL,
  url TEXT NOT NULL,
  media_url TEXT,
  submission_type TEXT DEFAULT 'GA' CHECK (submission_type IN ('VIP', 'GA', 'Free', 'Skip')),
  platform TEXT,
  platform_id TEXT,
  content_type TEXT CHECK (content_type IN ('track', 'playlist', 'album', 'artist')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected')),
  processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  metadata_extracted BOOLEAN DEFAULT FALSE,
  duration INTEGER,
  artwork_url TEXT,
  album_name TEXT,
  genre TEXT,
  is_explicit BOOLEAN DEFAULT FALSE,
  stream_url TEXT,
  user_email TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS for submissions table
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;

-- RLS policies for submissions
CREATE POLICY "Users can create their own submissions" ON public.submissions FOR
  INSERT WITH CHECK (auth.user_id() = user_id);

CREATE POLICY "Users can view their own submissions" ON public.submissions FOR
  SELECT USING (auth.user_id() = user_id);

CREATE POLICY "Users can update their own submissions" ON public.submissions FOR
  UPDATE USING (auth.user_id() = user_id);

CREATE POLICY "Users can delete their own submissions" ON public.submissions FOR
  DELETE USING (auth.user_id() = user_id);

-- Admin policy for submissions (admins can see all)
CREATE POLICY "Admins can view all submissions" ON public.submissions FOR
  SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

CREATE POLICY "Admins can manage all submissions" ON public.submissions FOR
  ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

-- Create tracks table for normalized track metadata
CREATE TABLE IF NOT EXISTS public.tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  album TEXT,
  duration INTEGER,
  artwork_url TEXT,
  audio_url TEXT,
  stream_url TEXT,
  platform TEXT NOT NULL,
  platform_id TEXT NOT NULL,
  genre TEXT,
  release_date DATE,
  is_explicit BOOLEAN DEFAULT FALSE,
  isrc TEXT,
  spotify_id TEXT,
  youtube_id TEXT,
  soundcloud_id TEXT,
  bandcamp_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(platform, platform_id)
);

-- Enable RLS for tracks table
ALTER TABLE public.tracks ENABLE ROW LEVEL SECURITY;

-- RLS policies for tracks (public read, admin write)
CREATE POLICY "Anyone can view tracks" ON public.tracks FOR
  SELECT USING (true);

CREATE POLICY "Admins can manage tracks" ON public.tracks FOR
  ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

-- Create playlist table
CREATE TABLE IF NOT EXISTS public.playlist (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL DEFAULT auth.user_id(),
  songTitle TEXT NOT NULL,
  artistName TEXT NOT NULL,
  audioUrl TEXT,
  artworkUrl TEXT,
  duration INTEGER,
  position INTEGER,
  isPlaying BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS for playlist table
ALTER TABLE public.playlist ENABLE ROW LEVEL SECURITY;

-- RLS policies for playlist
CREATE POLICY "Users can manage their own playlist items" ON public.playlist FOR
  ALL USING (auth.user_id() = user_id);

CREATE POLICY "Admins can view all playlists" ON public.playlist FOR
  SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

CREATE POLICY "Admins can manage all playlists" ON public.playlist FOR
  ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.user_id() 
      AND profiles.is_admin = true
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_submissions_user_id ON public.submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_submissions_platform ON public.submissions(platform);
CREATE INDEX IF NOT EXISTS idx_submissions_status ON public.submissions(status);
CREATE INDEX IF NOT EXISTS idx_tracks_platform_id ON public.tracks(platform, platform_id);
CREATE INDEX IF NOT EXISTS idx_playlist_user_id ON public.playlist(user_id);
CREATE INDEX IF NOT EXISTS idx_playlist_position ON public.playlist(position);
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON public.profiles(user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_submissions_updated_at
    BEFORE UPDATE ON public.submissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tracks_updated_at
    BEFORE UPDATE ON public.tracks
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_playlist_updated_at
    BEFORE UPDATE ON public.playlist
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Insert a default admin user (replace with your Clerk user ID)
-- INSERT INTO public.profiles (user_id, email, full_name, is_admin) 
-- VALUES ('your-clerk-user-id', '<EMAIL>', 'Admin User', true)
-- ON CONFLICT (user_id) DO UPDATE SET is_admin = true;
