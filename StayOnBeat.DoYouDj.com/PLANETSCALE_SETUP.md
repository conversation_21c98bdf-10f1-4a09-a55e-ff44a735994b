# Database Setup Guide for StayOnBeat

## 🚨 **IMPORTANT UPDATE**: PlanetScale removed their free tier in April 2024!

## 🏆 **BEST FREE ALTERNATIVES (2024/2025):**

### 🥇 **NEON (RECOMMENDED) - PostgreSQL**
- **FREE TIER**: 512MB storage, auto-pause when inactive
- **PostgreSQL**: Same as Supabase (easy migration)
- **Cost**: $0/month → $19/month when scaling

### 🥈 **RAILWAY - PostgreSQL/MySQL**
- **FREE TRIAL**: $5 credit monthly
- **Cost**: $5 credit/month → $20/month

### 🥉 **TURSO - SQLite**
- **FREE TIER**: 9GB storage, 1 billion reads
- **Cost**: $0/month → $29/month

---

## 🚀 **NEON SETUP (RECOMMENDED) - 5 minutes**

### Step 1: Create Neon Account
1. Go to [neon.tech](https://neon.tech)
2. Sign up with <PERSON>it<PERSON><PERSON> (recommended) or email
3. **FREE TIER**: No credit card required!

### Step 2: Create Database
1. Click "Create database"
2. **Database name**: `stayonbeat-db`
3. **Region**: Choose closest to your users
4. Click "Create database"

### Step 3: Get Connection String
1. In your database dashboard, click "Connect"
2. Select "Connect with: General"
3. Copy the connection string (looks like):
   ```
   mysql://username:password@host/database?sslaccept=strict
   ```

### Step 4: Configure StayOnBeat
1. Copy `.env.planetscale.example` to `.env.local`:
   ```bash
   cp .env.planetscale.example .env.local
   ```

2. Edit `.env.local` and add your connection string:
   ```env
   DATABASE_URL="mysql://your-connection-string-here"
   NEXT_PUBLIC_USE_LOCAL_DB=false
   ```

### Step 5: Create Database Schema
1. Run the schema migration:
   ```bash
   # Option 1: Use PlanetScale CLI (if installed)
   pscale shell stayonbeat-db main < scripts/migrate-to-planetscale.sql

   # Option 2: Copy/paste in PlanetScale Console
   # Go to your database → Console tab
   # Copy contents of scripts/migrate-to-planetscale.sql and paste
   ```

### Step 6: Migrate Your Data (Optional)
If you have existing local data:
```bash
node scripts/migrate-data.js
```

### Step 7: Test Connection
```bash
curl -X POST http://localhost:3003/api/process-url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://open.spotify.com/track/2FNvO68ODWQD4XOmm6gzEB", "submissionType": "Free"}'
```

## 🎯 PlanetScale Features for StayOnBeat

### ✅ FREE TIER Benefits:
- **1 billion row reads/month** - Perfect for music metadata
- **10 million row writes/month** - Handles thousands of submissions
- **1 database** - All you need for StayOnBeat
- **Automatic backups** - Your data is safe
- **Global edge network** - Fast worldwide access

### 🚀 Scaling Benefits:
- **Database branching** - Test schema changes safely
- **Zero-downtime migrations** - Update without stopping music
- **Connection pooling** - Handle traffic spikes
- **Read replicas** - Scale globally when needed

## 🔧 Database Schema Overview

### Core Tables:
- **submissions** - URL submissions from users
- **playlist** - Live queue/playlist for player
- **tracks** - Normalized track metadata
- **played_tracks** - History of played music
- **artists** - Artist information and profiles

### Enhanced Features:
- **JSON columns** - Store complex metadata
- **Full-text search** - Find tracks quickly
- **Indexes** - Fast queries on all tables
- **Foreign keys** - Data integrity

## 🛠️ Troubleshooting

### Connection Issues:
```bash
# Test connection
node -e "
const { connect } = require('@planetscale/database');
const db = connect({ url: process.env.DATABASE_URL });
db.execute('SELECT 1 as test').then(console.log).catch(console.error);
"
```

### Schema Issues:
```sql
-- Check if tables exist
SHOW TABLES;

-- Check table structure
DESCRIBE submissions;
```

### Migration Issues:
```bash
# Check current data
node -e "
const fs = require('fs');
const data = JSON.parse(fs.readFileSync('data/submissions.json', 'utf8'));
console.log('Local submissions:', data.length);
"
```

## 📊 Cost Planning

### Current Usage (FREE):
- **Submissions**: ~1000/month = 1000 writes
- **Admin queries**: ~5000/month = 5000 reads
- **Player queries**: ~10000/month = 10000 reads
- **Total**: Well within free tier limits

### When to Upgrade ($39/month):
- **10+ million reads/month** (10,000+ daily active users)
- **1+ million writes/month** (1000+ submissions/day)
- **Multiple databases** (separate staging/production)
- **Advanced features** (read replicas, insights)

## 🎵 StayOnBeat Integration

### Database Priority:
1. **PlanetScale** (if DATABASE_URL set)
2. **Local Database** (if NEXT_PUBLIC_USE_LOCAL_DB=true)
3. **Supabase** (fallback)

### Automatic Switching:
The app automatically detects which database to use:
```typescript
// In lib/url-processor.ts
function getDatabaseClient() {
  if (process.env.DATABASE_URL) {
    return planetscaleClient  // 🚀 PlanetScale
  } else if (shouldUseLocalDatabase()) {
    return localDatabase      // 📁 Local
  } else {
    return supabase          // ☁️ Supabase
  }
}
```

## 🎉 Success Checklist

- [ ] PlanetScale account created
- [ ] Database created and configured
- [ ] Connection string added to .env.local
- [ ] Schema migrated successfully
- [ ] Test API call returns success
- [ ] Admin dashboard loads data
- [ ] URL processing works end-to-end

## 🔗 Useful Links

- [PlanetScale Dashboard](https://app.planetscale.com/)
- [PlanetScale Documentation](https://planetscale.com/docs)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [StayOnBeat Workflow Documentation](./STAYONBEAT_WORKFLOW_DOCUMENTATION.md)

---

**Ready to scale your music platform with PlanetScale! 🎵**
