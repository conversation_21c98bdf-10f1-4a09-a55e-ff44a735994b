import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, getTokenFromRequest } from '@/lib/jwt';

export async function GET(req: NextRequest) {
  try {
    // Get token from request
    const token = getTokenFromRequest(req);
    
    if (!token) {
      return NextResponse.json(
        { authenticated: false, message: 'No authentication token provided' },
        { status: 401 }
      );
    }
    
    // Verify token
    const user = verifyToken(token);
    
    if (!user) {
      return NextResponse.json(
        { authenticated: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }
    
    // Return user info without sensitive data
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error('Error verifying token:', error);
    return NextResponse.json(
      { authenticated: false, message: 'Error verifying authentication' },
      { status: 500 }
    );
  }
}
