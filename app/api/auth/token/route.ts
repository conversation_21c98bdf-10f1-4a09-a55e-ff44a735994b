import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateToken, setTokenCookie } from '@/lib/jwt';

export async function POST(req: NextRequest) {
  try {
    const { supabaseToken, user } = await req.json();

    // Validate required fields
    if (!supabaseToken || !user || !user.id || !user.email) {
      return NextResponse.json(
        { message: 'Missing required user information' },
        { status: 400 }
      );
    }

    // Verify the Supabase token
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    );

    const { data: { user: supabaseUser }, error } = await supabase.auth.getUser(supabaseToken);

    if (error || !supabaseUser) {
      console.error('Error verifying Supabase token:', error);
      return NextResponse.json(
        { message: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Verify that the user ID matches
    if (supabaseUser.id !== user.id) {
      return NextResponse.json(
        { message: 'User ID mismatch' },
        { status: 401 }
      );
    }

    // Check if user is an admin in the database
    // This is where you would query your database to determine user roles
    // For now, we'll use a simple check based on email domain or metadata
    let role = user.role || 'user';
    
    // Example: Check if user is an admin based on email domain
    if (user.email.endsWith('@admin.doyoudj.com')) {
      role = 'admin';
    }
    
    // Example: Check if user is an admin based on Supabase metadata
    if (supabaseUser.user_metadata?.is_admin === true) {
      role = 'admin';
    }

    // Generate JWT token
    const token = generateToken({
      id: user.id,
      email: user.email,
      role: role,
    });

    // Set the token as an HTTP-only cookie
    setTokenCookie(token);

    // Return success with user info (but not the token, as it's in the cookie)
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        role: role,
      },
      message: 'Authentication successful',
    });
  } catch (error) {
    console.error('Error generating token:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
