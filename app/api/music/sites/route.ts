import { NextRequest, NextResponse } from 'next/server';
import { withAuth, JWTPayload } from '@/lib/jwt';

// Example music sites data
const musicSites = [
  {
    id: '1',
    name: 'SoundCloud',
    url: 'https://soundcloud.com',
    description: 'Stream and share your sounds',
    apiSupport: true,
  },
  {
    id: '2',
    name: 'Spotify',
    url: 'https://spotify.com',
    description: 'Digital music service with millions of songs',
    apiSupport: true,
  },
  {
    id: '3',
    name: 'Bandcamp',
    url: 'https://bandcamp.com',
    description: 'Online music platform for artists and labels',
    apiSupport: true,
  },
  {
    id: '4',
    name: 'YouTube Music',
    url: 'https://music.youtube.com',
    description: 'Music streaming service from YouTube',
    apiSupport: true,
  },
  {
    id: '5',
    name: 'Apple Music',
    url: 'https://music.apple.com',
    description: 'Music and video streaming service by Apple Inc.',
    apiSupport: true,
  },
];

// Handler for GET requests
async function handleGet(req: NextRequest, user: JWTPayload) {
  // You can use the user object to customize the response
  // For example, you might filter sites based on user preferences
  
  return NextResponse.json({
    sites: musicSites,
    user: {
      id: user.id,
      email: user.email,
      role: user.role,
    },
  });
}

// Export the GET handler with authentication
export const GET = (req: NextRequest) => withAuth(req, handleGet);
