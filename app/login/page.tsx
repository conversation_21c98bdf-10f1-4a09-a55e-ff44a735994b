'use client'

import { useState } from 'react'
import { createClient } from '@supabase/supabase-js'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { CurrentUserAvatar } from '@/components/current-user-avatar'
import type { SupportedStorage } from '@supabase/supabase-js'

// UI Components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

// Helper function to check if localStorage is available
const supportsLocalStorage = () => {
  try {
    if (typeof globalThis.localStorage !== 'undefined') {
      const key = '__supabase_storage_test__'
      globalThis.localStorage.setItem(key, 'test')
      globalThis.localStorage.removeItem(key)
      return true
    }
    return false
  } catch (e) {
    return false
  }
}

// Custom storage adapter
const customStorageAdapter: SupportedStorage = {
  getItem: (key) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage
      return null
    }
    return globalThis.localStorage.getItem(key)
  },
  setItem: (key, value) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage here
      return
    }
    globalThis.localStorage.setItem(key, value)
  },
  removeItem: (key) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage here
      return
    }
    globalThis.localStorage.removeItem(key)
  },
}

export default function Login() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || 'SUPABASE_URL',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'SUPABASE_ANON_KEY',
    {
      auth: {
        storage: customStorageAdapter,
      },
    }
  )

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // First authenticate with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setError(error.message)
        return
      }

      // If successful, get JWT token from our API
      const response = await fetch('/api/auth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supabaseToken: data.session?.access_token,
          user: {
            id: data.user?.id,
            email: data.user?.email,
            // Default to 'user' role, admin roles should be assigned in the database
            role: data.user?.user_metadata?.role || 'user',
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        setError(errorData.message || 'Failed to get authentication token')
        return
      }

      // Redirect to the home page or the page they were trying to access
      const searchParams = new URLSearchParams(window.location.search)
      const redirectTo = searchParams.get('from') || '/'

      router.push(redirectTo)
      router.refresh()
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('Sign in error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOAuthSignIn = async (provider: 'google' | 'github') => {
    setLoading(true)
    setError(null)

    try {
      // Store the current URL to redirect back after OAuth
      const searchParams = new URLSearchParams(window.location.search)
      const redirectTo = searchParams.get('from') || '/'

      // Store the redirect URL in localStorage to use after OAuth callback
      if (supportsLocalStorage()) {
        localStorage.setItem('authRedirectTo', redirectTo)
      }

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          // Pass additional data to be used for JWT creation
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })

      if (error) {
        setError(error.message)
        setLoading(false)
      }
      // No need to set loading to false on success as we're redirecting
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('OAuth sign in error:', error)
      setLoading(false)
    }
  }

  // Add custom CSS for the login page
  const loginStyles = `
    /* Neon turquoise button with 3D glass marble bevel look */
    .neon-turquoise-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.15) 0%, rgba(0, 0, 0, 0.9) 50%, rgba(0, 0, 0, 0.9) 100%);
      color: #00ffff;
      border: 1px solid #00ffff;
      box-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px rgba(0, 255, 255, 0.5);
      text-shadow: 0 0 5px #00ffff, 0 0 10px rgba(0, 255, 255, 0.7);
      border-radius: 4px;
      backdrop-filter: blur(4px);
      font-family: var(--font-tilt-neon), var(--font-orbitron), system-ui, sans-serif;
      letter-spacing: 1px;
    }

    .neon-turquoise-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 40%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
      border-radius: 4px 4px 0 0;
      pointer-events: none;
    }

    .neon-turquoise-button:hover {
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.25) 0%, rgba(0, 0, 0, 0.8) 50%, rgba(0, 0, 0, 0.8) 100%);
      color: #fff;
      border: 1px solid #00ffff;
      box-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4);
      text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px rgba(0, 255, 255, 0.8);
      transform: translateY(-1px);
    }

    .neon-turquoise-button:active {
      transform: translateY(1px);
      box-shadow: 0 0 8px #00ffff, 0 0 16px rgba(0, 255, 255, 0.6);
    }

    /* Neon card effect with glass marble look */
    .neon-card {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 40, 0.8) 100%);
      border: 1px solid #00ffff;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.4), 0 0 20px rgba(0, 255, 255, 0.2), 0 0 30px rgba(0, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 8px;
      overflow: hidden;
      position: relative;
    }

    .neon-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: linear-gradient(to right, transparent, #00ffff, transparent);
      opacity: 0.7;
      filter: blur(2px);
    }

    /* Neon input effect with glass look */
    .neon-input {
      background: rgba(0, 0, 0, 0.6);
      border: 1px solid rgba(0, 255, 255, 0.3);
      color: #00ffff;
      border-radius: 4px;
      transition: all 0.3s ease;
      backdrop-filter: blur(4px);
      font-family: var(--font-tilt-neon), var(--font-orbitron), system-ui, sans-serif;
    }

    .neon-input:focus {
      border-color: #00ffff;
      box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.25), 0 0 10px rgba(0, 255, 255, 0.3);
      background: rgba(0, 0, 0, 0.7);
      color: #fff;
      text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    }

    .neon-input::placeholder {
      color: rgba(0, 255, 255, 0.5);
    }
  `

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 to-black p-4">
      <style dangerouslySetInnerHTML={{ __html: loginStyles }} />
      <div className="w-full max-w-md">
        <Card className="border-0 neon-card backdrop-blur-sm">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-3xl font-bold tracking-tight font-tiltNeon neon-text-cyan" style={{
              textShadow: "0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff, 0 0 20px #00ffff",
              color: "#00ffff",
              letterSpacing: "2px"
            }}>
              Sign In
            </CardTitle>
            <CardDescription className="text-cyan-300 font-tiltNeon" style={{
              textShadow: "0 0 3px #00ffff",
              letterSpacing: "1px"
            }}>
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-cyan-300 font-tiltNeon" style={{
                  textShadow: "0 0 3px #00ffff",
                  letterSpacing: "1px"
                }}>Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="neon-input"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-cyan-300 font-tiltNeon" style={{
                    textShadow: "0 0 3px #00ffff",
                    letterSpacing: "1px"
                  }}>Password</Label>
                  <Link href="/reset-password" className="text-sm text-cyan-400 hover:text-cyan-300 transition-colors font-tiltNeon" style={{
                    textShadow: "0 0 3px #00ffff, 0 0 5px #00ffff",
                    letterSpacing: "1px"
                  }}>
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="neon-input"
                />
              </div>

              {error && (
                <div className="p-3 rounded-md bg-red-500/10 border border-red-500/50 text-red-400 text-sm font-orbitron">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                disabled={loading}
                className="w-full neon-turquoise-button font-orbitron"
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-cyan-800/30"></div>
                </div>
                <div className="relative flex justify-center text-xs">
                  <span className="px-2 bg-black text-cyan-500 font-tiltNeon" style={{
                    textShadow: "0 0 3px #00ffff",
                    letterSpacing: "1px"
                  }}>Or continue with</span>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="neon-turquoise-button font-orbitron"
                  onClick={() => handleOAuthSignIn('google')}
                  disabled={loading}
                >
                  Google
                </Button>
                <Button
                  variant="outline"
                  className="neon-turquoise-button font-orbitron"
                  onClick={() => handleOAuthSignIn('github')}
                  disabled={loading}
                >
                  GitHub
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm text-cyan-500 font-tiltNeon" style={{
              textShadow: "0 0 2px #00ffff",
              letterSpacing: "1px"
            }}>
              Don't have an account?{' '}
              <Link href="/signup" className="text-cyan-400 hover:text-cyan-300 transition-colors" style={{
                textShadow: "0 0 3px #00ffff, 0 0 5px #00ffff",
                letterSpacing: "1px"
              }}>
                Sign up
              </Link>
            </div>
            <div className="flex justify-center">
              <CurrentUserAvatar />
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
