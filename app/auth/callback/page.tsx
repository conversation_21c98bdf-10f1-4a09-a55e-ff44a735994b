'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@supabase/supabase-js'
import type { SupportedStorage } from '@supabase/supabase-js'

// Helper function to check if localStorage is available
const supportsLocalStorage = () => {
  try {
    if (typeof globalThis.localStorage !== 'undefined') {
      const key = '__supabase_storage_test__'
      globalThis.localStorage.setItem(key, 'test')
      globalThis.localStorage.removeItem(key)
      return true
    }
    return false
  } catch (e) {
    return false
  }
}

// Custom storage adapter
const customStorageAdapter: SupportedStorage = {
  getItem: (key) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage
      return null
    }
    return globalThis.localStorage.getItem(key)
  },
  setItem: (key, value) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage here
      return
    }
    globalThis.localStorage.setItem(key, value)
  },
  removeItem: (key) => {
    if (!supportsLocalStorage()) {
      // Configure alternate storage here
      return
    }
    globalThis.localStorage.removeItem(key)
  },
}

export default function AuthCallback() {
  const router = useRouter()

  useEffect(() => {
    const handleAuthCallback = async () => {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || 'SUPABASE_URL',
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'SUPABASE_ANON_KEY',
        {
          auth: {
            storage: customStorageAdapter,
          },
        }
      )

      // Get the code and state from the URL
      const hashParams = new URLSearchParams(window.location.hash.substring(1))
      const queryParams = new URLSearchParams(window.location.search)

      // Try to exchange the code for a session
      try {
        // Process the OAuth callback
        const { data, error } = await supabase.auth.exchangeCodeForSession(
          queryParams.get('code') || hashParams.get('code') || ''
        )

        if (error) {
          console.error('Error exchanging code for session:', error)
          router.push('/login?error=Authentication%20failed')
          return
        }

        // Get our custom JWT token
        if (data?.session) {
          try {
            const response = await fetch('/api/auth/token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                supabaseToken: data.session.access_token,
                user: {
                  id: data.user?.id,
                  email: data.user?.email,
                  // Default to 'user' role, admin roles should be assigned in the database
                  role: data.user?.user_metadata?.role || 'user',
                }
              }),
            })

            if (!response.ok) {
              const errorData = await response.json()
              console.error('Error getting JWT token:', errorData)
              router.push('/login?error=Authentication%20failed')
              return
            }
          } catch (tokenError) {
            console.error('Error fetching JWT token:', tokenError)
            router.push('/login?error=Authentication%20failed')
            return
          }
        }

        // Get redirect URL from localStorage if available
        let redirectTo = '/'
        if (supportsLocalStorage() && localStorage.getItem('authRedirectTo')) {
          redirectTo = localStorage.getItem('authRedirectTo') || '/'
          localStorage.removeItem('authRedirectTo')
        }

        // Redirect to the appropriate page
        router.push(redirectTo)
      } catch (error) {
        console.error('Unexpected error during authentication:', error)
        router.push('/login?error=Authentication%20failed')
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 to-black p-4">
      <div className="w-full max-w-md text-center">
        <div className="bg-black/80 backdrop-blur-sm shadow-2xl shadow-purple-500/20 p-8 rounded-lg border-0">
          <h1 className="text-3xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-teal-400 to-cyan-300 drop-shadow-[0_0_10px_rgba(45,212,191,0.8)] mb-4">
            Authenticating...
          </h1>
          <div className="flex justify-center">
            <div className="w-12 h-12 rounded-full border-4 border-teal-400 border-t-transparent animate-spin"></div>
          </div>
          <p className="mt-4 text-zinc-400">Please wait while we complete your sign in.</p>
        </div>
      </div>
    </div>
  )
}
