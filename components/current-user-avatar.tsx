'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useRouter } from 'next/navigation'

export function CurrentUserAvatar() {
  const { user, isLoading, signOut } = useAuth()
  const router = useRouter()

  if (isLoading) {
    return <Skeleton className="h-10 w-10 rounded-full" />
  }

  if (!user) {
    return (
      <Link href="/login">
        <Button
          variant="outline"
          className="bg-zinc-900 border-zinc-800 text-teal-400 hover:bg-zinc-800 hover:text-teal-300 border-2 border-teal-500/50 shadow-[0_0_10px_rgba(45,212,191,0.3)] hover:shadow-[0_0_15px_rgba(45,212,191,0.5)] transition-all"
        >
          Sign In
        </Button>
      </Link>
    )
  }

  // Get user initials for the avatar fallback
  const getInitials = () => {
    if (!user.email) return '?'
    return user.email.substring(0, 2).toUpperCase()
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="h-10 w-10 cursor-pointer border-2 border-teal-500/50 shadow-[0_0_10px_rgba(45,212,191,0.3)] hover:shadow-[0_0_15px_rgba(45,212,191,0.5)] transition-all">
          <AvatarImage
            src={user.user_metadata?.avatar_url}
            alt={user.email || 'User avatar'}
          />
          <AvatarFallback className="bg-gradient-to-br from-teal-500 to-cyan-400 text-black font-medium">
            {getInitials()}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-sm border-zinc-800 text-zinc-100 shadow-xl shadow-purple-500/20">
        <DropdownMenuLabel className="text-teal-400">
          {user.email}
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-zinc-800" />
        <DropdownMenuItem
          className="cursor-pointer hover:bg-zinc-800 focus:bg-zinc-800"
          onClick={() => router.push('/profile')}
        >
          Profile
        </DropdownMenuItem>
        <DropdownMenuItem
          className="cursor-pointer hover:bg-zinc-800 focus:bg-zinc-800"
          onClick={() => router.push('/settings')}
        >
          Settings
        </DropdownMenuItem>
        <DropdownMenuSeparator className="bg-zinc-800" />
        <DropdownMenuItem
          className="cursor-pointer text-red-400 hover:bg-red-500/10 focus:bg-red-500/10"
          onClick={signOut}
        >
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
