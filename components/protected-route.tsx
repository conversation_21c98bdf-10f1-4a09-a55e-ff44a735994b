'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string | string[]
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { isLoading, isAuthenticated, user, hasRole } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login if not authenticated
      router.push(`/login?from=${encodeURIComponent(window.location.pathname)}`)
    } else if (!isLoading && isAuthenticated && requiredRole && !hasRole(requiredRole)) {
      // Redirect to home if authenticated but doesn't have required role
      router.push('/')
    }
  }, [isLoading, isAuthenticated, router, requiredRole, hasRole])

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 to-black p-4">
        <div className="w-full max-w-md text-center">
          <div className="bg-black/80 backdrop-blur-sm shadow-2xl shadow-purple-500/20 p-8 rounded-lg border-0">
            <h1 className="text-3xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-teal-400 to-cyan-300 drop-shadow-[0_0_10px_rgba(45,212,191,0.8)] mb-4">
              Loading...
            </h1>
            <div className="flex justify-center">
              <div className="w-12 h-12 rounded-full border-4 border-teal-400 border-t-transparent animate-spin"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // If not authenticated or doesn't have required role, don't render children
  if (!isAuthenticated || (requiredRole && !hasRole(requiredRole))) {
    return null
  }

  // Render children if authenticated and has required role
  return <>{children}</>
}
