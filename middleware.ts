import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyToken } from './lib/jwt'

export function middleware(request: NextRequest) {
  // Public paths that don't require authentication
  const publicPaths = [
    '/',
    '/login',
    '/signup',
    '/auth/callback',
    '/reset-password',
    '/api/auth',
  ]

  // Check if the path is public
  const path = request.nextUrl.pathname
  if (publicPaths.some(publicPath => 
    path === publicPath || 
    path.startsWith(`${publicPath}/`) ||
    path.startsWith('/api/auth/') ||
    path.startsWith('/_next/')
  )) {
    return NextResponse.next()
  }

  // Allow all requests to admin routes during development
  if (process.env.NODE_ENV === 'development' && path.startsWith('/admin')) {
    return NextResponse.next()
  }

  // Check for token in cookies or Authorization header
  const token = request.cookies.get('auth_token')?.value
  const authHeader = request.headers.get('authorization')
  const headerToken = authHeader && authHeader.startsWith('Bearer ') 
    ? authHeader.substring(7) 
    : null

  // Verify the token
  const user = verifyToken(token || headerToken || '')

  // If token is invalid or missing, redirect to login
  if (!user) {
    const url = new URL('/login', request.url)
    url.searchParams.set('from', request.nextUrl.pathname)
    return NextResponse.redirect(url)
  }

  // For API routes that require specific roles
  if (path.startsWith('/api/admin') && user.role !== 'admin') {
    return NextResponse.json({ message: 'Forbidden' }, { status: 403 })
  }

  // For music site routes, ensure they have appropriate access
  if (path.startsWith('/player') || path.startsWith('/playlists')) {
    // You can add specific role checks here if needed
    if (!user.id) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // For admin routes, ensure they have admin role
  if (path.startsWith('/admin') && user.role !== 'admin') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
