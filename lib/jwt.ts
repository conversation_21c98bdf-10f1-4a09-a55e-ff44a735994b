import jwt from 'jsonwebtoken';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// JWT Secret should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-fallback-secret-key-for-development';
const JWT_EXPIRY = process.env.JWT_EXPIRY || '1h';

export interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

/**
 * Generate a JWT token for a user
 */
export function generateToken(user: { id: string; email: string; role: string }): string {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role,
    },
    JWT_SECRET,
    {
      expiresIn: JWT_EXPIRY,
    }
  );
}

/**
 * Verify a JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    console.error('JWT verification error:', error);
    return null;
  }
}

/**
 * Set JWT token in cookies
 */
export function setTokenCookie(token: string) {
  cookies().set({
    name: 'auth_token',
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 60 * 60, // 1 hour in seconds
  });
}

/**
 * Get JWT token from cookies or authorization header
 */
export function getTokenFromRequest(req: NextRequest): string | null {
  // Try to get from cookies first
  const token = req.cookies.get('auth_token')?.value;
  if (token) return token;
  
  // Try to get from Authorization header
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
}

/**
 * Middleware to verify JWT and attach user to request
 */
export async function withAuth(
  req: NextRequest,
  handler: (req: NextRequest, user: JWTPayload) => Promise<NextResponse>
): Promise<NextResponse> {
  const token = getTokenFromRequest(req);
  
  if (!token) {
    return NextResponse.json({ message: 'Not authorized' }, { status: 401 });
  }
  
  const user = verifyToken(token);
  if (!user) {
    return NextResponse.json({ message: 'Invalid token' }, { status: 403 });
  }
  
  return handler(req, user);
}

/**
 * Check if user has required role
 */
export function hasRole(user: JWTPayload, requiredRole: string | string[]): boolean {
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role);
  }
  return user.role === requiredRole;
}
