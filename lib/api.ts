/**
 * Utility function to make authenticated API requests
 */
export async function fetchWithAuth<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // Make the request with credentials to include cookies
    const response = await fetch(url, {
      ...options,
      credentials: 'include', // Include cookies in the request
      headers: {
        ...options.headers,
        'Content-Type': 'application/json',
      },
    });

    // Handle non-OK responses
    if (!response.ok) {
      // If unauthorized, you might want to redirect to login
      if (response.status === 401) {
        if (typeof window !== 'undefined') {
          window.location.href = `/login?from=${encodeURIComponent(window.location.pathname)}`;
        }
        throw new Error('Unauthorized');
      }

      // For other errors, try to parse the error message
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }

    // Parse and return the response data
    return await response.json();
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Utility function to make authenticated GET requests
 */
export function get<T = any>(url: string, options: RequestInit = {}): Promise<T> {
  return fetchWithAuth<T>(url, { ...options, method: 'GET' });
}

/**
 * Utility function to make authenticated POST requests
 */
export function post<T = any>(url: string, data: any, options: RequestInit = {}): Promise<T> {
  return fetchWithAuth<T>(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * Utility function to make authenticated PUT requests
 */
export function put<T = any>(url: string, data: any, options: RequestInit = {}): Promise<T> {
  return fetchWithAuth<T>(url, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * Utility function to make authenticated DELETE requests
 */
export function del<T = any>(url: string, options: RequestInit = {}): Promise<T> {
  return fetchWithAuth<T>(url, { ...options, method: 'DELETE' });
}
